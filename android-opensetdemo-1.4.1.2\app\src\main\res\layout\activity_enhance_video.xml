<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/include_title" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:id="@+id/fl_video"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <RelativeLayout
            android:id="@+id/rl_down"
            android:layout_width="65dp"
            android:layout_height="65dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="40dp"
            android:background="@drawable/bg_enhance_video_rl_down">


            <RelativeLayout
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_centerInParent="true"
                android:background="@drawable/bg_enhance_video_down">

                <RelativeLayout
                    android:layout_width="26.5dp"
                    android:layout_height="31.5dp"
                    android:layout_centerInParent="true">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@mipmap/icon_packet" />

                    <TextView
                        android:id="@+id/tv_desc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentBottom="true"
                        android:layout_centerHorizontal="true"
                        android:text="00"
                        android:textColor="@android:color/white"
                        android:textSize="11sp" />

                </RelativeLayout>
            </RelativeLayout>


        </RelativeLayout>
    </RelativeLayout>
</LinearLayout>