<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_dialog"
    android:orientation="vertical"
    android:padding="20dp">

    <ImageView
        android:layout_width="133.5dp"
        android:layout_height="113.5dp"
        android:layout_gravity="center"
        android:src="@mipmap/newversionicon" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="5dp"
        android:text="发现新版本"
        android:textColor="@android:color/black"
        android:textStyle="bold"
        android:textSize="16sp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="更新内容："
        android:layout_marginTop="3dp"
        android:textColor="@android:color/black"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="1、dfdfsadfsadffa\n2、sdfasdfadfasdf"
        android:textColor="#666666"
        android:textSize="14sp" />

    <Button
        android:id="@+id/btn_update"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:text="立即更新"
        android:layout_marginTop="14dp"
        android:layout_marginLeft="5dp"
        android:textColor="@android:color/white"
        android:textSize="16sp"
        android:background="@drawable/bg_update"
        android:layout_marginRight="5dp"/>

</LinearLayout>