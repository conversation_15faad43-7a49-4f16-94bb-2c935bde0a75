{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-mergeDebugResources-30:/values-vi/values-vi.xml", "map": [{"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-vi\\strings.xml", "from": {"startLines": "97,96,93,92,110,99,98,1,2,60,80,59,81,113,56,154,107,47,106,140,141,139,137,136,144,143,134,133,148,69,71,79,46,45,6,54,55,68,67,44,11,82,63,61,64,62,9,32,17,28,29,25,21,31,19,20,26,23,30,15,16,27,22,18,24,112,83,8,7,86,157,158,75,162,78,161,159,160,76,77,48,151,39,37,40,38,36,35,41,153,152,88,111,100,95,94,101,89,105,104,103,102,70,72,87,53,5,51,12,10,129,130,128,117,118,116,147,121,122,120,125,126,124,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7653,7581,7319,7255,8670,7837,7760,16,72,4838,6450,4767,6545,8858,4647,14573,8607,4178,8551,13308,13625,13197,12706,12591,13840,13740,12114,12008,14170,5523,5644,6377,4120,4045,212,4555,4601,5329,5266,3935,454,6633,5076,4905,5155,4990,356,1782,965,1570,1623,1410,1178,1728,1080,1130,1460,1293,1675,837,904,1517,1243,1018,1351,8802,6717,308,261,6831,14682,14783,5923,15153,6318,15055,14884,14973,5980,6149,4231,14378,3675,2023,3731,3584,1950,1863,3796,14495,14419,7018,8732,7949,7482,7402,8072,7079,8412,8342,8264,8200,5574,5804,6949,4506,160,4377,724,402,11503,11870,11371,9102,9777,8950,13963,10061,10517,9918,10800,11220,10646,4450", "endColumns": "106,71,82,63,61,111,76,55,56,66,94,70,87,57,93,63,38,52,55,316,113,110,489,114,89,99,475,105,171,50,159,72,57,74,48,45,45,193,62,109,269,83,78,84,78,85,45,47,52,52,51,49,64,53,49,47,56,57,52,66,60,52,49,61,58,55,90,47,46,117,100,100,56,137,58,97,88,81,168,168,111,40,55,1560,64,90,72,86,109,77,75,60,69,122,98,79,127,137,138,69,77,63,69,90,68,48,51,72,80,51,366,106,131,674,139,151,206,455,127,142,419,149,153,55", "endOffsets": "7755,7648,7397,7314,8727,7944,7832,67,124,4900,6540,4833,6628,8911,4736,14632,8641,4226,8602,13620,13734,13303,13191,12701,13925,13835,12585,12109,14337,5569,5799,6445,4173,4115,256,4596,4642,5518,5324,4040,719,6712,5150,4985,5229,5071,397,1825,1013,1618,1670,1455,1238,1777,1125,1173,1512,1346,1723,899,960,1565,1288,1075,1405,8853,6803,351,303,6944,14778,14879,5975,15286,6372,15148,14968,15050,6144,6313,4338,14414,3726,3579,3791,3670,2018,1945,3901,14568,14490,7074,8797,8067,7576,7477,8195,7212,8546,8407,8337,8259,5639,5890,7013,4550,207,4445,800,449,11865,11972,11498,9772,9912,9097,14165,10512,10640,10056,11215,11365,10795,4501"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,47,48,49,50,51,54,55,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,74,75,76,78,79,80,81,82,83,84,85,86,87,88,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,200,201,202,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3008,3115,3187,3270,3334,3396,3508,3585,3641,4092,4159,4254,4325,4413,4684,4778,4922,4961,5014,5070,5387,5501,5612,6102,6217,6307,6407,6883,6989,7161,7212,7372,7508,7566,7641,7784,7830,7876,8070,8133,8243,8513,8597,8676,8761,8840,9059,9105,9153,9206,9259,9311,9361,9426,9480,9530,9578,9635,9693,9746,9813,9874,9927,9977,10039,10098,10154,14002,14050,14097,14215,14316,14417,14474,14612,14671,14769,14858,14940,15109,15278,15390,15509,15565,17126,17191,17282,17355,17442,17552,17630,17706,17767,17837,17960,18059,18139,18267,18405,18544,18614,18692,18756,18826,19002,19071,19120,19273,19346,19427,19479,19846,19953,20085,20760,20900,21052,21259,21715,21843,21986,22406,22556,22710", "endColumns": "106,71,82,63,61,111,76,55,56,66,94,70,87,57,93,63,38,52,55,316,113,110,489,114,89,99,475,105,171,50,159,72,57,74,48,45,45,193,62,109,269,83,78,84,78,85,45,47,52,52,51,49,64,53,49,47,56,57,52,66,60,52,49,61,58,55,90,47,46,117,100,100,56,137,58,97,88,81,168,168,111,40,55,1560,64,90,72,86,109,77,75,60,69,122,98,79,127,137,138,69,77,63,69,90,68,48,51,72,80,51,366,106,131,674,139,151,206,455,127,142,419,149,153,55", "endOffsets": "3110,3182,3265,3329,3391,3503,3580,3636,3693,4154,4249,4320,4408,4466,4773,4837,4956,5009,5065,5382,5496,5607,6097,6212,6302,6402,6878,6984,7156,7207,7367,7440,7561,7636,7685,7825,7871,8065,8128,8238,8508,8592,8671,8756,8835,8921,9100,9148,9201,9254,9306,9356,9421,9475,9525,9573,9630,9688,9741,9808,9869,9922,9972,10034,10093,10149,10240,14045,14092,14210,14311,14412,14469,14607,14666,14764,14853,14935,15104,15273,15385,15426,15560,17121,17186,17277,17350,17437,17547,17625,17701,17762,17832,17955,18054,18134,18262,18400,18539,18609,18687,18751,18821,18912,19066,19115,19167,19341,19422,19474,19841,19948,20080,20755,20895,21047,21254,21710,21838,21981,22401,22551,22705,22761"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5d5fa9c9e36bc912c1cb51114343e1e1\\transformed\\material-1.9.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,421,499,579,659,758,872,952,1015,1109,1183,1242,1328,1390,1451,1509,1573,1634,1688,1805,1862,1922,1976,2051,2178,2262,2340,2470,2554,2632,2723,2774,2825,2891,2959,3035,3116,3195,3270,3343,3419,3508,3585,3676,3770,3844,3914,4007,4056,4122,4207,4293,4355,4419,4482,4581,4686,4784,4889,4944,4999", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,78,76,77,79,79,98,113,79,62,93,73,58,85,61,60,57,63,60,53,116,56,59,53,74,126,83,77,129,83,77,90,50,50,65,67,75,80,78,74,72,75,88,76,90,93,73,69,92,48,65,84,85,61,63,62,98,104,97,104,54,54,77", "endOffsets": "260,339,416,494,574,654,753,867,947,1010,1104,1178,1237,1323,1385,1446,1504,1568,1629,1683,1800,1857,1917,1971,2046,2173,2257,2335,2465,2549,2627,2718,2769,2820,2886,2954,3030,3111,3190,3265,3338,3414,3503,3580,3671,3765,3839,3909,4002,4051,4117,4202,4288,4350,4414,4477,4576,4681,4779,4884,4939,4994,5072"}, "to": {"startLines": "2,42,43,44,45,46,52,53,56,73,77,89,90,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3698,3777,3854,3932,4012,4471,4570,4842,7445,7690,8926,9000,10245,10331,10393,10454,10512,10576,10637,10691,10808,10865,10925,10979,11054,11181,11265,11343,11473,11557,11635,11726,11777,11828,11894,11962,12038,12119,12198,12273,12346,12422,12511,12588,12679,12773,12847,12917,13010,13059,13125,13210,13296,13358,13422,13485,13584,13689,13787,13892,13947,15431", "endLines": "5,42,43,44,45,46,52,53,56,73,77,89,90,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,176", "endColumns": "12,78,76,77,79,79,98,113,79,62,93,73,58,85,61,60,57,63,60,53,116,56,59,53,74,126,83,77,129,83,77,90,50,50,65,67,75,80,78,74,72,75,88,76,90,93,73,69,92,48,65,84,85,61,63,62,98,104,97,104,54,54,77", "endOffsets": "310,3772,3849,3927,4007,4087,4565,4679,4917,7503,7779,8995,9054,10326,10388,10449,10507,10571,10632,10686,10803,10860,10920,10974,11049,11176,11260,11338,11468,11552,11630,11721,11772,11823,11889,11957,12033,12114,12193,12268,12341,12417,12506,12583,12674,12768,12842,12912,13005,13054,13120,13205,13291,13353,13417,13480,13579,13684,13782,13887,13942,13997,15504"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bde3453fac62cb9a4dc37198ed238829\\transformed\\appcompat-1.6.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,2798", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,422,524,633,717,820,939,1017,1093,1184,1277,1372,1466,1566,1659,1754,1848,1939,2030,2114,2218,2326,2427,2532,2647,2752,2909,18917", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "417,519,628,712,815,934,1012,1088,1179,1272,1367,1461,1561,1654,1749,1843,1934,2025,2109,2213,2321,2422,2527,2642,2747,2904,3003,18997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b7d68e85c64c5740c775d2c6236c08f5\\transformed\\core-1.9.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "203", "startColumns": "4", "startOffsets": "19172", "endColumns": "100", "endOffsets": "19268"}}]}]}