<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#ffffff">

    <FrameLayout
        android:id="@+id/media_view_container"
        android:layout_width="match_parent"
        android:layout_height="300dp" />

    <RelativeLayout
        android:layout_alignParentBottom="true"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="#ffffff"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/icon_image_view"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_marginBottom="5dp" />


        <TextView
            android:id="@+id/title_text_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/icon_image_view"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="5dp"
            android:layout_toStartOf="@id/cta_button"
            android:layout_toEndOf="@id/icon_image_view" />

        <TextView
            android:id="@+id/body_text_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/title_text_view"
            android:layout_marginStart="5dp"
            android:layout_marginTop="5dp"
            android:layout_toStartOf="@id/cta_button"
            android:layout_toEndOf="@id/icon_image_view"
            android:ellipsize="marquee"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:marqueeRepeatLimit="marquee_forever"
            android:singleLine="true" />

        <Button
            android:id="@+id/cta_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@+id/icon_image_view"
            android:layout_alignBottom="@+id/icon_image_view"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="15dp"
            android:layout_marginRight="15dp" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/ad_options_view"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="15dp"
        android:orientation="horizontal" />
</RelativeLayout>