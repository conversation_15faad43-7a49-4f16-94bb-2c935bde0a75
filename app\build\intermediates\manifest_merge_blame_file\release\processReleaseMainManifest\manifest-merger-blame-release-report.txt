1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.ainative.mountainsurvival"
4    android:versionCode="3"
5    android:versionName="1.2.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- 必要权限：应用基本功能所需 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:6:5-66
12-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:7:5-78
13-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- 广告SDK必需权限 -->
16    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
16-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:10:5-78
16-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:10:22-76
17
18    <!-- 广告优化权限：可选，用于提升广告效果 -->
19    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
19-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:13:5-75
19-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:13:22-73
20
21    <permission
21-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
22        android:name="com.ainative.mountainsurvival.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
22-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
23        android:protectionLevel="signature" />
23-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
24
25    <uses-permission android:name="com.ainative.mountainsurvival.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- 以下权限已移除，因为单机游戏不需要这些敏感权限 -->
25-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
25-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
26    <!-- <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/> -->
27    <!-- <uses-permission android:name="android.permission.READ_PHONE_STATE"/> -->
28    <!-- <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/> -->
29    <!-- <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/> -->
30    <!-- <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/> -->
31    <application
31-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:22:5-83:19
32        android:name="com.ainative.mountainsurvival.MountainSurvivalApplication"
32-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:23:9-52
33        android:allowBackup="true"
33-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:24:9-35
34        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
34-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
35        android:dataExtractionRules="@xml/data_extraction_rules"
35-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:25:9-65
36        android:extractNativeLibs="false"
37        android:fullBackupContent="@xml/backup_rules"
37-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:26:9-54
38        android:icon="@mipmap/ic_launcher"
38-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:27:9-43
39        android:label="@string/app_name"
39-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:28:9-41
40        android:networkSecurityConfig="@xml/network_security_config"
40-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:32:9-69
41        android:roundIcon="@mipmap/ic_launcher_round"
41-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:29:9-54
42        android:supportsRtl="true"
42-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:30:9-35
43        android:theme="@style/Theme.MountainSurvival" >
43-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:31:9-54
44
45        <!-- 开始界面 - 启动Activity -->
46        <activity
46-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:36:9-46:20
47            android:name="com.ainative.mountainsurvival.StartActivity"
47-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:37:13-42
48            android:exported="true"
48-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:38:13-36
49            android:label="@string/app_name"
49-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:39:13-45
50            android:theme="@style/Theme.MountainSurvival" >
50-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:40:13-58
51            <intent-filter>
51-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:41:13-45:29
52                <action android:name="android.intent.action.MAIN" />
52-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:42:17-69
52-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:42:25-66
53
54                <category android:name="android.intent.category.LAUNCHER" />
54-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:44:17-77
54-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:44:27-74
55            </intent-filter>
56        </activity>
57
58        <!-- 隐私政策界面 -->
59        <activity
59-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:49:9-53:61
60            android:name="com.ainative.mountainsurvival.PrivacyPolicyActivity"
60-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:50:13-50
61            android:exported="false"
61-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:51:13-37
62            android:label="@string/app_name"
62-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:52:13-45
63            android:theme="@style/Theme.MountainSurvival" />
63-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:53:13-58
64
65        <!-- 主游戏界面 -->
66        <activity
66-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:56:9-60:61
67            android:name="com.ainative.mountainsurvival.MainActivity"
67-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:57:13-41
68            android:exported="false"
68-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:58:13-37
69            android:label="@string/app_name"
69-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:59:13-45
70            android:theme="@style/Theme.MountainSurvival" />
70-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:60:13-58
71
72        <!-- AndroidX FileProvider 兼容广告SDK -->
73        <provider
74            android:name="androidx.core.content.FileProvider"
74-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:64:13-62
75            android:authorities="com.ainative.mountainsurvival.fileprovider"
75-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:65:13-64
76            android:exported="false"
76-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:66:13-37
77            android:grantUriPermissions="true" >
77-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:67:13-47
78            <meta-data
78-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:68:13-70:54
79                android:name="android.support.FILE_PROVIDER_PATHS"
79-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:69:17-67
80                android:resource="@xml/file_paths" />
80-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:70:17-51
81        </provider>
82
83        <!-- 广告SDK专用的FileProvider -->
84        <provider
85            android:name="androidx.core.content.FileProvider"
85-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:64:13-62
86            android:authorities="com.ainative.mountainsurvival.osetfileprovider"
86-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:65:13-64
87            android:exported="false"
87-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:66:13-37
88            android:grantUriPermissions="true" >
88-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:67:13-47
89            <meta-data
89-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:68:13-70:54
90                android:name="android.support.FILE_PROVIDER_PATHS"
90-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:69:17-67
91                android:resource="@xml/oset_file_paths" />
91-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:70:17-51
92        </provider>
93        <provider
93-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
94            android:name="androidx.startup.InitializationProvider"
94-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
95            android:authorities="com.ainative.mountainsurvival.androidx-startup"
95-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
96            android:exported="false" >
96-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
97            <meta-data
97-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
98                android:name="androidx.emoji2.text.EmojiCompatInitializer"
98-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
99                android:value="androidx.startup" />
99-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
100            <meta-data
100-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97801c6c507d8e8a69f1aae422f6dcba\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
101                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
101-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97801c6c507d8e8a69f1aae422f6dcba\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
102                android:value="androidx.startup" />
102-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97801c6c507d8e8a69f1aae422f6dcba\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
103        </provider> <!-- Just used for obtain context -->
104        <provider
104-->[io.github.aliyun-sls:aliyun-log-android-sdk:2.7.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d0163aaba9a980e61cd3d3add9946e\transformed\jetified-aliyun-log-android-sdk-2.7.13\AndroidManifest.xml:17:9-20:40
105            android:name="com.aliyun.sls.android.producer.provider.SLSContentProvider"
105-->[io.github.aliyun-sls:aliyun-log-android-sdk:2.7.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d0163aaba9a980e61cd3d3add9946e\transformed\jetified-aliyun-log-android-sdk-2.7.13\AndroidManifest.xml:18:13-87
106            android:authorities="com.ainative.mountainsurvival.sls_provider"
106-->[io.github.aliyun-sls:aliyun-log-android-sdk:2.7.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d0163aaba9a980e61cd3d3add9946e\transformed\jetified-aliyun-log-android-sdk-2.7.13\AndroidManifest.xml:19:13-64
107            android:exported="false" />
107-->[io.github.aliyun-sls:aliyun-log-android-sdk:2.7.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d0163aaba9a980e61cd3d3add9946e\transformed\jetified-aliyun-log-android-sdk-2.7.13\AndroidManifest.xml:20:13-37
108    </application>
109
110</manifest>
