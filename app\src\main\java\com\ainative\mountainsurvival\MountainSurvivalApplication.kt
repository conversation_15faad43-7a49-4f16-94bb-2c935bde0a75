package com.ainative.mountainsurvival

import android.app.Application
import android.os.Build
import android.util.Log
import android.webkit.WebView
import androidx.multidex.MultiDexApplication
import com.kc.openset.config.OSETSDK
// 注意：国外SDK可能没有OSETCustomController类
import com.kc.openset.listener.OSETInitListener

/**
 * 应用程序Application类
 * 负责初始化广告SDK
 */
class MountainSurvivalApplication : MultiDexApplication() {

    companion object {
        private const val TAG = "MountainSurvivalApp"

        // 神蓍广告SDK国外版本测试配置
        const val APP_KEY = "E6097975B89E83D6"
        const val REWARD_VIDEO_AD_ID = "09A177D681D6FB81241C3DCE963DCB46"
    }

    // 广告SDK初始化状态标志
    private var isAdSDKInitialized = false

    override fun attachBaseContext(base: android.content.Context?) {
        // 在attachBaseContext中初始化语言设置，确保在所有Activity创建前应用语言
        base?.let { context ->
            LanguageManager.initializeLanguage(context)
        }

        super.attachBaseContext(base)

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            Log.e(TAG, "进程名：${getProcessName()}")
            // 安卓9.0后不允许多进程使用同一个数据目录
            try {
                WebView.setDataDirectorySuffix(getProcessName())
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "Application onCreate")

        // 确保语言设置已正确初始化
        LanguageManager.initializeLanguage(this)
        Log.d(TAG, "当前应用语言: ${LanguageManager.getCurrentLanguage(this)}")

        // 注意：不在这里初始化广告SDK，必须等用户同意隐私政策后再初始化
        // 这是为了确保隐私合规，避免在用户同意前获取任何隐私信息
        Log.d(TAG, "等待用户同意隐私政策后再初始化广告SDK")
    }

    /**
     * 初始化广告SDK
     * 只有在用户同意隐私政策后才能调用此方法
     */
    fun initAdSDK() {
        if (isAdSDKInitialized) {
            Log.d(TAG, "广告SDK已经初始化，跳过重复初始化")
            return
        }

        Log.d(TAG, "开始初始化广告SDK")

        try {
            // 记录隐私合规信息
            PrivacyComplianceManager.logSDKNote()

            // 检查SDK是否已经初始化
            if (OSETSDK.getInstance().isInit()) {
                Log.d(TAG, "广告SDK已经初始化")
                isAdSDKInitialized = true
                return
            }

            // 国外SDK初始化（参考官方示例）
            OSETSDK.getInstance().init(this, APP_KEY, object : OSETInitListener {
                override fun onSuccess() {
                    Log.d(TAG, "广告SDK初始化成功")
                    Log.d(TAG, "使用APP_KEY: $APP_KEY")
                    Log.d(TAG, "激励视频广告位ID: $REWARD_VIDEO_AD_ID")
                    isAdSDKInitialized = true

                    // 初始化成功后预加载广告
                    try {
                        AdManager.preloadRewardedAd(this@MountainSurvivalApplication)
                        Log.d(TAG, "开始预加载激励视频广告")
                    } catch (e: Exception) {
                        Log.e(TAG, "预加载广告异常", e)
                    }
                }

                override fun onError(error: String?) {
                    Log.e(TAG, "广告SDK初始化失败: $error")
                    Log.e(TAG, "使用APP_KEY: $APP_KEY")
                    isAdSDKInitialized = false
                }
            })
        } catch (e: Exception) {
            Log.e(TAG, "广告SDK初始化异常", e)
            isAdSDKInitialized = false
        }
    }

    /**
     * 检查广告SDK是否已初始化
     */
    fun isAdSDKInitialized(): Boolean {
        return isAdSDKInitialized
    }
}
