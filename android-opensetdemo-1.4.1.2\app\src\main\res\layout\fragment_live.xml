<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:scrollbars="none">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.test.opensetdemo.CustomImageView
            android:id="@+id/iv_banner"
            android:layout_width="match_parent"
            android:layout_height="130dp"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="15dp"
            android:layout_marginBottom="10dp"
            android:src="@mipmap/live_banner"
            app:scale_h="130px"
            app:scale_w="330px" />

        <com.test.opensetdemo.MyGridView
            android:id="@+id/gv_live"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="10dp"
            android:numColumns="4"
            android:verticalSpacing="15dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="15dp"
            android:layout_marginRight="15dp"
            android:orientation="horizontal">

            <com.test.opensetdemo.CustomImageView
                android:id="@+id/iv_jp"
                android:layout_width="0dp"
                android:layout_height="75dp"
                android:layout_weight="1"
                android:src="@mipmap/image_live_jp"
                app:scale_h="75dp"
                app:scale_w="158dp" />

            <com.test.opensetdemo.CustomImageView
                android:id="@+id/iv_jd"
                android:layout_width="0dp"
                android:layout_height="75dp"
                android:layout_marginLeft="14dp"
                android:layout_weight="1"
                android:src="@mipmap/image_live_jd"
                app:scale_h="75dp"
                app:scale_w="158dp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="15dp"
            android:orientation="horizontal">

            <com.test.opensetdemo.CustomImageView
                android:id="@+id/iv_mp"
                android:layout_width="0dp"
                android:layout_height="75dp"
                android:layout_weight="1"
                android:src="@mipmap/image_live_mp"
                app:scale_h="75dp"
                app:scale_w="158dp" />

            <com.test.opensetdemo.CustomImageView
                android:id="@+id/iv_wl"
                android:layout_width="0dp"
                android:layout_height="75dp"
                android:layout_marginLeft="14dp"
                android:layout_weight="1"
                android:src="@mipmap/image_live_wl"
                app:scale_h="75dp"
                app:scale_w="158dp" />
        </LinearLayout>
    </LinearLayout>

</ScrollView>