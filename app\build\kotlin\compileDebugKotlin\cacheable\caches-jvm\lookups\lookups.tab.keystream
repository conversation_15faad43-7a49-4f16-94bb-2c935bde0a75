  holo_blue_dark android.R.color  Activity android.app  Application android.app  ActivityMainBinding android.app.Activity  ActivityPrivacyPolicyBinding android.app.Activity  ActivityStartBinding android.app.Activity  	AdManager android.app.Activity  AlertDialog android.app.Activity  Choice android.app.Activity  
ClickableSpan android.app.Activity  Double android.app.Activity  	Exception android.app.Activity  Float android.app.Activity  ForegroundColorSpan android.app.Activity  
GameEngine android.app.Activity  GameEventUtils android.app.Activity  GameLoopTest android.app.Activity  GameManager android.app.Activity  	GameState android.app.Activity  GameUIState android.app.Activity  Int android.app.Activity  Intent android.app.Activity  LanguageManager android.app.Activity  LinkMovementMethod android.app.Activity  Log android.app.Activity  Long android.app.Activity  MODE_PRIVATE android.app.Activity  MainActivity android.app.Activity  MountainSurvivalApplication android.app.Activity  MusicManager android.app.Activity  PRIVACY_POLICY_REQUEST_CODE android.app.Activity  PRIVACY_POLICY_URL android.app.Activity  Pair android.app.Activity  PrivacyAuditLogger android.app.Activity  PrivacyPolicyActivity android.app.Activity  R android.app.Activity  RESULT_CANCELED android.app.Activity  	RESULT_OK android.app.Activity  SpannableString android.app.Activity  Spanned android.app.Activity  String android.app.Activity  System android.app.Activity  TAG android.app.Activity  Triple android.app.Activity  Uri android.app.Activity  View android.app.Activity  android android.app.Activity  any android.app.Activity  application android.app.Activity  applyChoice android.app.Activity  
checkGameOver android.app.Activity  coerceAtMost android.app.Activity  
component1 android.app.Activity  
component2 android.app.Activity  contains android.app.Activity  	emptyList android.app.Activity  emptyMap android.app.Activity  filter android.app.Activity  finish android.app.Activity  forEachIndexed android.app.Activity  generateComplianceReport android.app.Activity  getCurrentLanguage android.app.Activity  getEvent android.app.Activity  getEventMapStatistics android.app.Activity  getGameOverReason android.app.Activity  getLanguageDisplayName android.app.Activity  	getString android.app.Activity  getSupportedLanguages android.app.Activity  giveResourceReward android.app.Activity  indexOf android.app.Activity  indices android.app.Activity  
initialize android.app.Activity  initializeGame android.app.Activity  initializeLanguage android.app.Activity  isChoiceAvailable android.app.Activity  
isInitialized android.app.Activity  isMusicEnabled android.app.Activity  
isNotEmpty android.app.Activity  
isNullOrEmpty android.app.Activity  	isVictory android.app.Activity  java android.app.Activity  joinToString android.app.Activity  layoutInflater android.app.Activity  let android.app.Activity  listOf android.app.Activity  
loadEvents android.app.Activity  map android.app.Activity  mapOf android.app.Activity  
mutableListOf android.app.Activity  mutableMapOf android.app.Activity  onActivityResult android.app.Activity  onCreate android.app.Activity  openPrivacyPolicyUrl android.app.Activity  packageName android.app.Activity  pauseBackgroundMusic android.app.Activity  performNightPhase android.app.Activity  preloadRewardVideo android.app.Activity  recreate android.app.Activity  	resetGame android.app.Activity  resumeBackgroundMusic android.app.Activity  reviveCharacter android.app.Activity  run android.app.Activity  set android.app.Activity  setGameState android.app.Activity  setLanguage android.app.Activity  	setResult android.app.Activity  showAdErrorDialog android.app.Activity  showReviveAd android.app.Activity  showReviveSuccessDialog android.app.Activity  showRewardDialog android.app.Activity  showRewardVideoForResource android.app.Activity  split android.app.Activity  
startActivity android.app.Activity  startBackgroundMusic android.app.Activity  stopBackgroundMusic android.app.Activity  take android.app.Activity  testGameLoop android.app.Activity  to android.app.Activity  toIntOrNull android.app.Activity  toList android.app.Activity  toMutableMap android.app.Activity  toMutableSet android.app.Activity  toTypedArray android.app.Activity  toggleMusic android.app.Activity  trim android.app.Activity  validateEventMap android.app.Activity  
AdCallback android.app.Activity.AdManager  ResourceType android.app.Activity.AdManager  ReviveAdCallback android.app.Activity.AdManager  ChoiceResult  android.app.Activity.GameManager  APP_KEY android.app.Application  	AdManager android.app.Application  Build android.app.Application  	Exception android.app.Application  LanguageManager android.app.Application  Log android.app.Application  OSETInitListener android.app.Application  OSETSDK android.app.Application  PrivacyComplianceManager android.app.Application  REWARD_VIDEO_AD_ID android.app.Application  String android.app.Application  TAG android.app.Application  WebView android.app.Application  getCurrentLanguage android.app.Application  getProcessName android.app.Application  initializeLanguage android.app.Application  isAdSDKInitialized android.app.Application  let android.app.Application  
logSDKNote android.app.Application  onCreate android.app.Application  preloadRewardedAd android.app.Application  show android.app.Dialog  Context android.content  Intent android.content  SharedPreferences android.content  APP_KEY android.content.Context  ActivityMainBinding android.content.Context  ActivityPrivacyPolicyBinding android.content.Context  ActivityStartBinding android.content.Context  	AdManager android.content.Context  AlertDialog android.content.Context  Build android.content.Context  Choice android.content.Context  
ClickableSpan android.content.Context  Double android.content.Context  	Exception android.content.Context  Float android.content.Context  ForegroundColorSpan android.content.Context  
GameEngine android.content.Context  GameEventUtils android.content.Context  GameLoopTest android.content.Context  GameManager android.content.Context  	GameState android.content.Context  GameUIState android.content.Context  Int android.content.Context  Intent android.content.Context  LanguageManager android.content.Context  LinkMovementMethod android.content.Context  Log android.content.Context  Long android.content.Context  MODE_PRIVATE android.content.Context  MainActivity android.content.Context  MountainSurvivalApplication android.content.Context  MusicManager android.content.Context  OSETInitListener android.content.Context  OSETSDK android.content.Context  PRIVACY_POLICY_REQUEST_CODE android.content.Context  PRIVACY_POLICY_URL android.content.Context  Pair android.content.Context  PrivacyAuditLogger android.content.Context  PrivacyComplianceManager android.content.Context  PrivacyPolicyActivity android.content.Context  R android.content.Context  RESULT_CANCELED android.content.Context  	RESULT_OK android.content.Context  REWARD_VIDEO_AD_ID android.content.Context  SpannableString android.content.Context  Spanned android.content.Context  String android.content.Context  System android.content.Context  TAG android.content.Context  Triple android.content.Context  Uri android.content.Context  View android.content.Context  WebView android.content.Context  android android.content.Context  any android.content.Context  applicationContext android.content.Context  applyChoice android.content.Context  assets android.content.Context  
checkGameOver android.content.Context  coerceAtMost android.content.Context  
component1 android.content.Context  
component2 android.content.Context  contains android.content.Context  	emptyList android.content.Context  emptyMap android.content.Context  filter android.content.Context  forEachIndexed android.content.Context  generateComplianceReport android.content.Context  getCurrentLanguage android.content.Context  getEvent android.content.Context  getEventMapStatistics android.content.Context  getGameOverReason android.content.Context  getLanguageDisplayName android.content.Context  getProcessName android.content.Context  getSharedPreferences android.content.Context  	getString android.content.Context  getSupportedLanguages android.content.Context  giveResourceReward android.content.Context  indexOf android.content.Context  indices android.content.Context  
initialize android.content.Context  initializeGame android.content.Context  initializeLanguage android.content.Context  isAdSDKInitialized android.content.Context  isChoiceAvailable android.content.Context  
isInitialized android.content.Context  isMusicEnabled android.content.Context  
isNotEmpty android.content.Context  
isNullOrEmpty android.content.Context  	isVictory android.content.Context  java android.content.Context  joinToString android.content.Context  let android.content.Context  listOf android.content.Context  
loadEvents android.content.Context  
logSDKNote android.content.Context  map android.content.Context  mapOf android.content.Context  
mutableListOf android.content.Context  mutableMapOf android.content.Context  openPrivacyPolicyUrl android.content.Context  packageName android.content.Context  pauseBackgroundMusic android.content.Context  performNightPhase android.content.Context  preloadRewardVideo android.content.Context  preloadRewardedAd android.content.Context  	resetGame android.content.Context  	resources android.content.Context  resumeBackgroundMusic android.content.Context  reviveCharacter android.content.Context  run android.content.Context  set android.content.Context  setGameState android.content.Context  setLanguage android.content.Context  showAdErrorDialog android.content.Context  showReviveAd android.content.Context  showReviveSuccessDialog android.content.Context  showRewardDialog android.content.Context  showRewardVideoForResource android.content.Context  split android.content.Context  startBackgroundMusic android.content.Context  stopBackgroundMusic android.content.Context  take android.content.Context  testGameLoop android.content.Context  to android.content.Context  toIntOrNull android.content.Context  toList android.content.Context  toMutableMap android.content.Context  toMutableSet android.content.Context  toTypedArray android.content.Context  toggleMusic android.content.Context  trim android.content.Context  validateEventMap android.content.Context  
AdCallback !android.content.Context.AdManager  ResourceType !android.content.Context.AdManager  ReviveAdCallback !android.content.Context.AdManager  ChoiceResult #android.content.Context.GameManager  APP_KEY android.content.ContextWrapper  ActivityMainBinding android.content.ContextWrapper  ActivityPrivacyPolicyBinding android.content.ContextWrapper  ActivityStartBinding android.content.ContextWrapper  	AdManager android.content.ContextWrapper  AlertDialog android.content.ContextWrapper  Build android.content.ContextWrapper  Choice android.content.ContextWrapper  
ClickableSpan android.content.ContextWrapper  Double android.content.ContextWrapper  	Exception android.content.ContextWrapper  Float android.content.ContextWrapper  ForegroundColorSpan android.content.ContextWrapper  
GameEngine android.content.ContextWrapper  GameEventUtils android.content.ContextWrapper  GameLoopTest android.content.ContextWrapper  GameManager android.content.ContextWrapper  	GameState android.content.ContextWrapper  GameUIState android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  LanguageManager android.content.ContextWrapper  LinkMovementMethod android.content.ContextWrapper  Log android.content.ContextWrapper  Long android.content.ContextWrapper  MODE_PRIVATE android.content.ContextWrapper  MainActivity android.content.ContextWrapper  MountainSurvivalApplication android.content.ContextWrapper  MusicManager android.content.ContextWrapper  OSETInitListener android.content.ContextWrapper  OSETSDK android.content.ContextWrapper  PRIVACY_POLICY_REQUEST_CODE android.content.ContextWrapper  PRIVACY_POLICY_URL android.content.ContextWrapper  Pair android.content.ContextWrapper  PrivacyAuditLogger android.content.ContextWrapper  PrivacyComplianceManager android.content.ContextWrapper  PrivacyPolicyActivity android.content.ContextWrapper  R android.content.ContextWrapper  RESULT_CANCELED android.content.ContextWrapper  	RESULT_OK android.content.ContextWrapper  REWARD_VIDEO_AD_ID android.content.ContextWrapper  SpannableString android.content.ContextWrapper  Spanned android.content.ContextWrapper  String android.content.ContextWrapper  System android.content.ContextWrapper  TAG android.content.ContextWrapper  Triple android.content.ContextWrapper  Uri android.content.ContextWrapper  View android.content.ContextWrapper  WebView android.content.ContextWrapper  android android.content.ContextWrapper  any android.content.ContextWrapper  applyChoice android.content.ContextWrapper  
checkGameOver android.content.ContextWrapper  coerceAtMost android.content.ContextWrapper  
component1 android.content.ContextWrapper  
component2 android.content.ContextWrapper  contains android.content.ContextWrapper  	emptyList android.content.ContextWrapper  emptyMap android.content.ContextWrapper  filter android.content.ContextWrapper  forEachIndexed android.content.ContextWrapper  generateComplianceReport android.content.ContextWrapper  getCurrentLanguage android.content.ContextWrapper  getEvent android.content.ContextWrapper  getEventMapStatistics android.content.ContextWrapper  getGameOverReason android.content.ContextWrapper  getLanguageDisplayName android.content.ContextWrapper  getProcessName android.content.ContextWrapper  getSharedPreferences android.content.ContextWrapper  	getString android.content.ContextWrapper  getSupportedLanguages android.content.ContextWrapper  giveResourceReward android.content.ContextWrapper  indexOf android.content.ContextWrapper  indices android.content.ContextWrapper  
initialize android.content.ContextWrapper  initializeGame android.content.ContextWrapper  initializeLanguage android.content.ContextWrapper  isAdSDKInitialized android.content.ContextWrapper  isChoiceAvailable android.content.ContextWrapper  
isInitialized android.content.ContextWrapper  isMusicEnabled android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  
isNullOrEmpty android.content.ContextWrapper  	isVictory android.content.ContextWrapper  java android.content.ContextWrapper  joinToString android.content.ContextWrapper  let android.content.ContextWrapper  listOf android.content.ContextWrapper  
loadEvents android.content.ContextWrapper  
logSDKNote android.content.ContextWrapper  map android.content.ContextWrapper  mapOf android.content.ContextWrapper  
mutableListOf android.content.ContextWrapper  mutableMapOf android.content.ContextWrapper  openPrivacyPolicyUrl android.content.ContextWrapper  packageName android.content.ContextWrapper  pauseBackgroundMusic android.content.ContextWrapper  performNightPhase android.content.ContextWrapper  preloadRewardVideo android.content.ContextWrapper  preloadRewardedAd android.content.ContextWrapper  	resetGame android.content.ContextWrapper  resumeBackgroundMusic android.content.ContextWrapper  reviveCharacter android.content.ContextWrapper  run android.content.ContextWrapper  set android.content.ContextWrapper  setGameState android.content.ContextWrapper  setLanguage android.content.ContextWrapper  showAdErrorDialog android.content.ContextWrapper  showReviveAd android.content.ContextWrapper  showReviveSuccessDialog android.content.ContextWrapper  showRewardDialog android.content.ContextWrapper  showRewardVideoForResource android.content.ContextWrapper  split android.content.ContextWrapper  startBackgroundMusic android.content.ContextWrapper  stopBackgroundMusic android.content.ContextWrapper  take android.content.ContextWrapper  testGameLoop android.content.ContextWrapper  to android.content.ContextWrapper  toIntOrNull android.content.ContextWrapper  toList android.content.ContextWrapper  toMutableMap android.content.ContextWrapper  toMutableSet android.content.ContextWrapper  toTypedArray android.content.ContextWrapper  toggleMusic android.content.ContextWrapper  trim android.content.ContextWrapper  validateEventMap android.content.ContextWrapper  
AdCallback (android.content.ContextWrapper.AdManager  ResourceType (android.content.ContextWrapper.AdManager  ReviveAdCallback (android.content.ContextWrapper.AdManager  ChoiceResult *android.content.ContextWrapper.GameManager  OnClickListener android.content.DialogInterface  dismiss android.content.DialogInterface  <SAM-CONSTRUCTOR> /android.content.DialogInterface.OnClickListener  ACTION_VIEW android.content.Intent  edit !android.content.SharedPreferences  
getBoolean !android.content.SharedPreferences  getLong !android.content.SharedPreferences  	getString !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  
putBoolean (android.content.SharedPreferences.Editor  putLong (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  
Configuration android.content.res  open  android.content.res.AssetManager  	setLocale !android.content.res.Configuration  
configuration android.content.res.Resources  displayMetrics android.content.res.Resources  getColor android.content.res.Resources  updateConfiguration android.content.res.Resources  MediaPlayer 
android.media  create android.media.MediaPlayer  	isLooping android.media.MediaPlayer  	isPlaying android.media.MediaPlayer  let android.media.MediaPlayer  pause android.media.MediaPlayer  release android.media.MediaPlayer  start android.media.MediaPlayer  stop android.media.MediaPlayer  Uri android.net  parse android.net.Uri  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  P android.os.Build.VERSION_CODES  SpannableString android.text  Spanned android.text  setSpan android.text.SpannableString  SPAN_EXCLUSIVE_EXCLUSIVE android.text.Spanned  LinkMovementMethod android.text.method  getInstance &android.text.method.LinkMovementMethod  
ClickableSpan android.text.style  ForegroundColorSpan android.text.style  openPrivacyPolicyUrl !android.text.style.CharacterStyle  openPrivacyPolicyUrl  android.text.style.ClickableSpan  Log android.util  d android.util.Log  e android.util.Log  i android.util.Log  w android.util.Log  LayoutInflater android.view  View android.view  ActivityMainBinding  android.view.ContextThemeWrapper  ActivityPrivacyPolicyBinding  android.view.ContextThemeWrapper  ActivityStartBinding  android.view.ContextThemeWrapper  	AdManager  android.view.ContextThemeWrapper  AlertDialog  android.view.ContextThemeWrapper  Choice  android.view.ContextThemeWrapper  
ClickableSpan  android.view.ContextThemeWrapper  Double  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  Float  android.view.ContextThemeWrapper  ForegroundColorSpan  android.view.ContextThemeWrapper  
GameEngine  android.view.ContextThemeWrapper  GameEventUtils  android.view.ContextThemeWrapper  GameLoopTest  android.view.ContextThemeWrapper  GameManager  android.view.ContextThemeWrapper  	GameState  android.view.ContextThemeWrapper  GameUIState  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  LanguageManager  android.view.ContextThemeWrapper  LinkMovementMethod  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  Long  android.view.ContextThemeWrapper  MODE_PRIVATE  android.view.ContextThemeWrapper  MainActivity  android.view.ContextThemeWrapper  MountainSurvivalApplication  android.view.ContextThemeWrapper  MusicManager  android.view.ContextThemeWrapper  PRIVACY_POLICY_REQUEST_CODE  android.view.ContextThemeWrapper  PRIVACY_POLICY_URL  android.view.ContextThemeWrapper  Pair  android.view.ContextThemeWrapper  PrivacyAuditLogger  android.view.ContextThemeWrapper  PrivacyPolicyActivity  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  RESULT_CANCELED  android.view.ContextThemeWrapper  	RESULT_OK  android.view.ContextThemeWrapper  SpannableString  android.view.ContextThemeWrapper  Spanned  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  System  android.view.ContextThemeWrapper  TAG  android.view.ContextThemeWrapper  Triple  android.view.ContextThemeWrapper  Uri  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  any  android.view.ContextThemeWrapper  applyChoice  android.view.ContextThemeWrapper  
checkGameOver  android.view.ContextThemeWrapper  coerceAtMost  android.view.ContextThemeWrapper  
component1  android.view.ContextThemeWrapper  
component2  android.view.ContextThemeWrapper  contains  android.view.ContextThemeWrapper  	emptyList  android.view.ContextThemeWrapper  emptyMap  android.view.ContextThemeWrapper  filter  android.view.ContextThemeWrapper  forEachIndexed  android.view.ContextThemeWrapper  generateComplianceReport  android.view.ContextThemeWrapper  getCurrentLanguage  android.view.ContextThemeWrapper  getEvent  android.view.ContextThemeWrapper  getEventMapStatistics  android.view.ContextThemeWrapper  getGameOverReason  android.view.ContextThemeWrapper  getLanguageDisplayName  android.view.ContextThemeWrapper  	getString  android.view.ContextThemeWrapper  getSupportedLanguages  android.view.ContextThemeWrapper  giveResourceReward  android.view.ContextThemeWrapper  indexOf  android.view.ContextThemeWrapper  indices  android.view.ContextThemeWrapper  
initialize  android.view.ContextThemeWrapper  initializeGame  android.view.ContextThemeWrapper  initializeLanguage  android.view.ContextThemeWrapper  isChoiceAvailable  android.view.ContextThemeWrapper  
isInitialized  android.view.ContextThemeWrapper  isMusicEnabled  android.view.ContextThemeWrapper  
isNotEmpty  android.view.ContextThemeWrapper  
isNullOrEmpty  android.view.ContextThemeWrapper  	isVictory  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  joinToString  android.view.ContextThemeWrapper  let  android.view.ContextThemeWrapper  listOf  android.view.ContextThemeWrapper  
loadEvents  android.view.ContextThemeWrapper  map  android.view.ContextThemeWrapper  mapOf  android.view.ContextThemeWrapper  
mutableListOf  android.view.ContextThemeWrapper  mutableMapOf  android.view.ContextThemeWrapper  openPrivacyPolicyUrl  android.view.ContextThemeWrapper  pauseBackgroundMusic  android.view.ContextThemeWrapper  performNightPhase  android.view.ContextThemeWrapper  preloadRewardVideo  android.view.ContextThemeWrapper  	resetGame  android.view.ContextThemeWrapper  resumeBackgroundMusic  android.view.ContextThemeWrapper  reviveCharacter  android.view.ContextThemeWrapper  run  android.view.ContextThemeWrapper  set  android.view.ContextThemeWrapper  setGameState  android.view.ContextThemeWrapper  setLanguage  android.view.ContextThemeWrapper  showAdErrorDialog  android.view.ContextThemeWrapper  showReviveAd  android.view.ContextThemeWrapper  showReviveSuccessDialog  android.view.ContextThemeWrapper  showRewardDialog  android.view.ContextThemeWrapper  showRewardVideoForResource  android.view.ContextThemeWrapper  split  android.view.ContextThemeWrapper  startBackgroundMusic  android.view.ContextThemeWrapper  stopBackgroundMusic  android.view.ContextThemeWrapper  take  android.view.ContextThemeWrapper  testGameLoop  android.view.ContextThemeWrapper  to  android.view.ContextThemeWrapper  toIntOrNull  android.view.ContextThemeWrapper  toList  android.view.ContextThemeWrapper  toMutableMap  android.view.ContextThemeWrapper  toMutableSet  android.view.ContextThemeWrapper  toTypedArray  android.view.ContextThemeWrapper  toggleMusic  android.view.ContextThemeWrapper  trim  android.view.ContextThemeWrapper  validateEventMap  android.view.ContextThemeWrapper  
AdCallback *android.view.ContextThemeWrapper.AdManager  ResourceType *android.view.ContextThemeWrapper.AdManager  ReviveAdCallback *android.view.ContextThemeWrapper.AdManager  ChoiceResult ,android.view.ContextThemeWrapper.GameManager  GONE android.view.View  OnClickListener android.view.View  VISIBLE android.view.View  hasOnClickListeners android.view.View  	isEnabled android.view.View  setOnClickListener android.view.View  
visibility android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  WebView android.webkit  setDataDirectorySuffix android.webkit.WebView  Button android.widget  FrameLayout android.widget  	ImageView android.widget  LinearLayout android.widget  TextView android.widget  hasOnClickListeners android.widget.Button  	isEnabled android.widget.Button  setOnClickListener android.widget.Button  text android.widget.Button  
visibility android.widget.Button  setImageResource android.widget.ImageView  movementMethod android.widget.TextView  setOnClickListener android.widget.TextView  text android.widget.TextView  ActivityMainBinding #androidx.activity.ComponentActivity  ActivityPrivacyPolicyBinding #androidx.activity.ComponentActivity  ActivityStartBinding #androidx.activity.ComponentActivity  	AdManager #androidx.activity.ComponentActivity  AlertDialog #androidx.activity.ComponentActivity  Choice #androidx.activity.ComponentActivity  
ClickableSpan #androidx.activity.ComponentActivity  Double #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  Float #androidx.activity.ComponentActivity  ForegroundColorSpan #androidx.activity.ComponentActivity  
GameEngine #androidx.activity.ComponentActivity  GameEventUtils #androidx.activity.ComponentActivity  GameLoopTest #androidx.activity.ComponentActivity  GameManager #androidx.activity.ComponentActivity  	GameState #androidx.activity.ComponentActivity  GameUIState #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  LanguageManager #androidx.activity.ComponentActivity  LinkMovementMethod #androidx.activity.ComponentActivity  Log #androidx.activity.ComponentActivity  Long #androidx.activity.ComponentActivity  MODE_PRIVATE #androidx.activity.ComponentActivity  MainActivity #androidx.activity.ComponentActivity  MountainSurvivalApplication #androidx.activity.ComponentActivity  MusicManager #androidx.activity.ComponentActivity  PRIVACY_POLICY_REQUEST_CODE #androidx.activity.ComponentActivity  PRIVACY_POLICY_URL #androidx.activity.ComponentActivity  Pair #androidx.activity.ComponentActivity  PrivacyAuditLogger #androidx.activity.ComponentActivity  PrivacyPolicyActivity #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  RESULT_CANCELED #androidx.activity.ComponentActivity  	RESULT_OK #androidx.activity.ComponentActivity  SpannableString #androidx.activity.ComponentActivity  Spanned #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  System #androidx.activity.ComponentActivity  TAG #androidx.activity.ComponentActivity  Triple #androidx.activity.ComponentActivity  Uri #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  any #androidx.activity.ComponentActivity  applyChoice #androidx.activity.ComponentActivity  
checkGameOver #androidx.activity.ComponentActivity  coerceAtMost #androidx.activity.ComponentActivity  
component1 #androidx.activity.ComponentActivity  
component2 #androidx.activity.ComponentActivity  contains #androidx.activity.ComponentActivity  	emptyList #androidx.activity.ComponentActivity  emptyMap #androidx.activity.ComponentActivity  filter #androidx.activity.ComponentActivity  forEachIndexed #androidx.activity.ComponentActivity  generateComplianceReport #androidx.activity.ComponentActivity  getCurrentLanguage #androidx.activity.ComponentActivity  getEvent #androidx.activity.ComponentActivity  getEventMapStatistics #androidx.activity.ComponentActivity  getGameOverReason #androidx.activity.ComponentActivity  getLanguageDisplayName #androidx.activity.ComponentActivity  	getString #androidx.activity.ComponentActivity  getSupportedLanguages #androidx.activity.ComponentActivity  giveResourceReward #androidx.activity.ComponentActivity  indexOf #androidx.activity.ComponentActivity  indices #androidx.activity.ComponentActivity  
initialize #androidx.activity.ComponentActivity  initializeGame #androidx.activity.ComponentActivity  initializeLanguage #androidx.activity.ComponentActivity  isChoiceAvailable #androidx.activity.ComponentActivity  
isInitialized #androidx.activity.ComponentActivity  isMusicEnabled #androidx.activity.ComponentActivity  
isNotEmpty #androidx.activity.ComponentActivity  
isNullOrEmpty #androidx.activity.ComponentActivity  	isVictory #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  joinToString #androidx.activity.ComponentActivity  let #androidx.activity.ComponentActivity  listOf #androidx.activity.ComponentActivity  
loadEvents #androidx.activity.ComponentActivity  map #androidx.activity.ComponentActivity  mapOf #androidx.activity.ComponentActivity  
mutableListOf #androidx.activity.ComponentActivity  mutableMapOf #androidx.activity.ComponentActivity  openPrivacyPolicyUrl #androidx.activity.ComponentActivity  pauseBackgroundMusic #androidx.activity.ComponentActivity  performNightPhase #androidx.activity.ComponentActivity  preloadRewardVideo #androidx.activity.ComponentActivity  	resetGame #androidx.activity.ComponentActivity  resumeBackgroundMusic #androidx.activity.ComponentActivity  reviveCharacter #androidx.activity.ComponentActivity  run #androidx.activity.ComponentActivity  set #androidx.activity.ComponentActivity  setGameState #androidx.activity.ComponentActivity  setLanguage #androidx.activity.ComponentActivity  showAdErrorDialog #androidx.activity.ComponentActivity  showReviveAd #androidx.activity.ComponentActivity  showReviveSuccessDialog #androidx.activity.ComponentActivity  showRewardDialog #androidx.activity.ComponentActivity  showRewardVideoForResource #androidx.activity.ComponentActivity  split #androidx.activity.ComponentActivity  startActivityForResult #androidx.activity.ComponentActivity  startBackgroundMusic #androidx.activity.ComponentActivity  stopBackgroundMusic #androidx.activity.ComponentActivity  take #androidx.activity.ComponentActivity  testGameLoop #androidx.activity.ComponentActivity  to #androidx.activity.ComponentActivity  toIntOrNull #androidx.activity.ComponentActivity  toList #androidx.activity.ComponentActivity  toMutableMap #androidx.activity.ComponentActivity  toMutableSet #androidx.activity.ComponentActivity  toTypedArray #androidx.activity.ComponentActivity  toggleMusic #androidx.activity.ComponentActivity  trim #androidx.activity.ComponentActivity  validateEventMap #androidx.activity.ComponentActivity  
AdCallback -androidx.activity.ComponentActivity.AdManager  ResourceType -androidx.activity.ComponentActivity.AdManager  ReviveAdCallback -androidx.activity.ComponentActivity.AdManager  ChoiceResult /androidx.activity.ComponentActivity.GameManager  	ActionBar androidx.appcompat.app  AlertDialog androidx.appcompat.app  AppCompatActivity androidx.appcompat.app  hide  androidx.appcompat.app.ActionBar  Builder "androidx.appcompat.app.AlertDialog  dismiss "androidx.appcompat.app.AlertDialog  show "androidx.appcompat.app.AlertDialog  create *androidx.appcompat.app.AlertDialog.Builder  
setCancelable *androidx.appcompat.app.AlertDialog.Builder  
setMessage *androidx.appcompat.app.AlertDialog.Builder  setNegativeButton *androidx.appcompat.app.AlertDialog.Builder  setPositiveButton *androidx.appcompat.app.AlertDialog.Builder  setSingleChoiceItems *androidx.appcompat.app.AlertDialog.Builder  setTitle *androidx.appcompat.app.AlertDialog.Builder  show *androidx.appcompat.app.AlertDialog.Builder  ActivityMainBinding (androidx.appcompat.app.AppCompatActivity  ActivityPrivacyPolicyBinding (androidx.appcompat.app.AppCompatActivity  ActivityStartBinding (androidx.appcompat.app.AppCompatActivity  	AdManager (androidx.appcompat.app.AppCompatActivity  AlertDialog (androidx.appcompat.app.AppCompatActivity  Choice (androidx.appcompat.app.AppCompatActivity  
ClickableSpan (androidx.appcompat.app.AppCompatActivity  Double (androidx.appcompat.app.AppCompatActivity  	Exception (androidx.appcompat.app.AppCompatActivity  Float (androidx.appcompat.app.AppCompatActivity  ForegroundColorSpan (androidx.appcompat.app.AppCompatActivity  
GameEngine (androidx.appcompat.app.AppCompatActivity  GameEventUtils (androidx.appcompat.app.AppCompatActivity  GameLoopTest (androidx.appcompat.app.AppCompatActivity  GameManager (androidx.appcompat.app.AppCompatActivity  	GameState (androidx.appcompat.app.AppCompatActivity  GameUIState (androidx.appcompat.app.AppCompatActivity  Int (androidx.appcompat.app.AppCompatActivity  Intent (androidx.appcompat.app.AppCompatActivity  LanguageManager (androidx.appcompat.app.AppCompatActivity  LinkMovementMethod (androidx.appcompat.app.AppCompatActivity  Log (androidx.appcompat.app.AppCompatActivity  Long (androidx.appcompat.app.AppCompatActivity  MODE_PRIVATE (androidx.appcompat.app.AppCompatActivity  MainActivity (androidx.appcompat.app.AppCompatActivity  MountainSurvivalApplication (androidx.appcompat.app.AppCompatActivity  MusicManager (androidx.appcompat.app.AppCompatActivity  PRIVACY_POLICY_REQUEST_CODE (androidx.appcompat.app.AppCompatActivity  PRIVACY_POLICY_URL (androidx.appcompat.app.AppCompatActivity  Pair (androidx.appcompat.app.AppCompatActivity  PrivacyAuditLogger (androidx.appcompat.app.AppCompatActivity  PrivacyPolicyActivity (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  RESULT_CANCELED (androidx.appcompat.app.AppCompatActivity  	RESULT_OK (androidx.appcompat.app.AppCompatActivity  SpannableString (androidx.appcompat.app.AppCompatActivity  Spanned (androidx.appcompat.app.AppCompatActivity  String (androidx.appcompat.app.AppCompatActivity  System (androidx.appcompat.app.AppCompatActivity  TAG (androidx.appcompat.app.AppCompatActivity  Triple (androidx.appcompat.app.AppCompatActivity  Uri (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  android (androidx.appcompat.app.AppCompatActivity  any (androidx.appcompat.app.AppCompatActivity  applyChoice (androidx.appcompat.app.AppCompatActivity  
checkGameOver (androidx.appcompat.app.AppCompatActivity  coerceAtMost (androidx.appcompat.app.AppCompatActivity  
component1 (androidx.appcompat.app.AppCompatActivity  
component2 (androidx.appcompat.app.AppCompatActivity  contains (androidx.appcompat.app.AppCompatActivity  	emptyList (androidx.appcompat.app.AppCompatActivity  emptyMap (androidx.appcompat.app.AppCompatActivity  filter (androidx.appcompat.app.AppCompatActivity  forEachIndexed (androidx.appcompat.app.AppCompatActivity  generateComplianceReport (androidx.appcompat.app.AppCompatActivity  getCurrentLanguage (androidx.appcompat.app.AppCompatActivity  getEvent (androidx.appcompat.app.AppCompatActivity  getEventMapStatistics (androidx.appcompat.app.AppCompatActivity  getGameOverReason (androidx.appcompat.app.AppCompatActivity  getLanguageDisplayName (androidx.appcompat.app.AppCompatActivity  	getString (androidx.appcompat.app.AppCompatActivity  getSupportedLanguages (androidx.appcompat.app.AppCompatActivity  giveResourceReward (androidx.appcompat.app.AppCompatActivity  indexOf (androidx.appcompat.app.AppCompatActivity  indices (androidx.appcompat.app.AppCompatActivity  
initialize (androidx.appcompat.app.AppCompatActivity  initializeGame (androidx.appcompat.app.AppCompatActivity  initializeLanguage (androidx.appcompat.app.AppCompatActivity  isChoiceAvailable (androidx.appcompat.app.AppCompatActivity  
isInitialized (androidx.appcompat.app.AppCompatActivity  isMusicEnabled (androidx.appcompat.app.AppCompatActivity  
isNotEmpty (androidx.appcompat.app.AppCompatActivity  
isNullOrEmpty (androidx.appcompat.app.AppCompatActivity  	isVictory (androidx.appcompat.app.AppCompatActivity  java (androidx.appcompat.app.AppCompatActivity  joinToString (androidx.appcompat.app.AppCompatActivity  let (androidx.appcompat.app.AppCompatActivity  listOf (androidx.appcompat.app.AppCompatActivity  
loadEvents (androidx.appcompat.app.AppCompatActivity  map (androidx.appcompat.app.AppCompatActivity  mapOf (androidx.appcompat.app.AppCompatActivity  
mutableListOf (androidx.appcompat.app.AppCompatActivity  mutableMapOf (androidx.appcompat.app.AppCompatActivity  onActivityResult (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  	onDestroy (androidx.appcompat.app.AppCompatActivity  onPause (androidx.appcompat.app.AppCompatActivity  onResume (androidx.appcompat.app.AppCompatActivity  openPrivacyPolicyUrl (androidx.appcompat.app.AppCompatActivity  pauseBackgroundMusic (androidx.appcompat.app.AppCompatActivity  performNightPhase (androidx.appcompat.app.AppCompatActivity  preloadRewardVideo (androidx.appcompat.app.AppCompatActivity  	resetGame (androidx.appcompat.app.AppCompatActivity  	resources (androidx.appcompat.app.AppCompatActivity  resumeBackgroundMusic (androidx.appcompat.app.AppCompatActivity  reviveCharacter (androidx.appcompat.app.AppCompatActivity  run (androidx.appcompat.app.AppCompatActivity  set (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  setGameState (androidx.appcompat.app.AppCompatActivity  setLanguage (androidx.appcompat.app.AppCompatActivity  showAdErrorDialog (androidx.appcompat.app.AppCompatActivity  showReviveAd (androidx.appcompat.app.AppCompatActivity  showReviveSuccessDialog (androidx.appcompat.app.AppCompatActivity  showRewardDialog (androidx.appcompat.app.AppCompatActivity  showRewardVideoForResource (androidx.appcompat.app.AppCompatActivity  split (androidx.appcompat.app.AppCompatActivity  startBackgroundMusic (androidx.appcompat.app.AppCompatActivity  stopBackgroundMusic (androidx.appcompat.app.AppCompatActivity  supportActionBar (androidx.appcompat.app.AppCompatActivity  take (androidx.appcompat.app.AppCompatActivity  testGameLoop (androidx.appcompat.app.AppCompatActivity  to (androidx.appcompat.app.AppCompatActivity  toIntOrNull (androidx.appcompat.app.AppCompatActivity  toList (androidx.appcompat.app.AppCompatActivity  toMutableMap (androidx.appcompat.app.AppCompatActivity  toMutableSet (androidx.appcompat.app.AppCompatActivity  toTypedArray (androidx.appcompat.app.AppCompatActivity  toggleMusic (androidx.appcompat.app.AppCompatActivity  trim (androidx.appcompat.app.AppCompatActivity  validateEventMap (androidx.appcompat.app.AppCompatActivity  
AdCallback 2androidx.appcompat.app.AppCompatActivity.AdManager  ResourceType 2androidx.appcompat.app.AppCompatActivity.AdManager  ReviveAdCallback 2androidx.appcompat.app.AppCompatActivity.AdManager  ChoiceResult 4androidx.appcompat.app.AppCompatActivity.GameManager  ConstraintLayout  androidx.constraintlayout.widget  ActivityMainBinding #androidx.core.app.ComponentActivity  ActivityPrivacyPolicyBinding #androidx.core.app.ComponentActivity  ActivityStartBinding #androidx.core.app.ComponentActivity  	AdManager #androidx.core.app.ComponentActivity  AlertDialog #androidx.core.app.ComponentActivity  Choice #androidx.core.app.ComponentActivity  
ClickableSpan #androidx.core.app.ComponentActivity  Double #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  Float #androidx.core.app.ComponentActivity  ForegroundColorSpan #androidx.core.app.ComponentActivity  
GameEngine #androidx.core.app.ComponentActivity  GameEventUtils #androidx.core.app.ComponentActivity  GameLoopTest #androidx.core.app.ComponentActivity  GameManager #androidx.core.app.ComponentActivity  	GameState #androidx.core.app.ComponentActivity  GameUIState #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  LanguageManager #androidx.core.app.ComponentActivity  LinkMovementMethod #androidx.core.app.ComponentActivity  Log #androidx.core.app.ComponentActivity  Long #androidx.core.app.ComponentActivity  MODE_PRIVATE #androidx.core.app.ComponentActivity  MainActivity #androidx.core.app.ComponentActivity  MountainSurvivalApplication #androidx.core.app.ComponentActivity  MusicManager #androidx.core.app.ComponentActivity  PRIVACY_POLICY_REQUEST_CODE #androidx.core.app.ComponentActivity  PRIVACY_POLICY_URL #androidx.core.app.ComponentActivity  Pair #androidx.core.app.ComponentActivity  PrivacyAuditLogger #androidx.core.app.ComponentActivity  PrivacyPolicyActivity #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  RESULT_CANCELED #androidx.core.app.ComponentActivity  	RESULT_OK #androidx.core.app.ComponentActivity  SpannableString #androidx.core.app.ComponentActivity  Spanned #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  System #androidx.core.app.ComponentActivity  TAG #androidx.core.app.ComponentActivity  Triple #androidx.core.app.ComponentActivity  Uri #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  any #androidx.core.app.ComponentActivity  applyChoice #androidx.core.app.ComponentActivity  
checkGameOver #androidx.core.app.ComponentActivity  coerceAtMost #androidx.core.app.ComponentActivity  
component1 #androidx.core.app.ComponentActivity  
component2 #androidx.core.app.ComponentActivity  contains #androidx.core.app.ComponentActivity  	emptyList #androidx.core.app.ComponentActivity  emptyMap #androidx.core.app.ComponentActivity  filter #androidx.core.app.ComponentActivity  forEachIndexed #androidx.core.app.ComponentActivity  generateComplianceReport #androidx.core.app.ComponentActivity  getCurrentLanguage #androidx.core.app.ComponentActivity  getEvent #androidx.core.app.ComponentActivity  getEventMapStatistics #androidx.core.app.ComponentActivity  getGameOverReason #androidx.core.app.ComponentActivity  getLanguageDisplayName #androidx.core.app.ComponentActivity  	getString #androidx.core.app.ComponentActivity  getSupportedLanguages #androidx.core.app.ComponentActivity  giveResourceReward #androidx.core.app.ComponentActivity  indexOf #androidx.core.app.ComponentActivity  indices #androidx.core.app.ComponentActivity  
initialize #androidx.core.app.ComponentActivity  initializeGame #androidx.core.app.ComponentActivity  initializeLanguage #androidx.core.app.ComponentActivity  isChoiceAvailable #androidx.core.app.ComponentActivity  
isInitialized #androidx.core.app.ComponentActivity  isMusicEnabled #androidx.core.app.ComponentActivity  
isNotEmpty #androidx.core.app.ComponentActivity  
isNullOrEmpty #androidx.core.app.ComponentActivity  	isVictory #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  joinToString #androidx.core.app.ComponentActivity  let #androidx.core.app.ComponentActivity  listOf #androidx.core.app.ComponentActivity  
loadEvents #androidx.core.app.ComponentActivity  map #androidx.core.app.ComponentActivity  mapOf #androidx.core.app.ComponentActivity  
mutableListOf #androidx.core.app.ComponentActivity  mutableMapOf #androidx.core.app.ComponentActivity  openPrivacyPolicyUrl #androidx.core.app.ComponentActivity  pauseBackgroundMusic #androidx.core.app.ComponentActivity  performNightPhase #androidx.core.app.ComponentActivity  preloadRewardVideo #androidx.core.app.ComponentActivity  	resetGame #androidx.core.app.ComponentActivity  resumeBackgroundMusic #androidx.core.app.ComponentActivity  reviveCharacter #androidx.core.app.ComponentActivity  run #androidx.core.app.ComponentActivity  set #androidx.core.app.ComponentActivity  setGameState #androidx.core.app.ComponentActivity  setLanguage #androidx.core.app.ComponentActivity  showAdErrorDialog #androidx.core.app.ComponentActivity  showReviveAd #androidx.core.app.ComponentActivity  showReviveSuccessDialog #androidx.core.app.ComponentActivity  showRewardDialog #androidx.core.app.ComponentActivity  showRewardVideoForResource #androidx.core.app.ComponentActivity  split #androidx.core.app.ComponentActivity  startBackgroundMusic #androidx.core.app.ComponentActivity  stopBackgroundMusic #androidx.core.app.ComponentActivity  take #androidx.core.app.ComponentActivity  testGameLoop #androidx.core.app.ComponentActivity  to #androidx.core.app.ComponentActivity  toIntOrNull #androidx.core.app.ComponentActivity  toList #androidx.core.app.ComponentActivity  toMutableMap #androidx.core.app.ComponentActivity  toMutableSet #androidx.core.app.ComponentActivity  toTypedArray #androidx.core.app.ComponentActivity  toggleMusic #androidx.core.app.ComponentActivity  trim #androidx.core.app.ComponentActivity  validateEventMap #androidx.core.app.ComponentActivity  
AdCallback -androidx.core.app.ComponentActivity.AdManager  ResourceType -androidx.core.app.ComponentActivity.AdManager  ReviveAdCallback -androidx.core.app.ComponentActivity.AdManager  ChoiceResult /androidx.core.app.ComponentActivity.GameManager  ActivityMainBinding &androidx.fragment.app.FragmentActivity  ActivityPrivacyPolicyBinding &androidx.fragment.app.FragmentActivity  ActivityStartBinding &androidx.fragment.app.FragmentActivity  	AdManager &androidx.fragment.app.FragmentActivity  AlertDialog &androidx.fragment.app.FragmentActivity  Choice &androidx.fragment.app.FragmentActivity  
ClickableSpan &androidx.fragment.app.FragmentActivity  Double &androidx.fragment.app.FragmentActivity  	Exception &androidx.fragment.app.FragmentActivity  Float &androidx.fragment.app.FragmentActivity  ForegroundColorSpan &androidx.fragment.app.FragmentActivity  
GameEngine &androidx.fragment.app.FragmentActivity  GameEventUtils &androidx.fragment.app.FragmentActivity  GameLoopTest &androidx.fragment.app.FragmentActivity  GameManager &androidx.fragment.app.FragmentActivity  	GameState &androidx.fragment.app.FragmentActivity  GameUIState &androidx.fragment.app.FragmentActivity  Int &androidx.fragment.app.FragmentActivity  Intent &androidx.fragment.app.FragmentActivity  LanguageManager &androidx.fragment.app.FragmentActivity  LinkMovementMethod &androidx.fragment.app.FragmentActivity  Log &androidx.fragment.app.FragmentActivity  Long &androidx.fragment.app.FragmentActivity  MODE_PRIVATE &androidx.fragment.app.FragmentActivity  MainActivity &androidx.fragment.app.FragmentActivity  MountainSurvivalApplication &androidx.fragment.app.FragmentActivity  MusicManager &androidx.fragment.app.FragmentActivity  PRIVACY_POLICY_REQUEST_CODE &androidx.fragment.app.FragmentActivity  PRIVACY_POLICY_URL &androidx.fragment.app.FragmentActivity  Pair &androidx.fragment.app.FragmentActivity  PrivacyAuditLogger &androidx.fragment.app.FragmentActivity  PrivacyPolicyActivity &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  RESULT_CANCELED &androidx.fragment.app.FragmentActivity  	RESULT_OK &androidx.fragment.app.FragmentActivity  SpannableString &androidx.fragment.app.FragmentActivity  Spanned &androidx.fragment.app.FragmentActivity  String &androidx.fragment.app.FragmentActivity  System &androidx.fragment.app.FragmentActivity  TAG &androidx.fragment.app.FragmentActivity  Triple &androidx.fragment.app.FragmentActivity  Uri &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  android &androidx.fragment.app.FragmentActivity  any &androidx.fragment.app.FragmentActivity  applyChoice &androidx.fragment.app.FragmentActivity  
checkGameOver &androidx.fragment.app.FragmentActivity  coerceAtMost &androidx.fragment.app.FragmentActivity  
component1 &androidx.fragment.app.FragmentActivity  
component2 &androidx.fragment.app.FragmentActivity  contains &androidx.fragment.app.FragmentActivity  	emptyList &androidx.fragment.app.FragmentActivity  emptyMap &androidx.fragment.app.FragmentActivity  filter &androidx.fragment.app.FragmentActivity  forEachIndexed &androidx.fragment.app.FragmentActivity  generateComplianceReport &androidx.fragment.app.FragmentActivity  getCurrentLanguage &androidx.fragment.app.FragmentActivity  getEvent &androidx.fragment.app.FragmentActivity  getEventMapStatistics &androidx.fragment.app.FragmentActivity  getGameOverReason &androidx.fragment.app.FragmentActivity  getLanguageDisplayName &androidx.fragment.app.FragmentActivity  	getString &androidx.fragment.app.FragmentActivity  getSupportedLanguages &androidx.fragment.app.FragmentActivity  giveResourceReward &androidx.fragment.app.FragmentActivity  indexOf &androidx.fragment.app.FragmentActivity  indices &androidx.fragment.app.FragmentActivity  
initialize &androidx.fragment.app.FragmentActivity  initializeGame &androidx.fragment.app.FragmentActivity  initializeLanguage &androidx.fragment.app.FragmentActivity  isChoiceAvailable &androidx.fragment.app.FragmentActivity  
isInitialized &androidx.fragment.app.FragmentActivity  isMusicEnabled &androidx.fragment.app.FragmentActivity  
isNotEmpty &androidx.fragment.app.FragmentActivity  
isNullOrEmpty &androidx.fragment.app.FragmentActivity  	isVictory &androidx.fragment.app.FragmentActivity  java &androidx.fragment.app.FragmentActivity  joinToString &androidx.fragment.app.FragmentActivity  let &androidx.fragment.app.FragmentActivity  listOf &androidx.fragment.app.FragmentActivity  
loadEvents &androidx.fragment.app.FragmentActivity  map &androidx.fragment.app.FragmentActivity  mapOf &androidx.fragment.app.FragmentActivity  
mutableListOf &androidx.fragment.app.FragmentActivity  mutableMapOf &androidx.fragment.app.FragmentActivity  onActivityResult &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  onPause &androidx.fragment.app.FragmentActivity  onResume &androidx.fragment.app.FragmentActivity  openPrivacyPolicyUrl &androidx.fragment.app.FragmentActivity  pauseBackgroundMusic &androidx.fragment.app.FragmentActivity  performNightPhase &androidx.fragment.app.FragmentActivity  preloadRewardVideo &androidx.fragment.app.FragmentActivity  	resetGame &androidx.fragment.app.FragmentActivity  resumeBackgroundMusic &androidx.fragment.app.FragmentActivity  reviveCharacter &androidx.fragment.app.FragmentActivity  run &androidx.fragment.app.FragmentActivity  set &androidx.fragment.app.FragmentActivity  setGameState &androidx.fragment.app.FragmentActivity  setLanguage &androidx.fragment.app.FragmentActivity  showAdErrorDialog &androidx.fragment.app.FragmentActivity  showReviveAd &androidx.fragment.app.FragmentActivity  showReviveSuccessDialog &androidx.fragment.app.FragmentActivity  showRewardDialog &androidx.fragment.app.FragmentActivity  showRewardVideoForResource &androidx.fragment.app.FragmentActivity  split &androidx.fragment.app.FragmentActivity  startBackgroundMusic &androidx.fragment.app.FragmentActivity  stopBackgroundMusic &androidx.fragment.app.FragmentActivity  take &androidx.fragment.app.FragmentActivity  testGameLoop &androidx.fragment.app.FragmentActivity  to &androidx.fragment.app.FragmentActivity  toIntOrNull &androidx.fragment.app.FragmentActivity  toList &androidx.fragment.app.FragmentActivity  toMutableMap &androidx.fragment.app.FragmentActivity  toMutableSet &androidx.fragment.app.FragmentActivity  toTypedArray &androidx.fragment.app.FragmentActivity  toggleMusic &androidx.fragment.app.FragmentActivity  trim &androidx.fragment.app.FragmentActivity  validateEventMap &androidx.fragment.app.FragmentActivity  
AdCallback 0androidx.fragment.app.FragmentActivity.AdManager  ResourceType 0androidx.fragment.app.FragmentActivity.AdManager  ReviveAdCallback 0androidx.fragment.app.FragmentActivity.AdManager  ChoiceResult 2androidx.fragment.app.FragmentActivity.GameManager  MultiDexApplication androidx.multidex  APP_KEY %androidx.multidex.MultiDexApplication  	AdManager %androidx.multidex.MultiDexApplication  Build %androidx.multidex.MultiDexApplication  	Exception %androidx.multidex.MultiDexApplication  LanguageManager %androidx.multidex.MultiDexApplication  Log %androidx.multidex.MultiDexApplication  OSETInitListener %androidx.multidex.MultiDexApplication  OSETSDK %androidx.multidex.MultiDexApplication  PrivacyComplianceManager %androidx.multidex.MultiDexApplication  REWARD_VIDEO_AD_ID %androidx.multidex.MultiDexApplication  String %androidx.multidex.MultiDexApplication  TAG %androidx.multidex.MultiDexApplication  WebView %androidx.multidex.MultiDexApplication  attachBaseContext %androidx.multidex.MultiDexApplication  getCurrentLanguage %androidx.multidex.MultiDexApplication  getProcessName %androidx.multidex.MultiDexApplication  initializeLanguage %androidx.multidex.MultiDexApplication  isAdSDKInitialized %androidx.multidex.MultiDexApplication  let %androidx.multidex.MultiDexApplication  
logSDKNote %androidx.multidex.MultiDexApplication  onCreate %androidx.multidex.MultiDexApplication  preloadRewardedAd %androidx.multidex.MultiDexApplication  APP_KEY com.ainative.mountainsurvival  Activity com.ainative.mountainsurvival  ActivityMainBinding com.ainative.mountainsurvival  ActivityPrivacyPolicyBinding com.ainative.mountainsurvival  ActivityStartBinding com.ainative.mountainsurvival  
AdCallback com.ainative.mountainsurvival  	AdManager com.ainative.mountainsurvival  AlertDialog com.ainative.mountainsurvival  Any com.ainative.mountainsurvival  AppCompatActivity com.ainative.mountainsurvival  Boolean com.ainative.mountainsurvival  Build com.ainative.mountainsurvival  Bundle com.ainative.mountainsurvival  	ByteArray com.ainative.mountainsurvival  Charsets com.ainative.mountainsurvival  Choice com.ainative.mountainsurvival  ChoiceResult com.ainative.mountainsurvival  
ClickableSpan com.ainative.mountainsurvival  
Configuration com.ainative.mountainsurvival  Context com.ainative.mountainsurvival  
DayTestResult com.ainative.mountainsurvival  Double com.ainative.mountainsurvival  	EatResult com.ainative.mountainsurvival  EventContainer com.ainative.mountainsurvival  EventEngine com.ainative.mountainsurvival  EventFlowTestResult com.ainative.mountainsurvival  EventTestResult com.ainative.mountainsurvival  
EventTestStep com.ainative.mountainsurvival  	Exception com.ainative.mountainsurvival  Float com.ainative.mountainsurvival  ForegroundColorSpan com.ainative.mountainsurvival  
GameEngine com.ainative.mountainsurvival  GameEngineTest com.ainative.mountainsurvival  	GameEvent com.ainative.mountainsurvival  GameEventUtils com.ainative.mountainsurvival  GameLoopTest com.ainative.mountainsurvival  GameLoopTestResult com.ainative.mountainsurvival  GameManager com.ainative.mountainsurvival  GameOverResult com.ainative.mountainsurvival  GameOverTestResult com.ainative.mountainsurvival  	GameState com.ainative.mountainsurvival  GameStateSnapshot com.ainative.mountainsurvival  GameStatistics com.ainative.mountainsurvival  GameSystemTest com.ainative.mountainsurvival  GameUIState com.ainative.mountainsurvival  Gson com.ainative.mountainsurvival  IOException com.ainative.mountainsurvival  Int com.ainative.mountainsurvival  Intent com.ainative.mountainsurvival  JsonSyntaxException com.ainative.mountainsurvival  LanguageManager com.ainative.mountainsurvival  LinkMovementMethod com.ainative.mountainsurvival  List com.ainative.mountainsurvival  Locale com.ainative.mountainsurvival  Log com.ainative.mountainsurvival  Long com.ainative.mountainsurvival  MODE_PRIVATE com.ainative.mountainsurvival  MainActivity com.ainative.mountainsurvival  Map com.ainative.mountainsurvival  MediaPlayer com.ainative.mountainsurvival  MountainSurvivalApplication com.ainative.mountainsurvival  MultiDexApplication com.ainative.mountainsurvival  MusicManager com.ainative.mountainsurvival  
MutableMap com.ainative.mountainsurvival  
MutableSet com.ainative.mountainsurvival  NightPhaseResult com.ainative.mountainsurvival  NightSettlementResult com.ainative.mountainsurvival  OSETInitListener com.ainative.mountainsurvival  OSETRewardListener com.ainative.mountainsurvival  OSETRewardVideo com.ainative.mountainsurvival  OSETSDK com.ainative.mountainsurvival  PRIVACY_POLICY_REQUEST_CODE com.ainative.mountainsurvival  PRIVACY_POLICY_URL com.ainative.mountainsurvival  Pair com.ainative.mountainsurvival  PerformanceTestResult com.ainative.mountainsurvival  PrivacyAuditLogger com.ainative.mountainsurvival  PrivacyComplianceManager com.ainative.mountainsurvival  PrivacyPolicyActivity com.ainative.mountainsurvival  R com.ainative.mountainsurvival  RESULT_CANCELED com.ainative.mountainsurvival  	RESULT_OK com.ainative.mountainsurvival  REWARD_VIDEO_AD_ID com.ainative.mountainsurvival  RandomChoice com.ainative.mountainsurvival  ResourceType com.ainative.mountainsurvival  ReviveAdCallback com.ainative.mountainsurvival  SpannableString com.ainative.mountainsurvival  Spanned com.ainative.mountainsurvival  
StartActivity com.ainative.mountainsurvival  String com.ainative.mountainsurvival  
StringBuilder com.ainative.mountainsurvival  System com.ainative.mountainsurvival  SystemTestResult com.ainative.mountainsurvival  TAG com.ainative.mountainsurvival  
TestResult com.ainative.mountainsurvival  Triple com.ainative.mountainsurvival  Uri com.ainative.mountainsurvival  View com.ainative.mountainsurvival  WebView com.ainative.mountainsurvival  android com.ainative.mountainsurvival  any com.ainative.mountainsurvival  applyChoice com.ainative.mountainsurvival  average com.ainative.mountainsurvival  
checkGameOver com.ainative.mountainsurvival  
coerceAtLeast com.ainative.mountainsurvival  coerceAtMost com.ainative.mountainsurvival  coerceIn com.ainative.mountainsurvival  
component1 com.ainative.mountainsurvival  
component2 com.ainative.mountainsurvival  contains com.ainative.mountainsurvival  count com.ainative.mountainsurvival  	emptyList com.ainative.mountainsurvival  emptyMap com.ainative.mountainsurvival  filter com.ainative.mountainsurvival  filterIsInstance com.ainative.mountainsurvival  find com.ainative.mountainsurvival  forEach com.ainative.mountainsurvival  forEachIndexed com.ainative.mountainsurvival  generateComplianceReport com.ainative.mountainsurvival  getCurrentLanguage com.ainative.mountainsurvival  getEvent com.ainative.mountainsurvival  getEventMapStatistics com.ainative.mountainsurvival  getGameOverReason com.ainative.mountainsurvival  getGameStatistics com.ainative.mountainsurvival  getLanguageDisplayName com.ainative.mountainsurvival  getProcessName com.ainative.mountainsurvival  	getString com.ainative.mountainsurvival  getSupportedLanguages com.ainative.mountainsurvival  giveResourceReward com.ainative.mountainsurvival  hasEvent com.ainative.mountainsurvival  indexOf com.ainative.mountainsurvival  indices com.ainative.mountainsurvival  
initialize com.ainative.mountainsurvival  initializeGame com.ainative.mountainsurvival  initializeLanguage com.ainative.mountainsurvival  invoke com.ainative.mountainsurvival  isAdSDKInitialized com.ainative.mountainsurvival  isBlank com.ainative.mountainsurvival  isChoiceAvailable com.ainative.mountainsurvival  isEmpty com.ainative.mountainsurvival  
isInitialized com.ainative.mountainsurvival  isMusicEnabled com.ainative.mountainsurvival  
isNotBlank com.ainative.mountainsurvival  
isNotEmpty com.ainative.mountainsurvival  
isNullOrEmpty com.ainative.mountainsurvival  isPrivacyPolicyAgreed com.ainative.mountainsurvival  	isVictory com.ainative.mountainsurvival  java com.ainative.mountainsurvival  joinToString com.ainative.mountainsurvival  let com.ainative.mountainsurvival  listOf com.ainative.mountainsurvival  
loadEvents com.ainative.mountainsurvival  logPrivacyPolicyStatusChange com.ainative.mountainsurvival  
logSDKNote com.ainative.mountainsurvival  	lowercase com.ainative.mountainsurvival  map com.ainative.mountainsurvival  mapOf com.ainative.mountainsurvival  maxOfOrNull com.ainative.mountainsurvival  	maxOrNull com.ainative.mountainsurvival  minOfOrNull com.ainative.mountainsurvival  	minOrNull com.ainative.mountainsurvival  minusAssign com.ainative.mountainsurvival  
mutableListOf com.ainative.mountainsurvival  mutableMapOf com.ainative.mountainsurvival  mutableSetOf com.ainative.mountainsurvival  openPrivacyPolicyUrl com.ainative.mountainsurvival  pauseBackgroundMusic com.ainative.mountainsurvival  performNightPhase com.ainative.mountainsurvival  
plusAssign com.ainative.mountainsurvival  preloadRewardVideo com.ainative.mountainsurvival  preloadRewardedAd com.ainative.mountainsurvival  repeat com.ainative.mountainsurvival  	resetGame com.ainative.mountainsurvival  resumeBackgroundMusic com.ainative.mountainsurvival  reviveCharacter com.ainative.mountainsurvival  run com.ainative.mountainsurvival  set com.ainative.mountainsurvival  setGameState com.ainative.mountainsurvival  setLanguage com.ainative.mountainsurvival  showAdErrorDialog com.ainative.mountainsurvival  showReviveAd com.ainative.mountainsurvival  showReviveSuccessDialog com.ainative.mountainsurvival  showRewardDialog com.ainative.mountainsurvival  showRewardVideoForResource com.ainative.mountainsurvival  split com.ainative.mountainsurvival  startBackgroundMusic com.ainative.mountainsurvival  stopBackgroundMusic com.ainative.mountainsurvival  sumOf com.ainative.mountainsurvival  take com.ainative.mountainsurvival  testGameLoop com.ainative.mountainsurvival  to com.ainative.mountainsurvival  toIntOrNull com.ainative.mountainsurvival  toList com.ainative.mountainsurvival  toMap com.ainative.mountainsurvival  toMutableMap com.ainative.mountainsurvival  toMutableSet com.ainative.mountainsurvival  toTypedArray com.ainative.mountainsurvival  toggleMusic com.ainative.mountainsurvival  trim com.ainative.mountainsurvival  until com.ainative.mountainsurvival  use com.ainative.mountainsurvival  
validateEvent com.ainative.mountainsurvival  validateEventMap com.ainative.mountainsurvival  Activity 'com.ainative.mountainsurvival.AdManager  
AdCallback 'com.ainative.mountainsurvival.AdManager  Context 'com.ainative.mountainsurvival.AdManager  Double 'com.ainative.mountainsurvival.AdManager  	Exception 'com.ainative.mountainsurvival.AdManager  Int 'com.ainative.mountainsurvival.AdManager  Log 'com.ainative.mountainsurvival.AdManager  MountainSurvivalApplication 'com.ainative.mountainsurvival.AdManager  OSETRewardListener 'com.ainative.mountainsurvival.AdManager  OSETRewardVideo 'com.ainative.mountainsurvival.AdManager  OSETSDK 'com.ainative.mountainsurvival.AdManager  ResourceType 'com.ainative.mountainsurvival.AdManager  ReviveAdCallback 'com.ainative.mountainsurvival.AdManager  String 'com.ainative.mountainsurvival.AdManager  TAG 'com.ainative.mountainsurvival.AdManager  preloadRewardVideo 'com.ainative.mountainsurvival.AdManager  preloadRewardedAd 'com.ainative.mountainsurvival.AdManager  showReviveAd 'com.ainative.mountainsurvival.AdManager  showRewardVideoForResource 'com.ainative.mountainsurvival.AdManager  
onAdClosed 2com.ainative.mountainsurvival.AdManager.AdCallback  onAdLoadFailed 2com.ainative.mountainsurvival.AdManager.AdCallback  onAdLoadSuccess 2com.ainative.mountainsurvival.AdManager.AdCallback  onAdRewarded 2com.ainative.mountainsurvival.AdManager.AdCallback  onAdShowSuccess 2com.ainative.mountainsurvival.AdManager.AdCallback  FIREWOOD 4com.ainative.mountainsurvival.AdManager.ResourceType  FOOD 4com.ainative.mountainsurvival.AdManager.ResourceType  STAMINA 4com.ainative.mountainsurvival.AdManager.ResourceType  WARMTH 4com.ainative.mountainsurvival.AdManager.ResourceType  displayName 4com.ainative.mountainsurvival.AdManager.ResourceType  rewardAmount 4com.ainative.mountainsurvival.AdManager.ResourceType  
onAdClosed 8com.ainative.mountainsurvival.AdManager.ReviveAdCallback  onAdLoadFailed 8com.ainative.mountainsurvival.AdManager.ReviveAdCallback  onAdLoadSuccess 8com.ainative.mountainsurvival.AdManager.ReviveAdCallback  onAdShowSuccess 8com.ainative.mountainsurvival.AdManager.ReviveAdCallback  onReviveSuccess 8com.ainative.mountainsurvival.AdManager.ReviveAdCallback  effects $com.ainative.mountainsurvival.Choice  let $com.ainative.mountainsurvival.Choice  nextEventId $com.ainative.mountainsurvival.Choice  requirements $com.ainative.mountainsurvival.Choice  
resultText $com.ainative.mountainsurvival.Choice  specialEffects $com.ainative.mountainsurvival.Choice  specialItemGained $com.ainative.mountainsurvival.Choice  specialItemReward $com.ainative.mountainsurvival.Choice  specialItemUsed $com.ainative.mountainsurvival.Choice  text $com.ainative.mountainsurvival.Choice  events ,com.ainative.mountainsurvival.EventContainer  	ByteArray )com.ainative.mountainsurvival.EventEngine  Charsets )com.ainative.mountainsurvival.EventEngine  EventContainer )com.ainative.mountainsurvival.EventEngine  GameEventUtils )com.ainative.mountainsurvival.EventEngine  Gson )com.ainative.mountainsurvival.EventEngine  String )com.ainative.mountainsurvival.EventEngine  any )com.ainative.mountainsurvival.EventEngine  contains )com.ainative.mountainsurvival.EventEngine  context )com.ainative.mountainsurvival.EventEngine  count )com.ainative.mountainsurvival.EventEngine  	emptyList )com.ainative.mountainsurvival.EventEngine  eventContainer )com.ainative.mountainsurvival.EventEngine  filter )com.ainative.mountainsurvival.EventEngine  find )com.ainative.mountainsurvival.EventEngine  getAllEvents )com.ainative.mountainsurvival.EventEngine  getEvent )com.ainative.mountainsurvival.EventEngine  gson )com.ainative.mountainsurvival.EventEngine  hasEvent )com.ainative.mountainsurvival.EventEngine  invoke )com.ainative.mountainsurvival.EventEngine  isChoiceAvailable )com.ainative.mountainsurvival.EventEngine  
isNotEmpty )com.ainative.mountainsurvival.EventEngine  
isNullOrEmpty )com.ainative.mountainsurvival.EventEngine  java )com.ainative.mountainsurvival.EventEngine  let )com.ainative.mountainsurvival.EventEngine  loadJsonFromAssets )com.ainative.mountainsurvival.EventEngine  	lowercase )com.ainative.mountainsurvival.EventEngine  mapOf )com.ainative.mountainsurvival.EventEngine  
mutableListOf )com.ainative.mountainsurvival.EventEngine  sumOf )com.ainative.mountainsurvival.EventEngine  to )com.ainative.mountainsurvival.EventEngine  
validateEvent )com.ainative.mountainsurvival.EventEngine  	ByteArray (com.ainative.mountainsurvival.GameEngine  Charsets (com.ainative.mountainsurvival.GameEngine  EVENTS_FILE_NAME (com.ainative.mountainsurvival.GameEngine  EVENTS_FILE_NAME_DE (com.ainative.mountainsurvival.GameEngine  EVENTS_FILE_NAME_EN (com.ainative.mountainsurvival.GameEngine  EVENTS_FILE_NAME_ES (com.ainative.mountainsurvival.GameEngine  EVENTS_FILE_NAME_FR (com.ainative.mountainsurvival.GameEngine  EVENTS_FILE_NAME_HI (com.ainative.mountainsurvival.GameEngine  EVENTS_FILE_NAME_IN (com.ainative.mountainsurvival.GameEngine  EVENTS_FILE_NAME_IT (com.ainative.mountainsurvival.GameEngine  EVENTS_FILE_NAME_JA (com.ainative.mountainsurvival.GameEngine  EVENTS_FILE_NAME_KO (com.ainative.mountainsurvival.GameEngine  EVENTS_FILE_NAME_MS (com.ainative.mountainsurvival.GameEngine  EVENTS_FILE_NAME_PT (com.ainative.mountainsurvival.GameEngine  EVENTS_FILE_NAME_RU (com.ainative.mountainsurvival.GameEngine  EVENTS_FILE_NAME_TH (com.ainative.mountainsurvival.GameEngine  EVENTS_FILE_NAME_TW (com.ainative.mountainsurvival.GameEngine  EVENTS_FILE_NAME_VI (com.ainative.mountainsurvival.GameEngine  EventContainer (com.ainative.mountainsurvival.GameEngine  Gson (com.ainative.mountainsurvival.GameEngine  LanguageManager (com.ainative.mountainsurvival.GameEngine  Log (com.ainative.mountainsurvival.GameEngine  String (com.ainative.mountainsurvival.GameEngine  TAG (com.ainative.mountainsurvival.GameEngine  convertEventsToMap (com.ainative.mountainsurvival.GameEngine  count (com.ainative.mountainsurvival.GameEngine  emptyMap (com.ainative.mountainsurvival.GameEngine  getCurrentLanguage (com.ainative.mountainsurvival.GameEngine  getEvent (com.ainative.mountainsurvival.GameEngine  getEventMapStatistics (com.ainative.mountainsurvival.GameEngine  gson (com.ainative.mountainsurvival.GameEngine  hasEvent (com.ainative.mountainsurvival.GameEngine  invoke (com.ainative.mountainsurvival.GameEngine  isBlank (com.ainative.mountainsurvival.GameEngine  isEmpty (com.ainative.mountainsurvival.GameEngine  
isNotBlank (com.ainative.mountainsurvival.GameEngine  
isNullOrEmpty (com.ainative.mountainsurvival.GameEngine  java (com.ainative.mountainsurvival.GameEngine  let (com.ainative.mountainsurvival.GameEngine  
loadEvents (com.ainative.mountainsurvival.GameEngine  logEventStatistics (com.ainative.mountainsurvival.GameEngine  mapOf (com.ainative.mountainsurvival.GameEngine  
mutableListOf (com.ainative.mountainsurvival.GameEngine  mutableMapOf (com.ainative.mountainsurvival.GameEngine  parseJsonToEventContainer (com.ainative.mountainsurvival.GameEngine  readJsonFromAssets (com.ainative.mountainsurvival.GameEngine  set (com.ainative.mountainsurvival.GameEngine  sumOf (com.ainative.mountainsurvival.GameEngine  to (com.ainative.mountainsurvival.GameEngine  toMap (com.ainative.mountainsurvival.GameEngine  use (com.ainative.mountainsurvival.GameEngine  validateEventMap (com.ainative.mountainsurvival.GameEngine  Any ,com.ainative.mountainsurvival.GameEngineTest  Boolean ,com.ainative.mountainsurvival.GameEngineTest  Context ,com.ainative.mountainsurvival.GameEngineTest  Double ,com.ainative.mountainsurvival.GameEngineTest  EventTestResult ,com.ainative.mountainsurvival.GameEngineTest  
GameEngine ,com.ainative.mountainsurvival.GameEngineTest  	GameEvent ,com.ainative.mountainsurvival.GameEngineTest  Int ,com.ainative.mountainsurvival.GameEngineTest  List ,com.ainative.mountainsurvival.GameEngineTest  Log ,com.ainative.mountainsurvival.GameEngineTest  Long ,com.ainative.mountainsurvival.GameEngineTest  Map ,com.ainative.mountainsurvival.GameEngineTest  Pair ,com.ainative.mountainsurvival.GameEngineTest  PerformanceTestResult ,com.ainative.mountainsurvival.GameEngineTest  String ,com.ainative.mountainsurvival.GameEngineTest  System ,com.ainative.mountainsurvival.GameEngineTest  TAG ,com.ainative.mountainsurvival.GameEngineTest  
TestResult ,com.ainative.mountainsurvival.GameEngineTest  average ,com.ainative.mountainsurvival.GameEngineTest  	emptyList ,com.ainative.mountainsurvival.GameEngineTest  emptyMap ,com.ainative.mountainsurvival.GameEngineTest  forEachIndexed ,com.ainative.mountainsurvival.GameEngineTest  getEvent ,com.ainative.mountainsurvival.GameEngineTest  getEventMapStatistics ,com.ainative.mountainsurvival.GameEngineTest  hasEvent ,com.ainative.mountainsurvival.GameEngineTest  isBlank ,com.ainative.mountainsurvival.GameEngineTest  let ,com.ainative.mountainsurvival.GameEngineTest  listOf ,com.ainative.mountainsurvival.GameEngineTest  
loadEvents ,com.ainative.mountainsurvival.GameEngineTest  map ,com.ainative.mountainsurvival.GameEngineTest  	maxOrNull ,com.ainative.mountainsurvival.GameEngineTest  	minOrNull ,com.ainative.mountainsurvival.GameEngineTest  
mutableListOf ,com.ainative.mountainsurvival.GameEngineTest  repeat ,com.ainative.mountainsurvival.GameEngineTest  to ,com.ainative.mountainsurvival.GameEngineTest  validateEventMap ,com.ainative.mountainsurvival.GameEngineTest  choices 'com.ainative.mountainsurvival.GameEvent  conditionalEvents 'com.ainative.mountainsurvival.GameEvent  effects 'com.ainative.mountainsurvival.GameEvent  id 'com.ainative.mountainsurvival.GameEvent  let 'com.ainative.mountainsurvival.GameEvent  text 'com.ainative.mountainsurvival.GameEvent  android ,com.ainative.mountainsurvival.GameEventUtils  
component1 ,com.ainative.mountainsurvival.GameEventUtils  
component2 ,com.ainative.mountainsurvival.GameEventUtils  convertToInt ,com.ainative.mountainsurvival.GameEventUtils  	emptyList ,com.ainative.mountainsurvival.GameEventUtils  filterIsInstance ,com.ainative.mountainsurvival.GameEventUtils  isBlank ,com.ainative.mountainsurvival.GameEventUtils  isChoiceAvailable ,com.ainative.mountainsurvival.GameEventUtils  listOf ,com.ainative.mountainsurvival.GameEventUtils  toIntOrNull ,com.ainative.mountainsurvival.GameEventUtils  
validateEvent ,com.ainative.mountainsurvival.GameEventUtils  Boolean *com.ainative.mountainsurvival.GameLoopTest  Choice *com.ainative.mountainsurvival.GameLoopTest  
DayTestResult *com.ainative.mountainsurvival.GameLoopTest  	Exception *com.ainative.mountainsurvival.GameLoopTest  GameLoopTestResult *com.ainative.mountainsurvival.GameLoopTest  GameManager *com.ainative.mountainsurvival.GameLoopTest  GameOverTestResult *com.ainative.mountainsurvival.GameLoopTest  	GameState *com.ainative.mountainsurvival.GameLoopTest  Int *com.ainative.mountainsurvival.GameLoopTest  List *com.ainative.mountainsurvival.GameLoopTest  Log *com.ainative.mountainsurvival.GameLoopTest  Long *com.ainative.mountainsurvival.GameLoopTest  String *com.ainative.mountainsurvival.GameLoopTest  System *com.ainative.mountainsurvival.GameLoopTest  TAG *com.ainative.mountainsurvival.GameLoopTest  applyChoice *com.ainative.mountainsurvival.GameLoopTest  
checkGameOver *com.ainative.mountainsurvival.GameLoopTest  getGameOverReason *com.ainative.mountainsurvival.GameLoopTest  initializeGame *com.ainative.mountainsurvival.GameLoopTest  	isVictory *com.ainative.mountainsurvival.GameLoopTest  mapOf *com.ainative.mountainsurvival.GameLoopTest  
mutableListOf *com.ainative.mountainsurvival.GameLoopTest  performNightPhase *com.ainative.mountainsurvival.GameLoopTest  repeat *com.ainative.mountainsurvival.GameLoopTest  testGameLoop *com.ainative.mountainsurvival.GameLoopTest  to *com.ainative.mountainsurvival.GameLoopTest  issues =com.ainative.mountainsurvival.GameLoopTest.GameLoopTestResult  success =com.ainative.mountainsurvival.GameLoopTest.GameLoopTestResult  NightPhaseResult 6com.ainative.mountainsurvival.GameLoopTest.GameManager  Any )com.ainative.mountainsurvival.GameManager  Boolean )com.ainative.mountainsurvival.GameManager  Choice )com.ainative.mountainsurvival.GameManager  ChoiceResult )com.ainative.mountainsurvival.GameManager  Double )com.ainative.mountainsurvival.GameManager  	EatResult )com.ainative.mountainsurvival.GameManager  Float )com.ainative.mountainsurvival.GameManager  GameOverResult )com.ainative.mountainsurvival.GameManager  	GameState )com.ainative.mountainsurvival.GameManager  GameStateSnapshot )com.ainative.mountainsurvival.GameManager  GameStatistics )com.ainative.mountainsurvival.GameManager  Int )com.ainative.mountainsurvival.GameManager  List )com.ainative.mountainsurvival.GameManager  Log )com.ainative.mountainsurvival.GameManager  Long )com.ainative.mountainsurvival.GameManager  Map )com.ainative.mountainsurvival.GameManager  NightPhaseResult )com.ainative.mountainsurvival.GameManager  NightSettlementResult )com.ainative.mountainsurvival.GameManager  Pair )com.ainative.mountainsurvival.GameManager  String )com.ainative.mountainsurvival.GameManager  System )com.ainative.mountainsurvival.GameManager  TAG )com.ainative.mountainsurvival.GameManager  applyChoice )com.ainative.mountainsurvival.GameManager  calculateTotalFirewoodCollected )com.ainative.mountainsurvival.GameManager  calculateTotalFoodConsumed )com.ainative.mountainsurvival.GameManager  
checkGameOver )com.ainative.mountainsurvival.GameManager  
component1 )com.ainative.mountainsurvival.GameManager  
component2 )com.ainative.mountainsurvival.GameManager  convertToInt )com.ainative.mountainsurvival.GameManager  emptyMap )com.ainative.mountainsurvival.GameManager  gameHistory )com.ainative.mountainsurvival.GameManager  	gameState )com.ainative.mountainsurvival.GameManager  getGameOverReason )com.ainative.mountainsurvival.GameManager  getGameOverResult )com.ainative.mountainsurvival.GameManager  getGameStatistics )com.ainative.mountainsurvival.GameManager  getPropertyValue )com.ainative.mountainsurvival.GameManager  initializeGame )com.ainative.mountainsurvival.GameManager  
isInitialized )com.ainative.mountainsurvival.GameManager  	isVictory )com.ainative.mountainsurvival.GameManager  let )com.ainative.mountainsurvival.GameManager  mapOf )com.ainative.mountainsurvival.GameManager  maxOfOrNull )com.ainative.mountainsurvival.GameManager  minOfOrNull )com.ainative.mountainsurvival.GameManager  
mutableListOf )com.ainative.mountainsurvival.GameManager  mutableMapOf )com.ainative.mountainsurvival.GameManager  mutableSetOf )com.ainative.mountainsurvival.GameManager  performNightPhase )com.ainative.mountainsurvival.GameManager  
plusAssign )com.ainative.mountainsurvival.GameManager  	resetGame )com.ainative.mountainsurvival.GameManager  saveStateSnapshot )com.ainative.mountainsurvival.GameManager  set )com.ainative.mountainsurvival.GameManager  setGameState )com.ainative.mountainsurvival.GameManager  to )com.ainative.mountainsurvival.GameManager  toIntOrNull )com.ainative.mountainsurvival.GameManager  toList )com.ainative.mountainsurvival.GameManager  until )com.ainative.mountainsurvival.GameManager  gameOverResult 6com.ainative.mountainsurvival.GameManager.ChoiceResult  message 6com.ainative.mountainsurvival.GameManager.ChoiceResult  specialItemGained 6com.ainative.mountainsurvival.GameManager.ChoiceResult  stateChanges 6com.ainative.mountainsurvival.GameManager.ChoiceResult  success 6com.ainative.mountainsurvival.GameManager.ChoiceResult  state ;com.ainative.mountainsurvival.GameManager.GameStateSnapshot  totalChoicesMade 8com.ainative.mountainsurvival.GameManager.GameStatistics  dayAfter :com.ainative.mountainsurvival.GameManager.NightPhaseResult  
firewoodAfter :com.ainative.mountainsurvival.GameManager.NightPhaseResult  firewoodBefore :com.ainative.mountainsurvival.GameManager.NightPhaseResult  firewoodUsed :com.ainative.mountainsurvival.GameManager.NightPhaseResult  hadEnoughFirewood :com.ainative.mountainsurvival.GameManager.NightPhaseResult  roofLeaking :com.ainative.mountainsurvival.GameManager.NightPhaseResult  roofLeakingWarmthLoss :com.ainative.mountainsurvival.GameManager.NightPhaseResult  staminaAfter :com.ainative.mountainsurvival.GameManager.NightPhaseResult  
staminaBefore :com.ainative.mountainsurvival.GameManager.NightPhaseResult  
staminaChange :com.ainative.mountainsurvival.GameManager.NightPhaseResult  warmthAfter :com.ainative.mountainsurvival.GameManager.NightPhaseResult  warmthBefore :com.ainative.mountainsurvival.GameManager.NightPhaseResult  warmthChange :com.ainative.mountainsurvival.GameManager.NightPhaseResult  Pair 'com.ainative.mountainsurvival.GameState  applyEffects 'com.ainative.mountainsurvival.GameState  cabinIntegrity 'com.ainative.mountainsurvival.GameState  
checkGameOver 'com.ainative.mountainsurvival.GameState  
coerceAtLeast 'com.ainative.mountainsurvival.GameState  coerceAtMost 'com.ainative.mountainsurvival.GameState  coerceIn 'com.ainative.mountainsurvival.GameState  
component1 'com.ainative.mountainsurvival.GameState  
component2 'com.ainative.mountainsurvival.GameState  copy 'com.ainative.mountainsurvival.GameState  
currentDay 'com.ainative.mountainsurvival.GameState  eat 'com.ainative.mountainsurvival.GameState  firewood 'com.ainative.mountainsurvival.GameState  food 'com.ainative.mountainsurvival.GameState  
getStatusText 'com.ainative.mountainsurvival.GameState  getVictoryType 'com.ainative.mountainsurvival.GameState  hasSpecialState 'com.ainative.mountainsurvival.GameState  hope 'com.ainative.mountainsurvival.GameState  let 'com.ainative.mountainsurvival.GameState  minusAssign 'com.ainative.mountainsurvival.GameState  nightTimeSettlement 'com.ainative.mountainsurvival.GameState  roofLeaking 'com.ainative.mountainsurvival.GameState  set 'com.ainative.mountainsurvival.GameState  setSpecialState 'com.ainative.mountainsurvival.GameState  specialItems 'com.ainative.mountainsurvival.GameState  
specialStates 'com.ainative.mountainsurvival.GameState  stamina 'com.ainative.mountainsurvival.GameState  warmth 'com.ainative.mountainsurvival.GameState  Boolean ,com.ainative.mountainsurvival.GameSystemTest  Choice ,com.ainative.mountainsurvival.GameSystemTest  Context ,com.ainative.mountainsurvival.GameSystemTest  EventFlowTestResult ,com.ainative.mountainsurvival.GameSystemTest  
EventTestStep ,com.ainative.mountainsurvival.GameSystemTest  	Exception ,com.ainative.mountainsurvival.GameSystemTest  
GameEngine ,com.ainative.mountainsurvival.GameSystemTest  GameManager ,com.ainative.mountainsurvival.GameSystemTest  	GameState ,com.ainative.mountainsurvival.GameSystemTest  Int ,com.ainative.mountainsurvival.GameSystemTest  List ,com.ainative.mountainsurvival.GameSystemTest  Log ,com.ainative.mountainsurvival.GameSystemTest  Long ,com.ainative.mountainsurvival.GameSystemTest  String ,com.ainative.mountainsurvival.GameSystemTest  System ,com.ainative.mountainsurvival.GameSystemTest  SystemTestResult ,com.ainative.mountainsurvival.GameSystemTest  TAG ,com.ainative.mountainsurvival.GameSystemTest  applyChoice ,com.ainative.mountainsurvival.GameSystemTest  getEvent ,com.ainative.mountainsurvival.GameSystemTest  getGameStatistics ,com.ainative.mountainsurvival.GameSystemTest  initializeGame ,com.ainative.mountainsurvival.GameSystemTest  
isNotEmpty ,com.ainative.mountainsurvival.GameSystemTest  let ,com.ainative.mountainsurvival.GameSystemTest  
loadEvents ,com.ainative.mountainsurvival.GameSystemTest  map ,com.ainative.mountainsurvival.GameSystemTest  mapOf ,com.ainative.mountainsurvival.GameSystemTest  
mutableListOf ,com.ainative.mountainsurvival.GameSystemTest  to ,com.ainative.mountainsurvival.GameSystemTest  validateEventMap ,com.ainative.mountainsurvival.GameSystemTest  GameStatistics 8com.ainative.mountainsurvival.GameSystemTest.GameManager  
Configuration -com.ainative.mountainsurvival.LanguageManager  Context -com.ainative.mountainsurvival.LanguageManager  DEFAULT_LANGUAGE -com.ainative.mountainsurvival.LanguageManager  KEY_LANGUAGE -com.ainative.mountainsurvival.LanguageManager  LANGUAGE_ENGLISH -com.ainative.mountainsurvival.LanguageManager  LANGUAGE_FRENCH -com.ainative.mountainsurvival.LanguageManager  LANGUAGE_GERMAN -com.ainative.mountainsurvival.LanguageManager  LANGUAGE_HINDI -com.ainative.mountainsurvival.LanguageManager  LANGUAGE_INDONESIAN -com.ainative.mountainsurvival.LanguageManager  LANGUAGE_ITALIAN -com.ainative.mountainsurvival.LanguageManager  LANGUAGE_JAPANESE -com.ainative.mountainsurvival.LanguageManager  LANGUAGE_KOREAN -com.ainative.mountainsurvival.LanguageManager  LANGUAGE_MALAY -com.ainative.mountainsurvival.LanguageManager  LANGUAGE_PORTUGUESE -com.ainative.mountainsurvival.LanguageManager  LANGUAGE_RUSSIAN -com.ainative.mountainsurvival.LanguageManager  LANGUAGE_SIMPLIFIED_CHINESE -com.ainative.mountainsurvival.LanguageManager  LANGUAGE_SPANISH -com.ainative.mountainsurvival.LanguageManager  
LANGUAGE_THAI -com.ainative.mountainsurvival.LanguageManager  LANGUAGE_TRADITIONAL_CHINESE -com.ainative.mountainsurvival.LanguageManager  LANGUAGE_VIETNAMESE -com.ainative.mountainsurvival.LanguageManager  Locale -com.ainative.mountainsurvival.LanguageManager  Log -com.ainative.mountainsurvival.LanguageManager  
PREFS_NAME -com.ainative.mountainsurvival.LanguageManager  R -com.ainative.mountainsurvival.LanguageManager  TAG -com.ainative.mountainsurvival.LanguageManager  
applyLanguage -com.ainative.mountainsurvival.LanguageManager  getCurrentLanguage -com.ainative.mountainsurvival.LanguageManager  getLanguageDisplayName -com.ainative.mountainsurvival.LanguageManager  getSupportedLanguages -com.ainative.mountainsurvival.LanguageManager  initializeLanguage -com.ainative.mountainsurvival.LanguageManager  listOf -com.ainative.mountainsurvival.LanguageManager  setLanguage -com.ainative.mountainsurvival.LanguageManager  ActivityMainBinding *com.ainative.mountainsurvival.MainActivity  	AdManager *com.ainative.mountainsurvival.MainActivity  AlertDialog *com.ainative.mountainsurvival.MainActivity  Any *com.ainative.mountainsurvival.MainActivity  Boolean *com.ainative.mountainsurvival.MainActivity  Bundle *com.ainative.mountainsurvival.MainActivity  Choice *com.ainative.mountainsurvival.MainActivity  	Companion *com.ainative.mountainsurvival.MainActivity  Double *com.ainative.mountainsurvival.MainActivity  	Exception *com.ainative.mountainsurvival.MainActivity  Float *com.ainative.mountainsurvival.MainActivity  
GameEngine *com.ainative.mountainsurvival.MainActivity  	GameEvent *com.ainative.mountainsurvival.MainActivity  GameEventUtils *com.ainative.mountainsurvival.MainActivity  GameLoopTest *com.ainative.mountainsurvival.MainActivity  GameManager *com.ainative.mountainsurvival.MainActivity  	GameState *com.ainative.mountainsurvival.MainActivity  GameUIState *com.ainative.mountainsurvival.MainActivity  Int *com.ainative.mountainsurvival.MainActivity  LanguageManager *com.ainative.mountainsurvival.MainActivity  List *com.ainative.mountainsurvival.MainActivity  Log *com.ainative.mountainsurvival.MainActivity  Long *com.ainative.mountainsurvival.MainActivity  Map *com.ainative.mountainsurvival.MainActivity  MusicManager *com.ainative.mountainsurvival.MainActivity  Pair *com.ainative.mountainsurvival.MainActivity  R *com.ainative.mountainsurvival.MainActivity  String *com.ainative.mountainsurvival.MainActivity  TAG *com.ainative.mountainsurvival.MainActivity  Triple *com.ainative.mountainsurvival.MainActivity  View *com.ainative.mountainsurvival.MainActivity  any *com.ainative.mountainsurvival.MainActivity  applyChoice *com.ainative.mountainsurvival.MainActivity  areButtonListenersSet *com.ainative.mountainsurvival.MainActivity  backupValidGameState *com.ainative.mountainsurvival.MainActivity  binding *com.ainative.mountainsurvival.MainActivity  
checkGameOver *com.ainative.mountainsurvival.MainActivity  coerceAtMost *com.ainative.mountainsurvival.MainActivity  
component1 *com.ainative.mountainsurvival.MainActivity  
component2 *com.ainative.mountainsurvival.MainActivity  contains *com.ainative.mountainsurvival.MainActivity  convertToInt *com.ainative.mountainsurvival.MainActivity  currentChoices *com.ainative.mountainsurvival.MainActivity  currentEvent *com.ainative.mountainsurvival.MainActivity  displayEvent *com.ainative.mountainsurvival.MainActivity  	emptyList *com.ainative.mountainsurvival.MainActivity  emptyMap *com.ainative.mountainsurvival.MainActivity  ensureButtonsAreClickable *com.ainative.mountainsurvival.MainActivity  evaluateCondition *com.ainative.mountainsurvival.MainActivity  evaluateConditionalEvent *com.ainative.mountainsurvival.MainActivity  eventMap *com.ainative.mountainsurvival.MainActivity  filter *com.ainative.mountainsurvival.MainActivity  finish *com.ainative.mountainsurvival.MainActivity  forEachIndexed *com.ainative.mountainsurvival.MainActivity  forceNightPhase *com.ainative.mountainsurvival.MainActivity  gameUIState *com.ainative.mountainsurvival.MainActivity  generateNightSettlementText *com.ainative.mountainsurvival.MainActivity  generateStateChangesText *com.ainative.mountainsurvival.MainActivity  getAvailableChoices *com.ainative.mountainsurvival.MainActivity  getEvent *com.ainative.mountainsurvival.MainActivity  getEventMapStatistics *com.ainative.mountainsurvival.MainActivity  getGameOverReason *com.ainative.mountainsurvival.MainActivity  	getString *com.ainative.mountainsurvival.MainActivity  giveResourceReward *com.ainative.mountainsurvival.MainActivity  handleChoiceClick *com.ainative.mountainsurvival.MainActivity  handleContinueClick *com.ainative.mountainsurvival.MainActivity  handleFoodChoice *com.ainative.mountainsurvival.MainActivity  handleFoodContinueClick *com.ainative.mountainsurvival.MainActivity  handleNightContinueClick *com.ainative.mountainsurvival.MainActivity  handleNormalChoice *com.ainative.mountainsurvival.MainActivity  indices *com.ainative.mountainsurvival.MainActivity  
initialize *com.ainative.mountainsurvival.MainActivity  initializeGame *com.ainative.mountainsurvival.MainActivity  initializeLanguage *com.ainative.mountainsurvival.MainActivity  initializeStateTransitionDP *com.ainative.mountainsurvival.MainActivity  isChoiceAvailable *com.ainative.mountainsurvival.MainActivity  isNightEvent *com.ainative.mountainsurvival.MainActivity  
isNotEmpty *com.ainative.mountainsurvival.MainActivity  
isNullOrEmpty *com.ainative.mountainsurvival.MainActivity  	isVictory *com.ainative.mountainsurvival.MainActivity  joinToString *com.ainative.mountainsurvival.MainActivity  lastValidCurrentChoices *com.ainative.mountainsurvival.MainActivity  lastValidCurrentEvent *com.ainative.mountainsurvival.MainActivity  lastValidGameState *com.ainative.mountainsurvival.MainActivity  lastValidGameUIState *com.ainative.mountainsurvival.MainActivity  layoutInflater *com.ainative.mountainsurvival.MainActivity  let *com.ainative.mountainsurvival.MainActivity  listOf *com.ainative.mountainsurvival.MainActivity  
loadEvents *com.ainative.mountainsurvival.MainActivity  mapOf *com.ainative.mountainsurvival.MainActivity  
mutableListOf *com.ainative.mountainsurvival.MainActivity  mutableMapOf *com.ainative.mountainsurvival.MainActivity  pauseBackgroundMusic *com.ainative.mountainsurvival.MainActivity  
pendingChoice *com.ainative.mountainsurvival.MainActivity  pendingNightEventId *com.ainative.mountainsurvival.MainActivity  performNightPhase *com.ainative.mountainsurvival.MainActivity  performNightPhaseAndCheck *com.ainative.mountainsurvival.MainActivity  preloadRewardVideo *com.ainative.mountainsurvival.MainActivity  proceedToNextDay *com.ainative.mountainsurvival.MainActivity  processNextStep *com.ainative.mountainsurvival.MainActivity  	resetGame *com.ainative.mountainsurvival.MainActivity  restartGame *com.ainative.mountainsurvival.MainActivity  resumeBackgroundMusic *com.ainative.mountainsurvival.MainActivity  reviveCharacter *com.ainative.mountainsurvival.MainActivity  run *com.ainative.mountainsurvival.MainActivity  saveGameStateForRevive *com.ainative.mountainsurvival.MainActivity  savedCurrentChoices *com.ainative.mountainsurvival.MainActivity  savedCurrentEvent *com.ainative.mountainsurvival.MainActivity  savedGameState *com.ainative.mountainsurvival.MainActivity  savedGameUIState *com.ainative.mountainsurvival.MainActivity  set *com.ainative.mountainsurvival.MainActivity  setContentView *com.ainative.mountainsurvival.MainActivity  setGameState *com.ainative.mountainsurvival.MainActivity  setupChoiceButtons *com.ainative.mountainsurvival.MainActivity  setupClickListeners *com.ainative.mountainsurvival.MainActivity  shouldEnterFoodPhase *com.ainative.mountainsurvival.MainActivity  showAdErrorDialog *com.ainative.mountainsurvival.MainActivity  showAdForResource *com.ainative.mountainsurvival.MainActivity  showContinueButton *com.ainative.mountainsurvival.MainActivity  showFailureButtons *com.ainative.mountainsurvival.MainActivity  showFailureScreen *com.ainative.mountainsurvival.MainActivity  showFoodChoicePhase *com.ainative.mountainsurvival.MainActivity  showGameOverDialog *com.ainative.mountainsurvival.MainActivity  showNightPhaseResult *com.ainative.mountainsurvival.MainActivity  showReviveAd *com.ainative.mountainsurvival.MainActivity  showReviveSuccessDialog *com.ainative.mountainsurvival.MainActivity  showRewardDialog *com.ainative.mountainsurvival.MainActivity  showRewardVideoForResource *com.ainative.mountainsurvival.MainActivity  showVictoryButtons *com.ainative.mountainsurvival.MainActivity  showVictoryScreen *com.ainative.mountainsurvival.MainActivity  split *com.ainative.mountainsurvival.MainActivity  startBackgroundMusic *com.ainative.mountainsurvival.MainActivity  stateTransitionDP *com.ainative.mountainsurvival.MainActivity  stopBackgroundMusic *com.ainative.mountainsurvival.MainActivity  take *com.ainative.mountainsurvival.MainActivity  testGameLoop *com.ainative.mountainsurvival.MainActivity  to *com.ainative.mountainsurvival.MainActivity  toIntOrNull *com.ainative.mountainsurvival.MainActivity  toList *com.ainative.mountainsurvival.MainActivity  toMutableMap *com.ainative.mountainsurvival.MainActivity  toMutableSet *com.ainative.mountainsurvival.MainActivity  transitionState *com.ainative.mountainsurvival.MainActivity  trim *com.ainative.mountainsurvival.MainActivity  updateUI *com.ainative.mountainsurvival.MainActivity  validateEventMap *com.ainative.mountainsurvival.MainActivity  
AdCallback 4com.ainative.mountainsurvival.MainActivity.AdManager  ResourceType 4com.ainative.mountainsurvival.MainActivity.AdManager  ReviveAdCallback 4com.ainative.mountainsurvival.MainActivity.AdManager  ActivityMainBinding 4com.ainative.mountainsurvival.MainActivity.Companion  	AdManager 4com.ainative.mountainsurvival.MainActivity.Companion  AlertDialog 4com.ainative.mountainsurvival.MainActivity.Companion  Choice 4com.ainative.mountainsurvival.MainActivity.Companion  
GameEngine 4com.ainative.mountainsurvival.MainActivity.Companion  GameEventUtils 4com.ainative.mountainsurvival.MainActivity.Companion  GameLoopTest 4com.ainative.mountainsurvival.MainActivity.Companion  GameManager 4com.ainative.mountainsurvival.MainActivity.Companion  	GameState 4com.ainative.mountainsurvival.MainActivity.Companion  GameUIState 4com.ainative.mountainsurvival.MainActivity.Companion  LanguageManager 4com.ainative.mountainsurvival.MainActivity.Companion  Log 4com.ainative.mountainsurvival.MainActivity.Companion  MusicManager 4com.ainative.mountainsurvival.MainActivity.Companion  Pair 4com.ainative.mountainsurvival.MainActivity.Companion  R 4com.ainative.mountainsurvival.MainActivity.Companion  TAG 4com.ainative.mountainsurvival.MainActivity.Companion  Triple 4com.ainative.mountainsurvival.MainActivity.Companion  View 4com.ainative.mountainsurvival.MainActivity.Companion  any 4com.ainative.mountainsurvival.MainActivity.Companion  applyChoice 4com.ainative.mountainsurvival.MainActivity.Companion  
checkGameOver 4com.ainative.mountainsurvival.MainActivity.Companion  coerceAtMost 4com.ainative.mountainsurvival.MainActivity.Companion  
component1 4com.ainative.mountainsurvival.MainActivity.Companion  
component2 4com.ainative.mountainsurvival.MainActivity.Companion  contains 4com.ainative.mountainsurvival.MainActivity.Companion  	emptyList 4com.ainative.mountainsurvival.MainActivity.Companion  emptyMap 4com.ainative.mountainsurvival.MainActivity.Companion  filter 4com.ainative.mountainsurvival.MainActivity.Companion  forEachIndexed 4com.ainative.mountainsurvival.MainActivity.Companion  getEvent 4com.ainative.mountainsurvival.MainActivity.Companion  getEventMapStatistics 4com.ainative.mountainsurvival.MainActivity.Companion  getGameOverReason 4com.ainative.mountainsurvival.MainActivity.Companion  	getString 4com.ainative.mountainsurvival.MainActivity.Companion  giveResourceReward 4com.ainative.mountainsurvival.MainActivity.Companion  indices 4com.ainative.mountainsurvival.MainActivity.Companion  
initialize 4com.ainative.mountainsurvival.MainActivity.Companion  initializeGame 4com.ainative.mountainsurvival.MainActivity.Companion  initializeLanguage 4com.ainative.mountainsurvival.MainActivity.Companion  isChoiceAvailable 4com.ainative.mountainsurvival.MainActivity.Companion  
isNotEmpty 4com.ainative.mountainsurvival.MainActivity.Companion  
isNullOrEmpty 4com.ainative.mountainsurvival.MainActivity.Companion  	isVictory 4com.ainative.mountainsurvival.MainActivity.Companion  joinToString 4com.ainative.mountainsurvival.MainActivity.Companion  let 4com.ainative.mountainsurvival.MainActivity.Companion  listOf 4com.ainative.mountainsurvival.MainActivity.Companion  
loadEvents 4com.ainative.mountainsurvival.MainActivity.Companion  mapOf 4com.ainative.mountainsurvival.MainActivity.Companion  
mutableListOf 4com.ainative.mountainsurvival.MainActivity.Companion  mutableMapOf 4com.ainative.mountainsurvival.MainActivity.Companion  pauseBackgroundMusic 4com.ainative.mountainsurvival.MainActivity.Companion  performNightPhase 4com.ainative.mountainsurvival.MainActivity.Companion  preloadRewardVideo 4com.ainative.mountainsurvival.MainActivity.Companion  	resetGame 4com.ainative.mountainsurvival.MainActivity.Companion  resumeBackgroundMusic 4com.ainative.mountainsurvival.MainActivity.Companion  reviveCharacter 4com.ainative.mountainsurvival.MainActivity.Companion  run 4com.ainative.mountainsurvival.MainActivity.Companion  set 4com.ainative.mountainsurvival.MainActivity.Companion  setGameState 4com.ainative.mountainsurvival.MainActivity.Companion  showAdErrorDialog 4com.ainative.mountainsurvival.MainActivity.Companion  showReviveAd 4com.ainative.mountainsurvival.MainActivity.Companion  showReviveSuccessDialog 4com.ainative.mountainsurvival.MainActivity.Companion  showRewardDialog 4com.ainative.mountainsurvival.MainActivity.Companion  showRewardVideoForResource 4com.ainative.mountainsurvival.MainActivity.Companion  split 4com.ainative.mountainsurvival.MainActivity.Companion  startBackgroundMusic 4com.ainative.mountainsurvival.MainActivity.Companion  stopBackgroundMusic 4com.ainative.mountainsurvival.MainActivity.Companion  take 4com.ainative.mountainsurvival.MainActivity.Companion  testGameLoop 4com.ainative.mountainsurvival.MainActivity.Companion  to 4com.ainative.mountainsurvival.MainActivity.Companion  toIntOrNull 4com.ainative.mountainsurvival.MainActivity.Companion  toList 4com.ainative.mountainsurvival.MainActivity.Companion  toMutableMap 4com.ainative.mountainsurvival.MainActivity.Companion  toMutableSet 4com.ainative.mountainsurvival.MainActivity.Companion  trim 4com.ainative.mountainsurvival.MainActivity.Companion  validateEventMap 4com.ainative.mountainsurvival.MainActivity.Companion  ChoiceResult 6com.ainative.mountainsurvival.MainActivity.GameManager  NightPhaseResult 6com.ainative.mountainsurvival.MainActivity.GameManager  FOOD_CHOICE 6com.ainative.mountainsurvival.MainActivity.GameUIState  FOOD_WAITING_CONTINUE 6com.ainative.mountainsurvival.MainActivity.GameUIState  	GAME_OVER 6com.ainative.mountainsurvival.MainActivity.GameUIState  NIGHT_PHASE 6com.ainative.mountainsurvival.MainActivity.GameUIState  NIGHT_WAITING_CONTINUE 6com.ainative.mountainsurvival.MainActivity.GameUIState  
NORMAL_CHOICE 6com.ainative.mountainsurvival.MainActivity.GameUIState  WAITING_CONTINUE 6com.ainative.mountainsurvival.MainActivity.GameUIState  APP_KEY 9com.ainative.mountainsurvival.MountainSurvivalApplication  	AdManager 9com.ainative.mountainsurvival.MountainSurvivalApplication  Boolean 9com.ainative.mountainsurvival.MountainSurvivalApplication  Build 9com.ainative.mountainsurvival.MountainSurvivalApplication  	Companion 9com.ainative.mountainsurvival.MountainSurvivalApplication  	Exception 9com.ainative.mountainsurvival.MountainSurvivalApplication  LanguageManager 9com.ainative.mountainsurvival.MountainSurvivalApplication  Log 9com.ainative.mountainsurvival.MountainSurvivalApplication  OSETInitListener 9com.ainative.mountainsurvival.MountainSurvivalApplication  OSETSDK 9com.ainative.mountainsurvival.MountainSurvivalApplication  PrivacyComplianceManager 9com.ainative.mountainsurvival.MountainSurvivalApplication  REWARD_VIDEO_AD_ID 9com.ainative.mountainsurvival.MountainSurvivalApplication  String 9com.ainative.mountainsurvival.MountainSurvivalApplication  TAG 9com.ainative.mountainsurvival.MountainSurvivalApplication  WebView 9com.ainative.mountainsurvival.MountainSurvivalApplication  android 9com.ainative.mountainsurvival.MountainSurvivalApplication  getCurrentLanguage 9com.ainative.mountainsurvival.MountainSurvivalApplication  getProcessName 9com.ainative.mountainsurvival.MountainSurvivalApplication  	initAdSDK 9com.ainative.mountainsurvival.MountainSurvivalApplication  initializeLanguage 9com.ainative.mountainsurvival.MountainSurvivalApplication  isAdSDKInitialized 9com.ainative.mountainsurvival.MountainSurvivalApplication  let 9com.ainative.mountainsurvival.MountainSurvivalApplication  
logSDKNote 9com.ainative.mountainsurvival.MountainSurvivalApplication  preloadRewardedAd 9com.ainative.mountainsurvival.MountainSurvivalApplication  APP_KEY Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  	AdManager Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  Build Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  LanguageManager Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  Log Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  OSETSDK Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  PrivacyComplianceManager Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  REWARD_VIDEO_AD_ID Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  TAG Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  WebView Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  getCurrentLanguage Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  getProcessName Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  initializeLanguage Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  isAdSDKInitialized Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  let Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  
logSDKNote Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  preloadRewardedAd Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  content Acom.ainative.mountainsurvival.MountainSurvivalApplication.android  Context Icom.ainative.mountainsurvival.MountainSurvivalApplication.android.content  Context *com.ainative.mountainsurvival.MusicManager  KEY_MUSIC_ENABLED *com.ainative.mountainsurvival.MusicManager  Log *com.ainative.mountainsurvival.MusicManager  MediaPlayer *com.ainative.mountainsurvival.MusicManager  
PREFS_NAME *com.ainative.mountainsurvival.MusicManager  R *com.ainative.mountainsurvival.MusicManager  TAG *com.ainative.mountainsurvival.MusicManager  
initialize *com.ainative.mountainsurvival.MusicManager  
isInitialized *com.ainative.mountainsurvival.MusicManager  isMusicEnabled *com.ainative.mountainsurvival.MusicManager  let *com.ainative.mountainsurvival.MusicManager  mediaPlayer *com.ainative.mountainsurvival.MusicManager  pauseBackgroundMusic *com.ainative.mountainsurvival.MusicManager  resumeBackgroundMusic *com.ainative.mountainsurvival.MusicManager  startBackgroundMusic *com.ainative.mountainsurvival.MusicManager  stopBackgroundMusic *com.ainative.mountainsurvival.MusicManager  toggleMusic *com.ainative.mountainsurvival.MusicManager  Log 0com.ainative.mountainsurvival.PrivacyAuditLogger  PrivacyComplianceManager 0com.ainative.mountainsurvival.PrivacyAuditLogger  
StringBuilder 0com.ainative.mountainsurvival.PrivacyAuditLogger  TAG 0com.ainative.mountainsurvival.PrivacyAuditLogger  generateComplianceReport 0com.ainative.mountainsurvival.PrivacyAuditLogger  isPrivacyPolicyAgreed 0com.ainative.mountainsurvival.PrivacyAuditLogger  logPrivacyPolicyStatusChange 0com.ainative.mountainsurvival.PrivacyAuditLogger  Context 6com.ainative.mountainsurvival.PrivacyComplianceManager  Log 6com.ainative.mountainsurvival.PrivacyComplianceManager  PrivacyAuditLogger 6com.ainative.mountainsurvival.PrivacyComplianceManager  TAG 6com.ainative.mountainsurvival.PrivacyComplianceManager  isPrivacyPolicyAgreed 6com.ainative.mountainsurvival.PrivacyComplianceManager  logPrivacyPolicyStatusChange 6com.ainative.mountainsurvival.PrivacyComplianceManager  
logSDKNote 6com.ainative.mountainsurvival.PrivacyComplianceManager  ActivityPrivacyPolicyBinding 3com.ainative.mountainsurvival.PrivacyPolicyActivity  Bundle 3com.ainative.mountainsurvival.PrivacyPolicyActivity  
ClickableSpan 3com.ainative.mountainsurvival.PrivacyPolicyActivity  	Companion 3com.ainative.mountainsurvival.PrivacyPolicyActivity  	Exception 3com.ainative.mountainsurvival.PrivacyPolicyActivity  ForegroundColorSpan 3com.ainative.mountainsurvival.PrivacyPolicyActivity  Intent 3com.ainative.mountainsurvival.PrivacyPolicyActivity  LinkMovementMethod 3com.ainative.mountainsurvival.PrivacyPolicyActivity  Log 3com.ainative.mountainsurvival.PrivacyPolicyActivity  MODE_PRIVATE 3com.ainative.mountainsurvival.PrivacyPolicyActivity  PRIVACY_POLICY_URL 3com.ainative.mountainsurvival.PrivacyPolicyActivity  R 3com.ainative.mountainsurvival.PrivacyPolicyActivity  RESULT_CANCELED 3com.ainative.mountainsurvival.PrivacyPolicyActivity  	RESULT_OK 3com.ainative.mountainsurvival.PrivacyPolicyActivity  SpannableString 3com.ainative.mountainsurvival.PrivacyPolicyActivity  Spanned 3com.ainative.mountainsurvival.PrivacyPolicyActivity  System 3com.ainative.mountainsurvival.PrivacyPolicyActivity  TAG 3com.ainative.mountainsurvival.PrivacyPolicyActivity  Uri 3com.ainative.mountainsurvival.PrivacyPolicyActivity  View 3com.ainative.mountainsurvival.PrivacyPolicyActivity  agreePrivacyPolicy 3com.ainative.mountainsurvival.PrivacyPolicyActivity  android 3com.ainative.mountainsurvival.PrivacyPolicyActivity  binding 3com.ainative.mountainsurvival.PrivacyPolicyActivity  disagreePrivacyPolicy 3com.ainative.mountainsurvival.PrivacyPolicyActivity  finish 3com.ainative.mountainsurvival.PrivacyPolicyActivity  getSharedPreferences 3com.ainative.mountainsurvival.PrivacyPolicyActivity  	getString 3com.ainative.mountainsurvival.PrivacyPolicyActivity  indexOf 3com.ainative.mountainsurvival.PrivacyPolicyActivity  layoutInflater 3com.ainative.mountainsurvival.PrivacyPolicyActivity  openPrivacyPolicyUrl 3com.ainative.mountainsurvival.PrivacyPolicyActivity  	resources 3com.ainative.mountainsurvival.PrivacyPolicyActivity  setContentView 3com.ainative.mountainsurvival.PrivacyPolicyActivity  	setResult 3com.ainative.mountainsurvival.PrivacyPolicyActivity  setupClickListeners 3com.ainative.mountainsurvival.PrivacyPolicyActivity  setupPrivacyPolicyContent 3com.ainative.mountainsurvival.PrivacyPolicyActivity  
startActivity 3com.ainative.mountainsurvival.PrivacyPolicyActivity  supportActionBar 3com.ainative.mountainsurvival.PrivacyPolicyActivity  ActivityPrivacyPolicyBinding =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  ForegroundColorSpan =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  Intent =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  LinkMovementMethod =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  Log =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  MODE_PRIVATE =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  PRIVACY_POLICY_URL =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  R =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  RESULT_CANCELED =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  	RESULT_OK =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  SpannableString =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  Spanned =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  System =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  TAG =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  Uri =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  android =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  indexOf =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  openPrivacyPolicyUrl =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  title (com.ainative.mountainsurvival.R.drawable  title_en (com.ainative.mountainsurvival.R.drawable  winter #com.ainative.mountainsurvival.R.raw  ad_load_failed_message &com.ainative.mountainsurvival.R.string  ad_load_failed_title &com.ainative.mountainsurvival.R.string  ad_loading_message &com.ainative.mountainsurvival.R.string  ad_loading_title &com.ainative.mountainsurvival.R.string  	ad_revive &com.ainative.mountainsurvival.R.string  ad_show_failed_message &com.ainative.mountainsurvival.R.string  ad_show_failed_title &com.ainative.mountainsurvival.R.string  
cabin_damaged &com.ainative.mountainsurvival.R.string  cabin_good_condition &com.ainative.mountainsurvival.R.string  cabin_improved &com.ainative.mountainsurvival.R.string  cabin_poor_condition &com.ainative.mountainsurvival.R.string  challenge_again &com.ainative.mountainsurvival.R.string  choice_effect_text &com.ainative.mountainsurvival.R.string  choice_made &com.ainative.mountainsurvival.R.string  confirm &com.ainative.mountainsurvival.R.string  continue_button &com.ainative.mountainsurvival.R.string  
continue_game &com.ainative.mountainsurvival.R.string  defeat_despair_content &com.ainative.mountainsurvival.R.string  defeat_despair_stats &com.ainative.mountainsurvival.R.string  defeat_despair_title &com.ainative.mountainsurvival.R.string  defeat_exhaustion_content &com.ainative.mountainsurvival.R.string  defeat_exhaustion_title &com.ainative.mountainsurvival.R.string  defeat_generic_content &com.ainative.mountainsurvival.R.string  defeat_generic_title &com.ainative.mountainsurvival.R.string  defeat_hypothermia_content &com.ainative.mountainsurvival.R.string  defeat_hypothermia_title &com.ainative.mountainsurvival.R.string  defeat_stats_format &com.ainative.mountainsurvival.R.string  eat_food &com.ainative.mountainsurvival.R.string  eat_food_result &com.ainative.mountainsurvival.R.string  environment_effects &com.ainative.mountainsurvival.R.string  event_effect &com.ainative.mountainsurvival.R.string  event_not_found &com.ainative.mountainsurvival.R.string  
firewood_name &com.ainative.mountainsurvival.R.string  	food_name &com.ainative.mountainsurvival.R.string  food_phase_description &com.ainative.mountainsurvival.R.string  food_phase_title &com.ainative.mountainsurvival.R.string  game_data_load_failed &com.ainative.mountainsurvival.R.string  high_morale_effect &com.ainative.mountainsurvival.R.string  hope_greatly_decreased &com.ainative.mountainsurvival.R.string  hope_greatly_increased &com.ainative.mountainsurvival.R.string  hope_slightly_decreased &com.ainative.mountainsurvival.R.string  hope_slightly_increased &com.ainative.mountainsurvival.R.string  language_cancel &com.ainative.mountainsurvival.R.string  language_english &com.ainative.mountainsurvival.R.string  language_french &com.ainative.mountainsurvival.R.string  language_german &com.ainative.mountainsurvival.R.string  language_hindi &com.ainative.mountainsurvival.R.string  language_indonesian &com.ainative.mountainsurvival.R.string  language_italian &com.ainative.mountainsurvival.R.string  language_japanese &com.ainative.mountainsurvival.R.string  language_korean &com.ainative.mountainsurvival.R.string  language_malay &com.ainative.mountainsurvival.R.string  language_portuguese &com.ainative.mountainsurvival.R.string  language_russian &com.ainative.mountainsurvival.R.string  language_selection_title &com.ainative.mountainsurvival.R.string  language_simplified_chinese &com.ainative.mountainsurvival.R.string  language_spanish &com.ainative.mountainsurvival.R.string  
language_thai &com.ainative.mountainsurvival.R.string  language_traditional_chinese &com.ainative.mountainsurvival.R.string  language_vietnamese &com.ainative.mountainsurvival.R.string  leave_mountain &com.ainative.mountainsurvival.R.string  low_morale_effect &com.ainative.mountainsurvival.R.string  	music_off &com.ainative.mountainsurvival.R.string  music_on &com.ainative.mountainsurvival.R.string  new_day_generic &com.ainative.mountainsurvival.R.string  night_firewood_used &com.ainative.mountainsurvival.R.string  night_no_firewood &com.ainative.mountainsurvival.R.string  night_phase_title &com.ainative.mountainsurvival.R.string  night_roof_leaking &com.ainative.mountainsurvival.R.string  night_settlement &com.ainative.mountainsurvival.R.string  night_stamina_lost &com.ainative.mountainsurvival.R.string  night_warmth_gained &com.ainative.mountainsurvival.R.string  night_warmth_lost &com.ainative.mountainsurvival.R.string  night_with_firewood &com.ainative.mountainsurvival.R.string  night_without_firewood &com.ainative.mountainsurvival.R.string  no_choice_continue &com.ainative.mountainsurvival.R.string  	ok_button &com.ainative.mountainsurvival.R.string  privacy_policy_content &com.ainative.mountainsurvival.R.string  privacy_policy_link_text &com.ainative.mountainsurvival.R.string  rest_indoors &com.ainative.mountainsurvival.R.string  restart_challenge &com.ainative.mountainsurvival.R.string  revive_ad_load_failed_message &com.ainative.mountainsurvival.R.string  revive_ad_loading_message &com.ainative.mountainsurvival.R.string  revive_ad_loading_title &com.ainative.mountainsurvival.R.string  revive_ad_show_failed_message &com.ainative.mountainsurvival.R.string  revive_success_generic &com.ainative.mountainsurvival.R.string  revive_success_message &com.ainative.mountainsurvival.R.string  revive_success_title &com.ainative.mountainsurvival.R.string  reward_message &com.ainative.mountainsurvival.R.string  reward_title &com.ainative.mountainsurvival.R.string  	save_food &com.ainative.mountainsurvival.R.string  save_food_result &com.ainative.mountainsurvival.R.string  search_resources &com.ainative.mountainsurvival.R.string  stamina_name &com.ainative.mountainsurvival.R.string  status_change_prefix &com.ainative.mountainsurvival.R.string  victory_barely_survived_content &com.ainative.mountainsurvival.R.string  victory_barely_survived_stats &com.ainative.mountainsurvival.R.string  victory_barely_survived_title &com.ainative.mountainsurvival.R.string  victory_perfect_rescue_content &com.ainative.mountainsurvival.R.string  victory_perfect_rescue_stats &com.ainative.mountainsurvival.R.string  victory_perfect_rescue_title &com.ainative.mountainsurvival.R.string  victory_stats_format &com.ainative.mountainsurvival.R.string  victory_strong_will_content &com.ainative.mountainsurvival.R.string  victory_strong_will_stats &com.ainative.mountainsurvival.R.string  victory_strong_will_title &com.ainative.mountainsurvival.R.string  #victory_successful_survival_content &com.ainative.mountainsurvival.R.string  !victory_successful_survival_stats &com.ainative.mountainsurvival.R.string  !victory_successful_survival_title &com.ainative.mountainsurvival.R.string  warmth_name &com.ainative.mountainsurvival.R.string  ActivityStartBinding +com.ainative.mountainsurvival.StartActivity  AlertDialog +com.ainative.mountainsurvival.StartActivity  Boolean +com.ainative.mountainsurvival.StartActivity  Bundle +com.ainative.mountainsurvival.StartActivity  	Exception +com.ainative.mountainsurvival.StartActivity  Int +com.ainative.mountainsurvival.StartActivity  Intent +com.ainative.mountainsurvival.StartActivity  LanguageManager +com.ainative.mountainsurvival.StartActivity  Log +com.ainative.mountainsurvival.StartActivity  MODE_PRIVATE +com.ainative.mountainsurvival.StartActivity  MainActivity +com.ainative.mountainsurvival.StartActivity  MountainSurvivalApplication +com.ainative.mountainsurvival.StartActivity  MusicManager +com.ainative.mountainsurvival.StartActivity  PRIVACY_POLICY_REQUEST_CODE +com.ainative.mountainsurvival.StartActivity  PrivacyAuditLogger +com.ainative.mountainsurvival.StartActivity  PrivacyPolicyActivity +com.ainative.mountainsurvival.StartActivity  R +com.ainative.mountainsurvival.StartActivity  	RESULT_OK +com.ainative.mountainsurvival.StartActivity  TAG +com.ainative.mountainsurvival.StartActivity  android +com.ainative.mountainsurvival.StartActivity  application +com.ainative.mountainsurvival.StartActivity  binding +com.ainative.mountainsurvival.StartActivity  checkPrivacyPolicyAgreed +com.ainative.mountainsurvival.StartActivity  exitGame +com.ainative.mountainsurvival.StartActivity  finish +com.ainative.mountainsurvival.StartActivity  generateComplianceReport +com.ainative.mountainsurvival.StartActivity  getCurrentLanguage +com.ainative.mountainsurvival.StartActivity  getLanguageDisplayName +com.ainative.mountainsurvival.StartActivity  getSharedPreferences +com.ainative.mountainsurvival.StartActivity  	getString +com.ainative.mountainsurvival.StartActivity  getSupportedLanguages +com.ainative.mountainsurvival.StartActivity  
initialize +com.ainative.mountainsurvival.StartActivity  initializeAdSDK +com.ainative.mountainsurvival.StartActivity  initializeLanguage +com.ainative.mountainsurvival.StartActivity  initializeStartScreen +com.ainative.mountainsurvival.StartActivity  
isInitialized +com.ainative.mountainsurvival.StartActivity  isMusicEnabled +com.ainative.mountainsurvival.StartActivity  java +com.ainative.mountainsurvival.StartActivity  layoutInflater +com.ainative.mountainsurvival.StartActivity  map +com.ainative.mountainsurvival.StartActivity  pauseBackgroundMusic +com.ainative.mountainsurvival.StartActivity  recreate +com.ainative.mountainsurvival.StartActivity  resumeBackgroundMusic +com.ainative.mountainsurvival.StartActivity  setContentView +com.ainative.mountainsurvival.StartActivity  setLanguage +com.ainative.mountainsurvival.StartActivity  setupClickListeners +com.ainative.mountainsurvival.StartActivity  showLanguageSelectionDialog +com.ainative.mountainsurvival.StartActivity  showPrivacyPolicyDialog +com.ainative.mountainsurvival.StartActivity  
startActivity +com.ainative.mountainsurvival.StartActivity  startActivityForResult +com.ainative.mountainsurvival.StartActivity  startBackgroundMusic +com.ainative.mountainsurvival.StartActivity  	startGame +com.ainative.mountainsurvival.StartActivity  stopBackgroundMusic +com.ainative.mountainsurvival.StartActivity  supportActionBar +com.ainative.mountainsurvival.StartActivity  toTypedArray +com.ainative.mountainsurvival.StartActivity  toggleMusic +com.ainative.mountainsurvival.StartActivity  updateMusicButtonText +com.ainative.mountainsurvival.StartActivity  updateTitleImage +com.ainative.mountainsurvival.StartActivity  ActivityStartBinding 5com.ainative.mountainsurvival.StartActivity.Companion  AlertDialog 5com.ainative.mountainsurvival.StartActivity.Companion  Intent 5com.ainative.mountainsurvival.StartActivity.Companion  LanguageManager 5com.ainative.mountainsurvival.StartActivity.Companion  Log 5com.ainative.mountainsurvival.StartActivity.Companion  MODE_PRIVATE 5com.ainative.mountainsurvival.StartActivity.Companion  MainActivity 5com.ainative.mountainsurvival.StartActivity.Companion  MusicManager 5com.ainative.mountainsurvival.StartActivity.Companion  PRIVACY_POLICY_REQUEST_CODE 5com.ainative.mountainsurvival.StartActivity.Companion  PrivacyAuditLogger 5com.ainative.mountainsurvival.StartActivity.Companion  PrivacyPolicyActivity 5com.ainative.mountainsurvival.StartActivity.Companion  R 5com.ainative.mountainsurvival.StartActivity.Companion  	RESULT_OK 5com.ainative.mountainsurvival.StartActivity.Companion  TAG 5com.ainative.mountainsurvival.StartActivity.Companion  android 5com.ainative.mountainsurvival.StartActivity.Companion  generateComplianceReport 5com.ainative.mountainsurvival.StartActivity.Companion  getCurrentLanguage 5com.ainative.mountainsurvival.StartActivity.Companion  getLanguageDisplayName 5com.ainative.mountainsurvival.StartActivity.Companion  getSupportedLanguages 5com.ainative.mountainsurvival.StartActivity.Companion  
initialize 5com.ainative.mountainsurvival.StartActivity.Companion  initializeLanguage 5com.ainative.mountainsurvival.StartActivity.Companion  
isInitialized 5com.ainative.mountainsurvival.StartActivity.Companion  isMusicEnabled 5com.ainative.mountainsurvival.StartActivity.Companion  java 5com.ainative.mountainsurvival.StartActivity.Companion  map 5com.ainative.mountainsurvival.StartActivity.Companion  pauseBackgroundMusic 5com.ainative.mountainsurvival.StartActivity.Companion  resumeBackgroundMusic 5com.ainative.mountainsurvival.StartActivity.Companion  setLanguage 5com.ainative.mountainsurvival.StartActivity.Companion  startBackgroundMusic 5com.ainative.mountainsurvival.StartActivity.Companion  stopBackgroundMusic 5com.ainative.mountainsurvival.StartActivity.Companion  toTypedArray 5com.ainative.mountainsurvival.StartActivity.Companion  toggleMusic 5com.ainative.mountainsurvival.StartActivity.Companion  content %com.ainative.mountainsurvival.android  Context -com.ainative.mountainsurvival.android.content  ActivityMainBinding )com.ainative.mountainsurvival.databinding  ActivityPrivacyPolicyBinding )com.ainative.mountainsurvival.databinding  ActivityStartBinding )com.ainative.mountainsurvival.databinding  
choice1Button =com.ainative.mountainsurvival.databinding.ActivityMainBinding  
choice2Button =com.ainative.mountainsurvival.databinding.ActivityMainBinding  
choice3Button =com.ainative.mountainsurvival.databinding.ActivityMainBinding  
choice4Button =com.ainative.mountainsurvival.databinding.ActivityMainBinding  firewoodPlusButton =com.ainative.mountainsurvival.databinding.ActivityMainBinding  firewoodTextView =com.ainative.mountainsurvival.databinding.ActivityMainBinding  foodPlusButton =com.ainative.mountainsurvival.databinding.ActivityMainBinding  foodTextView =com.ainative.mountainsurvival.databinding.ActivityMainBinding  inflate =com.ainative.mountainsurvival.databinding.ActivityMainBinding  root =com.ainative.mountainsurvival.databinding.ActivityMainBinding  staminaPlusButton =com.ainative.mountainsurvival.databinding.ActivityMainBinding  staminaTextView =com.ainative.mountainsurvival.databinding.ActivityMainBinding  
storyTextView =com.ainative.mountainsurvival.databinding.ActivityMainBinding  warmthPlusButton =com.ainative.mountainsurvival.databinding.ActivityMainBinding  warmthTextView =com.ainative.mountainsurvival.databinding.ActivityMainBinding  agreeButton Fcom.ainative.mountainsurvival.databinding.ActivityPrivacyPolicyBinding  disagreeButton Fcom.ainative.mountainsurvival.databinding.ActivityPrivacyPolicyBinding  inflate Fcom.ainative.mountainsurvival.databinding.ActivityPrivacyPolicyBinding  privacyContentTextView Fcom.ainative.mountainsurvival.databinding.ActivityPrivacyPolicyBinding  root Fcom.ainative.mountainsurvival.databinding.ActivityPrivacyPolicyBinding  exitGameButton >com.ainative.mountainsurvival.databinding.ActivityStartBinding  inflate >com.ainative.mountainsurvival.databinding.ActivityStartBinding  languageButton >com.ainative.mountainsurvival.databinding.ActivityStartBinding  musicToggleButton >com.ainative.mountainsurvival.databinding.ActivityStartBinding  root >com.ainative.mountainsurvival.databinding.ActivityStartBinding  startGameButton >com.ainative.mountainsurvival.databinding.ActivityStartBinding  titleImageView >com.ainative.mountainsurvival.databinding.ActivityStartBinding  Gson com.google.gson  JsonSyntaxException com.google.gson  fromJson com.google.gson.Gson  	TypeToken com.google.gson.reflect  OSETRewardListener com.kc.openset.ad.listener  OSETRewardVideo com.kc.openset.ad.reward  getInstance (com.kc.openset.ad.reward.OSETRewardVideo  
setContext (com.kc.openset.ad.reward.OSETRewardVideo  setPosId (com.kc.openset.ad.reward.OSETRewardVideo  	setUserId (com.kc.openset.ad.reward.OSETRewardVideo  showAd (com.kc.openset.ad.reward.OSETRewardVideo  	startLoad (com.kc.openset.ad.reward.OSETRewardVideo  OSETSDK com.kc.openset.config  getInstance com.kc.openset.config.OSETSDK  init com.kc.openset.config.OSETSDK  isInit com.kc.openset.config.OSETSDK  OSETInitListener com.kc.openset.listener  IOException java.io  InputStream java.io  printStackTrace java.io.IOException  	available java.io.InputStream  close java.io.InputStream  read java.io.InputStream  use java.io.InputStream  Class 	java.lang  	Exception 	java.lang  
StringBuilder 	java.lang  message java.lang.Exception  printStackTrace java.lang.Exception  append java.lang.StringBuilder  toString java.lang.StringBuilder  currentTimeMillis java.lang.System  
BigDecimal 	java.math  
BigInteger 	java.math  Charset java.nio.charset  Boolean 	java.util  
Configuration 	java.util  Context 	java.util  	Exception 	java.util  List 	java.util  Locale 	java.util  Log 	java.util  R 	java.util  String 	java.util  listOf 	java.util  ENGLISH java.util.Locale  FRENCH java.util.Locale  GERMAN java.util.Locale  ITALIAN java.util.Locale  JAPANESE java.util.Locale  KOREAN java.util.Locale  SIMPLIFIED_CHINESE java.util.Locale  TRADITIONAL_CHINESE java.util.Locale  displayName java.util.Locale  Array kotlin  	ByteArray kotlin  CharSequence kotlin  	Function1 kotlin  	Function2 kotlin  Nothing kotlin  Pair kotlin  Result kotlin  String kotlin  Triple kotlin  
isInitialized kotlin  let kotlin  map kotlin  repeat kotlin  run kotlin  to kotlin  toList kotlin  use kotlin  not kotlin.Boolean  isEmpty kotlin.CharSequence  div 
kotlin.Double  toInt 
kotlin.Double  Int kotlin.Enum  String kotlin.Enum  toInt kotlin.Float  
coerceAtLeast 
kotlin.Int  coerceAtMost 
kotlin.Int  coerceIn 
kotlin.Int  	compareTo 
kotlin.Int  inc 
kotlin.Int  minus 
kotlin.Int  minusAssign 
kotlin.Int  plus 
kotlin.Int  
plusAssign 
kotlin.Int  rangeTo 
kotlin.Int  times 
kotlin.Int  toDouble 
kotlin.Int  
unaryMinus 
kotlin.Int  minus kotlin.Long  toInt kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  	Companion 
kotlin.String  contains 
kotlin.String  indexOf 
kotlin.String  invoke 
kotlin.String  isBlank 
kotlin.String  isEmpty 
kotlin.String  
isNotBlank 
kotlin.String  
isNotEmpty 
kotlin.String  length 
kotlin.String  let 
kotlin.String  	lowercase 
kotlin.String  plus 
kotlin.String  split 
kotlin.String  take 
kotlin.String  to 
kotlin.String  toIntOrNull 
kotlin.String  trim 
kotlin.String  invoke kotlin.String.Companion  message kotlin.Throwable  printStackTrace kotlin.Throwable  
component1 
kotlin.Triple  
component2 
kotlin.Triple  
component3 
kotlin.Triple  
Collection kotlin.collections  IntIterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  Set kotlin.collections  any kotlin.collections  average kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  contains kotlin.collections  count kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  filter kotlin.collections  filterIsInstance kotlin.collections  find kotlin.collections  forEach kotlin.collections  forEachIndexed kotlin.collections  indexOf kotlin.collections  indices kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrEmpty kotlin.collections  joinToString kotlin.collections  listOf kotlin.collections  map kotlin.collections  mapOf kotlin.collections  maxOfOrNull kotlin.collections  	maxOrNull kotlin.collections  minOfOrNull kotlin.collections  	minOrNull kotlin.collections  minusAssign kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  mutableSetOf kotlin.collections  
plusAssign kotlin.collections  set kotlin.collections  sumOf kotlin.collections  sumOfInt kotlin.collections  take kotlin.collections  toList kotlin.collections  toMap kotlin.collections  toMutableMap kotlin.collections  toMutableSet kotlin.collections  toTypedArray kotlin.collections  count kotlin.collections.Collection  sumOf kotlin.collections.Collection  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  any kotlin.collections.List  contains kotlin.collections.List  count kotlin.collections.List  filter kotlin.collections.List  filterIsInstance kotlin.collections.List  find kotlin.collections.List  forEachIndexed kotlin.collections.List  get kotlin.collections.List  indexOf kotlin.collections.List  indices kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  
isNullOrEmpty kotlin.collections.List  map kotlin.collections.List  size kotlin.collections.List  sumOf kotlin.collections.List  toList kotlin.collections.List  toTypedArray kotlin.collections.List  Entry kotlin.collections.Map  containsKey kotlin.collections.Map  get kotlin.collections.Map  isEmpty kotlin.collections.Map  
isNotEmpty kotlin.collections.Map  
isNullOrEmpty kotlin.collections.Map  keys kotlin.collections.Map  let kotlin.collections.Map  size kotlin.collections.Map  values kotlin.collections.Map  
component1 kotlin.collections.Map.Entry  
component2 kotlin.collections.Map.Entry  add kotlin.collections.MutableList  addAll kotlin.collections.MutableList  average kotlin.collections.MutableList  clear kotlin.collections.MutableList  get kotlin.collections.MutableList  isEmpty kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  joinToString kotlin.collections.MutableList  maxOfOrNull kotlin.collections.MutableList  	maxOrNull kotlin.collections.MutableList  minOfOrNull kotlin.collections.MutableList  	minOrNull kotlin.collections.MutableList  removeAt kotlin.collections.MutableList  size kotlin.collections.MutableList  toList kotlin.collections.MutableList  containsKey kotlin.collections.MutableMap  get kotlin.collections.MutableMap  set kotlin.collections.MutableMap  size kotlin.collections.MutableMap  toMap kotlin.collections.MutableMap  toMutableMap kotlin.collections.MutableMap  add kotlin.collections.MutableSet  contains kotlin.collections.MutableSet  remove kotlin.collections.MutableSet  size kotlin.collections.MutableSet  toMutableSet kotlin.collections.MutableSet  contains kotlin.collections.Set  use 	kotlin.io  java 
kotlin.jvm  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  
coerceAtLeast 
kotlin.ranges  coerceAtMost 
kotlin.ranges  coerceIn 
kotlin.ranges  contains 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  KMutableProperty0 kotlin.reflect  java kotlin.reflect.KClass  
isInitialized  kotlin.reflect.KMutableProperty0  Sequence kotlin.sequences  any kotlin.sequences  average kotlin.sequences  contains kotlin.sequences  count kotlin.sequences  filter kotlin.sequences  filterIsInstance kotlin.sequences  find kotlin.sequences  forEach kotlin.sequences  forEachIndexed kotlin.sequences  indexOf kotlin.sequences  joinToString kotlin.sequences  map kotlin.sequences  maxOfOrNull kotlin.sequences  	maxOrNull kotlin.sequences  minOfOrNull kotlin.sequences  	minOrNull kotlin.sequences  sumOf kotlin.sequences  take kotlin.sequences  toList kotlin.sequences  toMutableSet kotlin.sequences  Charsets kotlin.text  String kotlin.text  any kotlin.text  contains kotlin.text  count kotlin.text  filter kotlin.text  find kotlin.text  forEach kotlin.text  forEachIndexed kotlin.text  indexOf kotlin.text  indices kotlin.text  isBlank kotlin.text  isEmpty kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  
isNullOrEmpty kotlin.text  	lowercase kotlin.text  map kotlin.text  maxOfOrNull kotlin.text  	maxOrNull kotlin.text  minOfOrNull kotlin.text  	minOrNull kotlin.text  repeat kotlin.text  set kotlin.text  split kotlin.text  sumOf kotlin.text  take kotlin.text  toIntOrNull kotlin.text  toList kotlin.text  trim kotlin.text  UTF_8 kotlin.text.Charsets  contains 'com.ainative.mountainsurvival.AdManager  	AdManager +com.ainative.mountainsurvival.StartActivity  preloadRewardVideo +com.ainative.mountainsurvival.StartActivity  	AdManager 5com.ainative.mountainsurvival.StartActivity.Companion  preloadRewardVideo 5com.ainative.mountainsurvival.StartActivity.Companion                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   