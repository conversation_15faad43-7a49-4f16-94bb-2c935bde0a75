plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
}

android {
    namespace = "com.ainative.mountainsurvival"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.ainative.mountainsurvival"
        minSdk = 24
        targetSdk = 35
        versionCode = 3
        versionName = "1.2.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled = true
    }

    // 国外广告SDK仓库配置
    repositories {
        flatDir {
            dirs("libs")
        }
        //bidmachine
        maven { url = uri("https://artifactory.bidmachine.io/bidmachine") }
        //ironsource
        maven { url = uri("https://android-sdk.is.com") }
        //mintegral
        maven { url = uri("https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_oversea") }
        //pangle
        maven { url = uri("https://artifact.bytedance.com/repository/pangle") }
        //Anythink(Core)
        maven {
            url = uri("https://jfrog.anythinktech.com/artifactory/overseas_sdk")
        }
        // adSet 国外版本
        maven {
            isAllowInsecureProtocol = true
            url = uri("http://maven.shenshiads.com/nexus/repository/adset/")
        }
    }

    signingConfigs {
        create("release") {
            storeFile = file("E:/Ai/AiCode/game/miyao.jks")
            storePassword = "nihaoshijie233"
            keyAlias = "mountainsurvival"
            keyPassword = "js42fiqn"
            // 启用V1和V2签名
            enableV1Signing = true
            enableV2Signing = true
        }
    }

    buildTypes {
        debug {
            isMinifyEnabled = false
            isDebuggable = true
            // Debug版本不使用混淆，加快编译速度
        }

        release {
            // 禁用混淆和资源压缩（保留混淆配置文件以备后用）
            isMinifyEnabled = false
            isShrinkResources = false
            // 保留ProGuard配置文件，如需重新启用混淆，将上面两行改为true即可
            proguardFiles(

                "proguard-ignore-warnings.pro",
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            signingConfig = signingConfigs.getByName("release")

            // R8优化配置，减少wind-sdk警告
            isDebuggable = false
            isJniDebuggable = false
            isPseudoLocalesEnabled = false
        }
    }

    // 编译优化选项
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = "11"
    }
    buildFeatures {
        viewBinding = true
    }
}

dependencies {

    implementation("com.google.code.gson:gson:2.9.0")

    // AndroidX库依赖
    implementation("androidx.appcompat:appcompat:1.6.1")
    implementation("androidx.constraintlayout:constraintlayout:2.1.4")
    implementation("androidx.legacy:legacy-support-v4:1.0.0")
    implementation("com.google.android.material:material:1.9.0")
    implementation("androidx.recyclerview:recyclerview:1.3.1")

    // 神蓍广告SDK - 国外版本（核心SDK）
    //core
    implementation("com.shenshi-hw:ad-openset-sdk:1.4.1.2")

    // 广告渠道适配器 - 按照国外版本文档配置
    // 使用示例项目中的确切版本号

    // 广告适配器依赖问题总结：
    // 1. 所有适配器都依赖 com.anythink.sdk:* 库，但这些库无法从配置的仓库下载
    // 2. 可能的原因：版本不匹配、仓库访问问题、或需要特殊的认证
    // 3. 建议：联系神蓍技术支持获取正确的集成方案

    //BidMachine - 暂时跳过，等待技术支持
    //implementation("com.shenshi-hw:ad-bidmachine-adapter:3.1.1.2")

    //bigo
    //implementation("com.shenshi-hw:ad-bigo-adapter:5.3.0.1")

    //DT(fyber)
    //implementation("com.shenshi-hw:ad-fyber-adapter:8.3.7.1")

    //inmobi
    //implementation("com.shenshi-hw:ad-inmobi-adapter:10.8.2.1")

    //ironsource
    //implementation("com.shenshi-hw:ad-ironsource-adapter:8.7.0.0.1")

    //kwai
    //implementation("com.shenshi-hw:ad-kwai-adapter:1.2.15.2")

    //applovin
    //implementation("com.shenshi-hw:ad-max-adapter:13.3.1.1")

    //mintegral
    //implementation("com.shenshi-hw:ad-mingetral-adapter:16.9.71.1")

    //pangle
    //implementation("com.shenshi-hw:ad-pangle-adapter:7.2.0.6.1")

    //topon(ADX)
    //implementation("com.shenshi-hw:ad-topon-adapter:6.5.08.1")

    //unity - 同样有依赖问题，暂时跳过
    //implementation("com.shenshi-hw:ad-unity-adapter:4.14.0.1")

    //vungle - 暂时跳过
    //implementation("com.shenshi-hw:ad-vungle-adapter:7.5.0.1")

    // 多dex支持
    implementation("androidx.multidex:multidex:2.0.1")

    // 测试依赖
    testImplementation("junit:junit:4.13.2")
    androidTestImplementation("androidx.test:runner:1.5.2")
    androidTestImplementation("androidx.test.espresso:espresso-core:3.5.1")
}