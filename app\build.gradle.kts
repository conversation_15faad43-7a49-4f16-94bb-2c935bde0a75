plugins {
    id("com.android.application")
    id("org.jetbrains.kotlin.android")
}

android {
    namespace = "com.ainative.mountainsurvival"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.ainative.mountainsurvival"
        minSdk = 24
        targetSdk = 35
        versionCode = 3
        versionName = "1.2.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled = true
    }

    signingConfigs {
        create("release") {
            storeFile = file("E:/Ai/AiCode/game/miyao.jks")
            storePassword = "nihaoshijie233"
            keyAlias = "mountainsurvival"
            keyPassword = "js42fiqn"
        }
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            signingConfig = signingConfigs.getByName("release")
        }
        debug {
            isMinifyEnabled = false
            isDebuggable = true
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = "1.8"
    }

    buildFeatures {
        viewBinding = true
        dataBinding = true
    }

    packaging {
        resources {
            pickFirsts += "**/libc++_shared.so"
            pickFirsts += "**/libjsc.so"
            excludes += "META-INF/DEPENDENCIES"
            excludes += "META-INF/LICENSE"
            excludes += "META-INF/LICENSE.txt"
            excludes += "META-INF/license.txt"
            excludes += "META-INF/NOTICE"
            excludes += "META-INF/NOTICE.txt"
            excludes += "META-INF/notice.txt"
            excludes += "META-INF/ASL2.0"
            excludes += "META-INF/*.kotlin_module"
        }
    }
}

dependencies {
    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar"))))

    // AndroidX 核心库
    implementation("androidx.core:core-ktx:1.12.0")
    implementation("androidx.appcompat:appcompat:1.6.1")
    implementation("com.google.android.material:material:1.11.0")
    implementation("androidx.constraintlayout:constraintlayout:2.1.4")
    implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.7.0")
    implementation("androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0")
    implementation("androidx.navigation:navigation-fragment-ktx:2.7.6")
    implementation("androidx.navigation:navigation-ui-ktx:2.7.6")

    // Kotlin
    implementation("org.jetbrains.kotlin:kotlin-stdlib:2.0.21")

    // 协程
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")

    // JSON解析
    implementation("com.google.code.gson:gson:2.10.1")

    // 网络请求
    implementation("com.squareup.okhttp3:okhttp:4.12.0")

    // 图片加载
    implementation("com.github.bumptech.glide:glide:4.16.0")

    // 权限请求 - 暂时移除有问题的依赖
    //implementation("com.permissionx.guolindev:permissionx:1.7.1")

    // 多dex支持
    implementation("androidx.multidex:multidex:2.0.1")

    // 神蓍广告SDK - 国外版本（核心SDK）
    implementation("com.shenshi-hw:ad-openset-sdk:1.4.1.2")

    // 广告渠道适配器 - 暂时移除，专注于修复核心SDK初始化问题
    // 依赖问题确认：第三方依赖库无法解析
    //BidMachine - 依赖无法解析
    //implementation("com.shenshi-hw:ad-bidmachine-adapter:3.1.1.2")

    // 测试依赖
    testImplementation("junit:junit:4.13.2")
    androidTestImplementation("androidx.test:runner:1.5.2")
    androidTestImplementation("androidx.test.espresso:espresso-core:3.5.1")
}

// 添加示例项目中的关键配置 - 强制使用特定版本的Kotlin标准库
configurations.configureEach {
    resolutionStrategy.force("org.jetbrains.kotlin:kotlin-stdlib:2.0.21")
    resolutionStrategy.force("org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21")
    resolutionStrategy.force("org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21")
}
