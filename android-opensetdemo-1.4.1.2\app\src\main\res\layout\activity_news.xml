<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/include_title" />

    <Button
        android:id="@+id/btn_show_activity"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="20dp"
        android:background="@drawable/bg_main_banner"
        android:gravity="center"
        android:text="新闻资讯1"
        android:textColor="@android:color/white" />
    <Button
        android:id="@+id/btn_show_activity_yd"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="20dp"
        android:background="@drawable/bg_main_banner"
        android:gravity="center"
        android:text="新闻资讯2"
        android:textColor="@android:color/white" />
    <Button
        android:id="@+id/btn_show_fragment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="20dp"
        android:background="@drawable/bg_main_banner"
        android:gravity="center"
        android:text="显示新闻资讯1到指定布局"
        android:textColor="@android:color/white" />
    <Button
        android:id="@+id/btn_show_fragment_yd"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="20dp"
        android:background="@drawable/bg_main_banner"
        android:gravity="center"
        android:text="显示新闻资讯2到指定布局"
        android:textColor="@android:color/white" />

    <FrameLayout
        android:id="@+id/fl"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />


</LinearLayout>