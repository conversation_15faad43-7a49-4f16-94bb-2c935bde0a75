package com.test.opensetdemo.ad;

import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.Toast;

//import com.kc.openset.OSETInsert;
import com.kc.openset.ad.intestitial.OSETInterstitial;
import com.kc.openset.ad.listener.OSETInterstitialListener;
import com.test.opensetdemo.BaseActivity;
import com.test.opensetdemo.Common;
import com.test.opensetdemo.R;

public class AdInsertActivity extends BaseActivity {

    private Button btnShow;

    @Override
    public int getLayoutId() {
        return R.layout.activity_insert;
    }

    @Override
    public void initView() {
        initTitle("插屏");
        btnShow = findViewById(R.id.btn_show);
        btnShow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showInsert();
            }
        });
    }

    private void showInsert() {
        OSETInterstitial.getInstance().setContext(this).setPosId(Common.POS_ID_Insert).showAd(this, new OSETInterstitialListener() {
            @Override
            public void onShow() {
                Toast.makeText(activity, "onShow", Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onError(String s, String s1) {
                Toast.makeText(activity, "onError", Toast.LENGTH_SHORT).show();
                Log.e("openseterror", "code:" + s + "----message:" + s1);
            }

            @Override
            public void onClick() {
                Toast.makeText(activity, "onClick", Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onClose() {
                Toast.makeText(activity, "onClose", Toast.LENGTH_SHORT).show();
            }
        });
    }
}
