package com.test.opensetdemo;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.widget.ImageView;

import androidx.annotation.Nullable;

/**
 * use to
 * Created by lc on 2022/2/17.
 */
@SuppressLint("AppCompatCustomView")
public class CustomImageView extends ImageView {

    private float scaleH, scaleW;

    public CustomImageView(Context context) {
        super(context);
    }

    public CustomImageView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        getAttrs(context, attrs);
    }

    public CustomImageView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        getAttrs(context, attrs);
    }

    public void getAttrs(Context context, AttributeSet attrs) {
        if (attrs == null) {
            return;
        }
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.CustomImageView);
        scaleH = ta.getDimension(R.styleable.CustomImageView_scale_h, 0);
        scaleW = ta.getDimension(R.styleable.CustomImageView_scale_w, 0);
        ta.recycle();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        //将最终的宽高设定为容器的宽高
        int w=getMeasuredWidth();
        setMeasuredDimension(w, (int) (w * (scaleH / scaleW)));
    }
}
