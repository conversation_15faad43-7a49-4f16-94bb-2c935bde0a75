package com.test.opensetdemo.utils;

import java.security.MessageDigest;

/**
 * use to
 * Created by lc on 2022/1/4.
 */
public class MD5Utils {
    public static String getMD5(String temp) {
        MessageDigest md5;
        try {
            md5 = MessageDigest.getInstance("MD5");
            byte[] md5Bytes = md5.digest(temp.getBytes());
            StringBuffer hexValue = new StringBuffer();
            for (int i = 0; i < md5Bytes.length; i++) {
                int val = ((int) md5Bytes[i]) & 0xff;
                if (val < 16)
                    hexValue.append("0");
                hexValue.append(Integer.toHexString(val));
            }
            temp = hexValue.toString();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return temp;
    }
}
