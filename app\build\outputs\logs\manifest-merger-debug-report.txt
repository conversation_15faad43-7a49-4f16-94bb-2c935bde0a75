-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:63:9-71:20
INJECTED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:74:9-82:20
	android:grantUriPermissions
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:67:13-47
	android:authorities
		INJECTED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml
		INJECTED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:65:13-64
	android:exported
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:66:13-37
	android:name
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:64:13-62
manifest
ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:2:1-85:12
INJECTED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:2:1-85:12
INJECTED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:2:1-85:12
INJECTED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:2:1-85:12
MERGED from [androidx.databinding:viewbinding:8.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4463e82091f7284071c541968c5d10db\transformed\jetified-viewbinding-8.7.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d5fa9c9e36bc912c1cb51114343e1e1\transformed\material-1.9.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af05a54f90b02ba9d95471b1da475021\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382d7ec708b1c60f6c45469107eb015d\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bde3453fac62cb9a4dc37198ed238829\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d92d08cc2576af245d8b1256d9fdca2b\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57b75fbe4fa53b584c6464086013040e\transformed\recyclerview-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5f4f7301a1ff6ef457f4dce945b82af\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [com.shenshi-hw:ad-openset-sdk:1.4.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb230dea6ce0a027a08476565719bd94\transformed\jetified-ad-openset-sdk-1.4.1.2\AndroidManifest.xml:2:1-13:12
MERGED from [com.github.bumptech.glide:glide:4.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\515e16fdb7b89bef3428a1b9c4124f71\transformed\jetified-glide-4.9.0\AndroidManifest.xml:2:1-12:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c5713d08ffe7a55a2f3e45a8d6da960\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f283c24a6b6e6a9d9c8f6d6be025229\transformed\jetified-activity-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c11679833c96ef1c17ca4cbc527ed75c\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e92958f763458f5f9c0f91c54258fed\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efaac7508b8e96d6b24ab730eb214ba0\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9ceaebcb04953e5af5b7d199355b7\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2b04e21828c7743ee4d1d340169781e\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38579722fe447de7e84370f857230e4e\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d3069d327dba0f285848d09130b847\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3d443da1b2e819987dac1bc7803fdb1\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6050ef2e88814984a91652c058012d6a\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f031e987a77329b9f10b1cc8abf27af8\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1b16c64dbae6be7624dd523479f1420\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\533f5aa970bb5d3672625a605676bde3\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3b14be50ee54593f95bc1d6e842787\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e931b6e179de3f7cfc2b10b9d677ec6\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea74a906e6d60a742bfd51dafa14c250\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6497744bf085ef425e48c8e38fd34341\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e4c61f38368fc54557b0c5a3e9ac7c3\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\869b9d8aff1267fb27988310f947358b\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42d804cca952a9f169877d8dbe807e09\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04369a3a21eff427dc786218164ed8c1\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ddb2eac7d4720622efdbabdb255f04e\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8869cb1353294265edd8ad60076d1bdf\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf6342dd0eb17ccac87a57a41112fa01\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97801c6c507d8e8a69f1aae422f6dcba\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\600b38f565fa40067a69a14564c6c08e\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4612d558030303403a8cfb545289fbdd\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [io.github.aliyun-sls:aliyun-log-android-sdk:2.7.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d0163aaba9a980e61cd3d3add9946e\transformed\jetified-aliyun-log-android-sdk-2.7.13\AndroidManifest.xml:2:1-23:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21323042fb0f26a896866a8b3e30a045\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e481672d098364d8e6c2450e3c89571f\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3c9a3b28f10b45ca7eba0a273fba3f1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d833d99e8e0b8e339661a951759c88a\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5422d7486e2f7babdc4c40344f3b10db\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4a0595eef7e2ce43a0f230fe1f681b8\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d73d3652d6cf20b5df3e528c6d9dbc4b\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8894c91348a93161200a6fccf5692981\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d2e5a8dc39e12408c13f4cbf54bf090\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\12431dbb4f9be9ddf644e0ebcb3dc8a9\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0821afea2278e1ca2f14d38ad998ab01\transformed\jetified-gifdecoder-4.9.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f531cf6953377e5912353d57e657ec6\transformed\multidex-2.0.1\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:6:5-66
MERGED from [com.shenshi-hw:ad-openset-sdk:1.4.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb230dea6ce0a027a08476565719bd94\transformed\jetified-ad-openset-sdk-1.4.1.2\AndroidManifest.xml:11:5-67
MERGED from [com.shenshi-hw:ad-openset-sdk:1.4.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb230dea6ce0a027a08476565719bd94\transformed\jetified-ad-openset-sdk-1.4.1.2\AndroidManifest.xml:11:5-67
MERGED from [io.github.aliyun-sls:aliyun-log-android-sdk:2.7.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d0163aaba9a980e61cd3d3add9946e\transformed\jetified-aliyun-log-android-sdk-2.7.13\AndroidManifest.xml:9:5-67
MERGED from [io.github.aliyun-sls:aliyun-log-android-sdk:2.7.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d0163aaba9a980e61cd3d3add9946e\transformed\jetified-aliyun-log-android-sdk-2.7.13\AndroidManifest.xml:9:5-67
	android:name
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:7:5-78
MERGED from [io.github.aliyun-sls:aliyun-log-android-sdk:2.7.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d0163aaba9a980e61cd3d3add9946e\transformed\jetified-aliyun-log-android-sdk-2.7.13\AndroidManifest.xml:12:5-79
MERGED from [io.github.aliyun-sls:aliyun-log-android-sdk:2.7.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d0163aaba9a980e61cd3d3add9946e\transformed\jetified-aliyun-log-android-sdk-2.7.13\AndroidManifest.xml:12:5-79
	android:name
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:10:5-78
	android:name
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:10:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:13:5-75
	android:name
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:13:22-73
application
ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:22:5-83:19
INJECTED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:22:5-83:19
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d5fa9c9e36bc912c1cb51114343e1e1\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d5fa9c9e36bc912c1cb51114343e1e1\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af05a54f90b02ba9d95471b1da475021\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af05a54f90b02ba9d95471b1da475021\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:glide:4.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\515e16fdb7b89bef3428a1b9c4124f71\transformed\jetified-glide-4.9.0\AndroidManifest.xml:10:5-20
MERGED from [com.github.bumptech.glide:glide:4.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\515e16fdb7b89bef3428a1b9c4124f71\transformed\jetified-glide-4.9.0\AndroidManifest.xml:10:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97801c6c507d8e8a69f1aae422f6dcba\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97801c6c507d8e8a69f1aae422f6dcba\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [io.github.aliyun-sls:aliyun-log-android-sdk:2.7.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d0163aaba9a980e61cd3d3add9946e\transformed\jetified-aliyun-log-android-sdk-2.7.13\AndroidManifest.xml:14:5-21:19
MERGED from [io.github.aliyun-sls:aliyun-log-android-sdk:2.7.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d0163aaba9a980e61cd3d3add9946e\transformed\jetified-aliyun-log-android-sdk-2.7.13\AndroidManifest.xml:14:5-21:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21323042fb0f26a896866a8b3e30a045\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21323042fb0f26a896866a8b3e30a045\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3c9a3b28f10b45ca7eba0a273fba3f1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3c9a3b28f10b45ca7eba0a273fba3f1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0821afea2278e1ca2f14d38ad998ab01\transformed\jetified-gifdecoder-4.9.0\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0821afea2278e1ca2f14d38ad998ab01\transformed\jetified-gifdecoder-4.9.0\AndroidManifest.xml:9:5-20
	android:extractNativeLibs
		INJECTED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml
	android:roundIcon
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:29:9-54
	android:icon
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:27:9-43
	android:networkSecurityConfig
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:32:9-69
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:30:9-35
	android:label
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:28:9-41
	android:fullBackupContent
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:26:9-54
	tools:targetApi
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:33:9-29
	android:allowBackup
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:24:9-35
	android:theme
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:31:9-54
	android:dataExtractionRules
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:25:9-65
	tools:replace
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:34:9-44
	android:name
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:23:9-52
activity#com.ainative.mountainsurvival.StartActivity
ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:36:9-46:20
	android:label
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:39:13-45
	android:exported
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:38:13-36
	android:theme
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:40:13-58
	android:name
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:37:13-42
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:41:13-45:29
action#android.intent.action.MAIN
ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:42:17-69
	android:name
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:42:25-66
category#android.intent.category.LAUNCHER
ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:44:17-77
	android:name
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:44:27-74
activity#com.ainative.mountainsurvival.PrivacyPolicyActivity
ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:49:9-53:61
	android:label
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:52:13-45
	android:exported
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:51:13-37
	android:theme
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:53:13-58
	android:name
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:50:13-50
activity#com.ainative.mountainsurvival.MainActivity
ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:56:9-60:61
	android:label
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:59:13-45
	android:exported
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:58:13-37
	android:theme
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:60:13-58
	android:name
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:57:13-41
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:68:13-70:54
	android:resource
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:70:17-51
	android:name
		ADDED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:69:17-67
uses-sdk
INJECTED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml
INJECTED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4463e82091f7284071c541968c5d10db\transformed\jetified-viewbinding-8.7.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4463e82091f7284071c541968c5d10db\transformed\jetified-viewbinding-8.7.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d5fa9c9e36bc912c1cb51114343e1e1\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d5fa9c9e36bc912c1cb51114343e1e1\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af05a54f90b02ba9d95471b1da475021\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af05a54f90b02ba9d95471b1da475021\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382d7ec708b1c60f6c45469107eb015d\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382d7ec708b1c60f6c45469107eb015d\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bde3453fac62cb9a4dc37198ed238829\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bde3453fac62cb9a4dc37198ed238829\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d92d08cc2576af245d8b1256d9fdca2b\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d92d08cc2576af245d8b1256d9fdca2b\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57b75fbe4fa53b584c6464086013040e\transformed\recyclerview-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57b75fbe4fa53b584c6464086013040e\transformed\recyclerview-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5f4f7301a1ff6ef457f4dce945b82af\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5f4f7301a1ff6ef457f4dce945b82af\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [com.shenshi-hw:ad-openset-sdk:1.4.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb230dea6ce0a027a08476565719bd94\transformed\jetified-ad-openset-sdk-1.4.1.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.shenshi-hw:ad-openset-sdk:1.4.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb230dea6ce0a027a08476565719bd94\transformed\jetified-ad-openset-sdk-1.4.1.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.bumptech.glide:glide:4.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\515e16fdb7b89bef3428a1b9c4124f71\transformed\jetified-glide-4.9.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\515e16fdb7b89bef3428a1b9c4124f71\transformed\jetified-glide-4.9.0\AndroidManifest.xml:6:5-8:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c5713d08ffe7a55a2f3e45a8d6da960\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c5713d08ffe7a55a2f3e45a8d6da960\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f283c24a6b6e6a9d9c8f6d6be025229\transformed\jetified-activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f283c24a6b6e6a9d9c8f6d6be025229\transformed\jetified-activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c11679833c96ef1c17ca4cbc527ed75c\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c11679833c96ef1c17ca4cbc527ed75c\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e92958f763458f5f9c0f91c54258fed\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e92958f763458f5f9c0f91c54258fed\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efaac7508b8e96d6b24ab730eb214ba0\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efaac7508b8e96d6b24ab730eb214ba0\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9ceaebcb04953e5af5b7d199355b7\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9ceaebcb04953e5af5b7d199355b7\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2b04e21828c7743ee4d1d340169781e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2b04e21828c7743ee4d1d340169781e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38579722fe447de7e84370f857230e4e\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38579722fe447de7e84370f857230e4e\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d3069d327dba0f285848d09130b847\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d3069d327dba0f285848d09130b847\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3d443da1b2e819987dac1bc7803fdb1\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3d443da1b2e819987dac1bc7803fdb1\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6050ef2e88814984a91652c058012d6a\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6050ef2e88814984a91652c058012d6a\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f031e987a77329b9f10b1cc8abf27af8\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f031e987a77329b9f10b1cc8abf27af8\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1b16c64dbae6be7624dd523479f1420\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1b16c64dbae6be7624dd523479f1420\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\533f5aa970bb5d3672625a605676bde3\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\533f5aa970bb5d3672625a605676bde3\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3b14be50ee54593f95bc1d6e842787\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3b14be50ee54593f95bc1d6e842787\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e931b6e179de3f7cfc2b10b9d677ec6\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e931b6e179de3f7cfc2b10b9d677ec6\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea74a906e6d60a742bfd51dafa14c250\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea74a906e6d60a742bfd51dafa14c250\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6497744bf085ef425e48c8e38fd34341\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6497744bf085ef425e48c8e38fd34341\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e4c61f38368fc54557b0c5a3e9ac7c3\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e4c61f38368fc54557b0c5a3e9ac7c3\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\869b9d8aff1267fb27988310f947358b\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\869b9d8aff1267fb27988310f947358b\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42d804cca952a9f169877d8dbe807e09\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42d804cca952a9f169877d8dbe807e09\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04369a3a21eff427dc786218164ed8c1\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04369a3a21eff427dc786218164ed8c1\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ddb2eac7d4720622efdbabdb255f04e\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ddb2eac7d4720622efdbabdb255f04e\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8869cb1353294265edd8ad60076d1bdf\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8869cb1353294265edd8ad60076d1bdf\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf6342dd0eb17ccac87a57a41112fa01\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf6342dd0eb17ccac87a57a41112fa01\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97801c6c507d8e8a69f1aae422f6dcba\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97801c6c507d8e8a69f1aae422f6dcba\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\600b38f565fa40067a69a14564c6c08e\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\600b38f565fa40067a69a14564c6c08e\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4612d558030303403a8cfb545289fbdd\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4612d558030303403a8cfb545289fbdd\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [io.github.aliyun-sls:aliyun-log-android-sdk:2.7.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d0163aaba9a980e61cd3d3add9946e\transformed\jetified-aliyun-log-android-sdk-2.7.13\AndroidManifest.xml:5:5-7:41
MERGED from [io.github.aliyun-sls:aliyun-log-android-sdk:2.7.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d0163aaba9a980e61cd3d3add9946e\transformed\jetified-aliyun-log-android-sdk-2.7.13\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21323042fb0f26a896866a8b3e30a045\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21323042fb0f26a896866a8b3e30a045\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e481672d098364d8e6c2450e3c89571f\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e481672d098364d8e6c2450e3c89571f\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3c9a3b28f10b45ca7eba0a273fba3f1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3c9a3b28f10b45ca7eba0a273fba3f1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d833d99e8e0b8e339661a951759c88a\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d833d99e8e0b8e339661a951759c88a\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5422d7486e2f7babdc4c40344f3b10db\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5422d7486e2f7babdc4c40344f3b10db\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4a0595eef7e2ce43a0f230fe1f681b8\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4a0595eef7e2ce43a0f230fe1f681b8\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d73d3652d6cf20b5df3e528c6d9dbc4b\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d73d3652d6cf20b5df3e528c6d9dbc4b\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8894c91348a93161200a6fccf5692981\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8894c91348a93161200a6fccf5692981\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d2e5a8dc39e12408c13f4cbf54bf090\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d2e5a8dc39e12408c13f4cbf54bf090\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\12431dbb4f9be9ddf644e0ebcb3dc8a9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\12431dbb4f9be9ddf644e0ebcb3dc8a9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0821afea2278e1ca2f14d38ad998ab01\transformed\jetified-gifdecoder-4.9.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0821afea2278e1ca2f14d38ad998ab01\transformed\jetified-gifdecoder-4.9.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f531cf6953377e5912353d57e657ec6\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f531cf6953377e5912353d57e657ec6\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
	android:targetSdkVersion
		INJECTED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97801c6c507d8e8a69f1aae422f6dcba\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97801c6c507d8e8a69f1aae422f6dcba\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21323042fb0f26a896866a8b3e30a045\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21323042fb0f26a896866a8b3e30a045\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.ainative.mountainsurvival.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.ainative.mountainsurvival.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97801c6c507d8e8a69f1aae422f6dcba\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97801c6c507d8e8a69f1aae422f6dcba\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97801c6c507d8e8a69f1aae422f6dcba\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
provider#com.aliyun.sls.android.producer.provider.SLSContentProvider
ADDED from [io.github.aliyun-sls:aliyun-log-android-sdk:2.7.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d0163aaba9a980e61cd3d3add9946e\transformed\jetified-aliyun-log-android-sdk-2.7.13\AndroidManifest.xml:17:9-20:40
	android:authorities
		ADDED from [io.github.aliyun-sls:aliyun-log-android-sdk:2.7.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d0163aaba9a980e61cd3d3add9946e\transformed\jetified-aliyun-log-android-sdk-2.7.13\AndroidManifest.xml:19:13-64
	android:exported
		ADDED from [io.github.aliyun-sls:aliyun-log-android-sdk:2.7.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d0163aaba9a980e61cd3d3add9946e\transformed\jetified-aliyun-log-android-sdk-2.7.13\AndroidManifest.xml:20:13-37
	android:name
		ADDED from [io.github.aliyun-sls:aliyun-log-android-sdk:2.7.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d0163aaba9a980e61cd3d3add9946e\transformed\jetified-aliyun-log-android-sdk-2.7.13\AndroidManifest.xml:18:13-87
