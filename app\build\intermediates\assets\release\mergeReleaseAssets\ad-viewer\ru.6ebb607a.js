!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="39ad49d0-5ed9-4ea7-82e6-944430a11a2d",e._sentryDebugIdIdentifier="sentry-dbid-39ad49d0-5ed9-4ea7-82e6-944430a11a2d")}catch(e){}}();var _global="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};_global.SENTRY_RELEASE={id:"6f8e85ef7e996f564ad171983d53e71d8be3b1f5"},(self.webpackChunkunity_ads_sdk_webview=self.webpackChunkunity_ads_sdk_webview||[]).push([[697],{77486:function(e,t,r){r.r(t),r(11397),r(53133),r(86102),r(49922),r(20462),r(20144),r(79730),r(7300),r(9347),r(48298),r(74837),r(66964),r(99143),r(3960),r(99029),r(95929),r(62141);var n=r(49114);function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach((function(t){u(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function u(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==o(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"===o(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var a=f(f({},n.default),{},{common:f(f({},n.default.common),{},{free:"Бесплатно",get:"Загрузить",install:"Установить",price:"Цена",sponsored:"Реклама"}),cta:{installNow:"Установить",learnMore:"Узнать подробнее"},reward:{description:"Вознаграждение не будет получено.",resumeButtonText:"ПРОДОЛЖИТЬ ПРОСМОТР",rewardGranted:"Вознаграждение выдано!",skipButtonText:"ЗАКРЫТЬ ОБЪЯВЛЕНИЕ",title:"Закрыть объявление?"}});t.default=a}}]);
//# sourceMappingURL=ru.6ebb607a.js.map