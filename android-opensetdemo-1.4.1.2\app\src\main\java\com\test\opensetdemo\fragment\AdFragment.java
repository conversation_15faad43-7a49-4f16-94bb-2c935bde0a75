package com.test.opensetdemo.fragment;

import android.app.Activity;
import android.content.Intent;
import android.view.View;
import android.widget.FrameLayout;

import com.test.opensetdemo.GVItemClick;
import com.test.opensetdemo.MainAdapter;
import com.test.opensetdemo.MainBean;
import com.test.opensetdemo.MyGridView;
import com.test.opensetdemo.R;
import com.test.opensetdemo.ad.AdBannerActivity;
import com.test.opensetdemo.ad.AdInsertActivity;
import com.test.opensetdemo.ad.AdRewardActivity;
import com.test.opensetdemo.ad.AppOpenActivity;
import com.test.opensetdemo.ad.NativeActivity;

import java.util.ArrayList;
import java.util.List;

/**
 * use to
 * Created by lc on 2022/2/17.
 */
public class AdFragment extends BaseFragment {

    private static final String TAG = "AdFragment";

    private MyGridView gvAd;
    private MyGridView gvContent;
    private MyGridView gvRisk;
    private FrameLayout flSuspend;
    private Activity activity;
    private MainAdapter adAdapter;
    private MainAdapter contentAdapter;
    private MainAdapter riskAdapter;

    @Override
    public void initView(View v) {
        activity = getActivity();
        gvAd = v.findViewById(R.id.gv_ad);
        gvContent = v.findViewById(R.id.gv_content);
        gvRisk = v.findViewById(R.id.gv_risk);
        flSuspend = v.findViewById(R.id.fl_suspend);
        initAdGrid();
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_ad;
    }

    private void initAdGrid() {
        List<MainBean> ad = new ArrayList<>();
        ad.add(new MainBean("banner广告", R.drawable.bg_main_banner, R.mipmap.ad_banner));
        ad.add(new MainBean("开屏广告", R.drawable.bg_main_splash, R.mipmap.ad_splash));
        ad.add(new MainBean("插屏广告", R.drawable.bg_main_insert, R.mipmap.ad_insert));
        ad.add(new MainBean("激励视频", R.drawable.bg_main_reward, R.mipmap.ad_reward));
        ad.add(new MainBean("原生信息流", R.drawable.bg_main_information, R.mipmap.ad_information));
        adAdapter = new MainAdapter(getActivity(), ad, click);
        gvAd.setAdapter(adAdapter);
    }

    private GVItemClick click = new GVItemClick() {
        @Override
        public void click(String name) {
            switch (name) {
                case "banner广告":
                    startActivity(new Intent(activity, AdBannerActivity.class));
                    break;
                case "开屏广告":
                    startActivity(new Intent(activity, AppOpenActivity.class));
                    break;
                case "插屏广告":
                    startActivity(new Intent(activity, AdInsertActivity.class));
                    break;
                case "激励视频":
                    startActivity(new Intent(activity, AdRewardActivity.class));
                    break;
                case "原生信息流":
                    startActivity(new Intent(activity, NativeActivity.class));
                    break;
            }
        }
    };
}
