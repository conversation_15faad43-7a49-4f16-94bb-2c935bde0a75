{"events": [{"id": "day1_start", "text": "<PERSON><PERSON> yang menusuk tulang membangunkan anda dari pengsan. Mema<PERSON>a mata terbuka, anda mendapati diri berbaring di sebuah pondok kayu yang reput. <PERSON> lua<PERSON>, raungan ribut salji bergema seperti auman raksasa. Anda memeriksa keadaan diri - mesti segera cari cara untuk membuat api!", "choices": [{"text": "Pecahkan perabot lama untuk membuat api", "effects": {"stamina": -15, "firewood": 6, "cabin_integrity": -8}, "resultText": "Menggunakan kapak di sudut, anda memecahkan kerusi yang goyah. Akhirnya ada tanda-tanda kehangatan di pondok yang sejuk. Walaupun memusnahkan perabot, sekarang kelang<PERSON>gan hidup lebih penting.", "nextEventId": "day1_evening"}, {"text": "<PERSON><PERSON><PERSON> mencari kayu api dalam ribut salji", "effects": {"warmth": -25, "stamina": -25, "firewood": 10}, "resultText": "<PERSON><PERSON>k pintu terbuka, anda hampir terjatuh oleh angin dan salji. <PERSON>gan terdesak mengumpul dahan kering dalam salji setinggi lutut, jari-jari beku kehilangan semua rasa. Tetapi anda berjaya mengumpul lebih banyak kayu api.", "nextEventId": "day1_evening"}, {"text": "<PERSON>i sumber haba lain", "effects": {"stamina": -10, "hope": -5}, "resultText": "<PERSON>a mencari sumber haba lain yang mungkin di sekeliling pondok, tetapi tidak menemui apa-apa selain perapian. Ini agak mengecewakan, tetapi sekurang-kurangnya tidak membazir terlalu banyak tenaga.", "nextEventId": "day1_evening"}]}, {"id": "day1_evening", "text": "<PERSON>da waktu senja, api oren menari di perapian. <PERSON>a berasa letih, tetapi sekurang-kura<PERSON><PERSON> sekarang ada kehangatan. <PERSON><PERSON> se<PERSON> hampir, dan anda perlu bersiap untuk kegelapan panjang di hadapan.", "choices": [{"text": "<PERSON><PERSON><PERSON><PERSON> rumah melawan angin dan salji", "effects": {"stamina": -20, "firewood": -5, "cabin_integrity": 20}, "requirements": {"firewood": 5, "stamina": 20}, "resultText": "Menggunakan kayu api untuk menutup celah tingkap dan mengukuhkan pintu. <PERSON><PERSON><PERSON> ini memenatkan, tetapi pondok menjadi lebih kukuh dan boleh menahan sejuk malam dengan lebih baik.", "nextEventId": "day1_night"}, {"text": "<PERSON><PERSON> ma<PERSON>an", "effects": {"stamina": -15, "food": 3}, "resultText": "Meneroka pondok dengan berhati-hati, anda menemui beberapa tin makanan di belakang rak. Sudah luput tetapi masih kelihatan boleh dimakan. Makanan ini akan membantu bertahan beberapa hari.", "nextEventId": "day1_night"}, {"text": "<PERSON><PERSON><PERSON> awal", "effects": {"stamina": 10, "hope": 5}, "resultText": "Memutuskan untuk berehat ber<PERSON><PERSON>ran per<PERSON>ian. Kehangatan api membungkus badan dan memulihkan sedikit semangat. <PERSON>a berjaya menyimpan tenaga untuk hari esok.", "nextEventId": "day1_night"}]}]}