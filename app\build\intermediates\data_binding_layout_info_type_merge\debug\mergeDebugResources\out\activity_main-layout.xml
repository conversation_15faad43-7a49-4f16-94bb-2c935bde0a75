<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.ainative.mountainsurvival" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="282" endOffset="51"/></Target><Target id="@+id/statusBarLayout" view="LinearLayout"><Expressions/><location startLine="31" startOffset="4" endLine="178" endOffset="18"/></Target><Target id="@+id/warmthTextView" view="TextView"><Expressions/><location startLine="54" startOffset="12" endLine="62" endOffset="42"/></Target><Target id="@+id/warmthPlusButton" view="TextView"><Expressions/><location startLine="64" startOffset="12" endLine="76" endOffset="42"/></Target><Target id="@+id/staminaTextView" view="TextView"><Expressions/><location startLine="87" startOffset="12" endLine="95" endOffset="42"/></Target><Target id="@+id/staminaPlusButton" view="TextView"><Expressions/><location startLine="97" startOffset="12" endLine="109" endOffset="42"/></Target><Target id="@+id/firewoodTextView" view="TextView"><Expressions/><location startLine="120" startOffset="12" endLine="128" endOffset="42"/></Target><Target id="@+id/firewoodPlusButton" view="TextView"><Expressions/><location startLine="130" startOffset="12" endLine="142" endOffset="42"/></Target><Target id="@+id/foodTextView" view="TextView"><Expressions/><location startLine="153" startOffset="12" endLine="161" endOffset="42"/></Target><Target id="@+id/foodPlusButton" view="TextView"><Expressions/><location startLine="163" startOffset="12" endLine="175" endOffset="42"/></Target><Target id="@+id/storyScrollView" view="ScrollView"><Expressions/><location startLine="181" startOffset="4" endLine="212" endOffset="16"/></Target><Target id="@+id/storyTextView" view="TextView"><Expressions/><location startLine="197" startOffset="8" endLine="210" endOffset="38"/></Target><Target id="@+id/choicesLayout" view="LinearLayout"><Expressions/><location startLine="215" startOffset="4" endLine="280" endOffset="18"/></Target><Target id="@+id/choice1Button" view="Button"><Expressions/><location startLine="228" startOffset="8" endLine="238" endOffset="38"/></Target><Target id="@+id/choice2Button" view="Button"><Expressions/><location startLine="241" startOffset="8" endLine="251" endOffset="38"/></Target><Target id="@+id/choice3Button" view="Button"><Expressions/><location startLine="254" startOffset="8" endLine="265" endOffset="39"/></Target><Target id="@+id/choice4Button" view="Button"><Expressions/><location startLine="268" startOffset="8" endLine="278" endOffset="39"/></Target></Targets></Layout>