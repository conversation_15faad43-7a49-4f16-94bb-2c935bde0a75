{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-mergeReleaseResources-30:/values-large-v4/values-large-v4.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bde3453fac62cb9a4dc37198ed238829\\transformed\\appcompat-1.6.1\\res\\values-large-v4\\values-large-v4.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,185,256,326,396,464,532,636", "endColumns": "58,70,70,69,69,67,67,103,115", "endOffsets": "109,180,251,321,391,459,527,631,747"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5d5fa9c9e36bc912c1cb51114343e1e1\\transformed\\material-1.9.0\\res\\values-large-v4\\values-large-v4.xml", "from": {"startLines": "2,3,4,10", "startColumns": "4,4,4,4", "startOffsets": "55,177,311,752", "endLines": "2,3,9,15", "endColumns": "121,133,10,10", "endOffsets": "172,306,747,1190"}, "to": {"startLines": "11,12,13,19", "startColumns": "4,4,4,4", "startOffsets": "752,874,1008,1449", "endLines": "11,12,18,24", "endColumns": "121,133,10,10", "endOffsets": "869,1003,1444,1887"}}]}]}