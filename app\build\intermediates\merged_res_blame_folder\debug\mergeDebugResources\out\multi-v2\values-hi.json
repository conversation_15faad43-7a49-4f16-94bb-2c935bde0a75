{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-mergeDebugResources-30:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b7d68e85c64c5740c775d2c6236c08f5\\transformed\\core-1.9.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "203", "startColumns": "4", "startOffsets": "19428", "endColumns": "100", "endOffsets": "19524"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-hi\\strings.xml", "from": {"startLines": "97,96,93,92,110,99,98,1,2,60,80,59,81,113,56,154,107,47,106,140,141,139,137,136,144,143,134,133,148,69,71,79,46,45,6,54,55,68,67,44,11,82,63,61,64,62,9,32,17,28,29,25,21,31,19,20,26,23,30,15,16,27,22,18,24,112,83,8,7,86,157,158,75,162,78,161,159,160,76,77,48,151,39,37,40,38,36,35,41,153,152,88,111,100,95,94,101,89,105,104,103,102,70,72,87,53,5,51,12,10,129,130,128,117,118,116,147,121,122,120,125,126,124,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7692,7625,7333,7265,8742,7883,7813,16,71,4767,6426,4697,6523,8923,4572,14496,8677,4122,8621,13238,13590,13134,12661,12560,13794,13700,12081,11976,14107,5493,5612,6356,4069,3999,211,4481,4529,5259,5198,3886,458,6625,5013,4836,5093,4927,366,1808,991,1596,1649,1436,1204,1754,1106,1156,1486,1319,1701,866,930,1543,1269,1044,1377,8867,6717,317,268,6834,14597,14703,5873,15108,6297,15008,14820,14923,5931,6114,4176,14306,3638,2040,3692,3553,1971,1890,3750,14421,14351,7033,8799,8005,7513,7428,8143,7095,8474,8410,8341,8282,5551,5761,6958,4431,158,4307,764,408,11460,11839,11320,9151,9782,9008,13910,10047,10509,9908,10766,11181,10623,4376", "endColumns": "120,66,94,67,56,121,69,54,56,68,96,69,101,61,94,63,42,53,55,351,108,103,471,100,83,93,477,104,166,57,148,69,52,69,56,47,42,233,60,112,305,91,79,90,81,85,41,53,52,52,51,49,64,53,49,47,56,57,52,63,60,52,49,61,58,55,94,48,48,123,105,116,57,139,58,99,102,84,182,182,100,44,53,1512,57,84,68,80,109,74,69,61,67,137,111,84,138,138,146,63,68,58,60,86,74,49,52,68,78,49,378,114,139,630,124,142,196,461,112,138,414,137,142,54", "endOffsets": "7808,7687,7423,7328,8794,8000,7878,66,123,4831,6518,4762,6620,8980,4662,14555,8715,4171,8672,13585,13694,13233,13128,12656,13873,13789,12554,12076,14269,5546,5756,6421,4117,4064,263,4524,4567,5488,5254,3994,759,6712,5088,4922,5170,5008,403,1857,1039,1644,1696,1481,1264,1803,1151,1199,1538,1372,1749,925,986,1591,1314,1101,1431,8918,6807,361,312,6953,14698,14815,5926,15243,6351,15103,14918,15003,6109,6292,4272,14346,3687,3548,3745,3633,2035,1966,3855,14491,14416,7090,8862,8138,7620,7508,8277,7229,8616,8469,8405,8336,5607,5843,7028,4476,206,4371,838,453,11834,11949,11455,9777,9902,9146,14102,10504,10617,10042,11176,11314,10761,4426"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,47,48,49,50,51,54,55,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,74,75,76,78,79,80,81,82,83,84,85,86,87,88,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,200,201,202,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2991,3112,3179,3274,3342,3399,3521,3591,3646,4141,4210,4307,4377,4479,4780,4875,5022,5065,5119,5175,5527,5636,5740,6212,6313,6397,6491,6969,7074,7241,7299,7448,7583,7636,7706,7857,7905,7948,8182,8243,8356,8662,8754,8834,8925,9007,9221,9263,9317,9370,9423,9475,9525,9590,9644,9694,9742,9799,9857,9910,9974,10035,10088,10138,10200,10259,10315,14242,14291,14340,14464,14570,14687,14745,14885,14944,15044,15147,15232,15415,15598,15699,15823,15877,17390,17448,17533,17602,17683,17793,17868,17938,18000,18068,18206,18318,18403,18542,18681,18828,18892,18961,19020,19081,19250,19325,19375,19529,19598,19677,19727,20106,20221,20361,20992,21117,21260,21457,21919,22032,22171,22586,22724,22867", "endColumns": "120,66,94,67,56,121,69,54,56,68,96,69,101,61,94,63,42,53,55,351,108,103,471,100,83,93,477,104,166,57,148,69,52,69,56,47,42,233,60,112,305,91,79,90,81,85,41,53,52,52,51,49,64,53,49,47,56,57,52,63,60,52,49,61,58,55,94,48,48,123,105,116,57,139,58,99,102,84,182,182,100,44,53,1512,57,84,68,80,109,74,69,61,67,137,111,84,138,138,146,63,68,58,60,86,74,49,52,68,78,49,378,114,139,630,124,142,196,461,112,138,414,137,142,54", "endOffsets": "3107,3174,3269,3337,3394,3516,3586,3641,3698,4205,4302,4372,4474,4536,4870,4934,5060,5114,5170,5522,5631,5735,6207,6308,6392,6486,6964,7069,7236,7294,7443,7513,7631,7701,7758,7900,7943,8177,8238,8351,8657,8749,8829,8920,9002,9088,9258,9312,9365,9418,9470,9520,9585,9639,9689,9737,9794,9852,9905,9969,10030,10083,10133,10195,10254,10310,10405,14286,14335,14459,14565,14682,14740,14880,14939,15039,15142,15227,15410,15593,15694,15739,15872,17385,17443,17528,17597,17678,17788,17863,17933,17995,18063,18201,18313,18398,18537,18676,18823,18887,18956,19015,19076,19163,19320,19370,19423,19593,19672,19722,20101,20216,20356,20987,21112,21255,21452,21914,22027,22166,22581,22719,22862,22917"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5d5fa9c9e36bc912c1cb51114343e1e1\\transformed\\material-1.9.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,337,413,494,601,697,804,936,1019,1084,1178,1247,1306,1391,1454,1517,1575,1640,1701,1762,1868,1926,1986,2045,2115,2231,2310,2390,2524,2599,2675,2772,2829,2884,2950,3020,3097,3183,3251,3327,3408,3486,3572,3659,3756,3855,3929,3999,4103,4157,4224,4314,4406,4468,4532,4595,4700,4808,4909,5018,5079,5138", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,77,75,80,106,95,106,131,82,64,93,68,58,84,62,62,57,64,60,60,105,57,59,58,69,115,78,79,133,74,75,96,56,54,65,69,76,85,67,75,80,77,85,86,96,98,73,69,103,53,66,89,91,61,63,62,104,107,100,108,60,58,78", "endOffsets": "254,332,408,489,596,692,799,931,1014,1079,1173,1242,1301,1386,1449,1512,1570,1635,1696,1757,1863,1921,1981,2040,2110,2226,2305,2385,2519,2594,2670,2767,2824,2879,2945,3015,3092,3178,3246,3322,3403,3481,3567,3654,3751,3850,3924,3994,4098,4152,4219,4309,4401,4463,4527,4590,4695,4803,4904,5013,5074,5133,5212"}, "to": {"startLines": "2,42,43,44,45,46,52,53,56,73,77,89,90,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3703,3781,3857,3938,4045,4541,4648,4939,7518,7763,9093,9162,10410,10495,10558,10621,10679,10744,10805,10866,10972,11030,11090,11149,11219,11335,11414,11494,11628,11703,11779,11876,11933,11988,12054,12124,12201,12287,12355,12431,12512,12590,12676,12763,12860,12959,13033,13103,13207,13261,13328,13418,13510,13572,13636,13699,13804,13912,14013,14122,14183,15744", "endLines": "5,42,43,44,45,46,52,53,56,73,77,89,90,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,176", "endColumns": "12,77,75,80,106,95,106,131,82,64,93,68,58,84,62,62,57,64,60,60,105,57,59,58,69,115,78,79,133,74,75,96,56,54,65,69,76,85,67,75,80,77,85,86,96,98,73,69,103,53,66,89,91,61,63,62,104,107,100,108,60,58,78", "endOffsets": "304,3776,3852,3933,4040,4136,4643,4775,5017,7578,7852,9157,9216,10490,10553,10616,10674,10739,10800,10861,10967,11025,11085,11144,11214,11330,11409,11489,11623,11698,11774,11871,11928,11983,12049,12119,12196,12282,12350,12426,12507,12585,12671,12758,12855,12954,13028,13098,13202,13256,13323,13413,13505,13567,13631,13694,13799,13907,14008,14117,14178,14237,15818"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bde3453fac62cb9a4dc37198ed238829\\transformed\\appcompat-1.6.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "206,304,414,500,602,723,801,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1896,2001,2103,2201,2311,2414,2523,2681,2782,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,415,513,623,709,811,932,1010,1087,1178,1271,1366,1460,1560,1653,1748,1842,1933,2024,2105,2210,2312,2410,2520,2623,2732,2890,19168", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "410,508,618,704,806,927,1005,1082,1173,1266,1361,1455,1555,1648,1743,1837,1928,2019,2100,2205,2307,2405,2515,2618,2727,2885,2986,19245"}}]}]}