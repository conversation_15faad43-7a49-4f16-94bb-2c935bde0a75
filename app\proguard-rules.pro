-ignorewarnings
# ===================================================================
# ProGuard 基础配置
# ===================================================================
# 保留调试所需的行号信息 (可选，但推荐)
-keepattributes SourceFile,LineNumberTable
# 保留注解、签名、内部类等元数据，防止反射和序列化出错
-keepattributes *Annotation*, Signature, InnerClasses, EnclosingMethod

# ===================================================================
# Android 核心 & Support Library
# ===================================================================
# 保留 Parcelable 实现，防止跨进程通信失败
-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}
# 保留 FileProvider (已修正为 Support Library 版本)
-keep class android.support.v4.content.FileProvider { *; }
-keep class * extends android.support.v4.content.FileProvider

# ===================================================================
# 你项目自身的代码规则
# ===================================================================
# 保护广告ID常量
-keepclassmembers class com.ainative.mountainsurvival.MountainSurvivalApplication {
    public static final java.lang.String APP_KEY;
    public static final java.lang.String REWARD_VIDEO_AD_ID;
}

# 保护游戏数据模型类 (用于JSON反序列化)
-keep class com.ainative.mountainsurvival.GameEvent { *; }
-keep class com.ainative.mountainsurvival.Choice { *; }
-keep class com.ainative.mountainsurvival.RandomChoice { *; }
-keep class com.ainative.mountainsurvival.EventContainer { *; }
-keep class com.ainative.mountainsurvival.GameState { *; }
-keep class com.ainative.mountainsurvival.GameManager$ChoiceResult { *; }
-keep class com.ainative.mountainsurvival.GameManager$GameOverResult { *; }
-keep class com.ainative.mountainsurvival.GameManager$NightSettlementResult { *; }

# 保护游戏核心类
-keep class com.ainative.mountainsurvival.GameEngine { *; }
-keep class com.ainative.mountainsurvival.GameManager { *; }
-keep class com.ainative.mountainsurvival.GameEventUtils { *; }

# 保护所有游戏相关的数据类和枚举
-keep class com.ainative.mountainsurvival.**$* { *; }

# ===================================================================
# 第三方库规则
# ===================================================================
# Gson (JSON序列化)
-dontwarn com.google.gson.**
-keep class com.google.gson.** { *; }
-keepclassmembers class * {
    @com.google.gson.annotations.SerializedName <fields>;
}

# OkHttp (网络请求)
-dontwarn okhttp3.**
-keep class okhttp3.** { *; }

# Kotlin 协程与元数据
-dontwarn kotlin.**
-keep class kotlin.Metadata { *; }
-keepclassmembers class **$WhenMappings { <fields>; }
-keepclassmembers class kotlin.Metadata { public <methods>; }

# 阿里云日志 (sls)
-dontwarn com.aliyun.sls.**
-keep class com.aliyun.sls.** { *; }
-keep interface com.aliyun.sls.android.producer.* { *; }

# ===================================================================
# 广告 SDK 混淆规则 (整合版)
# ===================================================================
# --- 穿山甲 (Pangle / open_ad_sdk) ---
-dontwarn com.bytedance.**
-keep class com.bytedance.** { *; }
-keep interface com.bytedance.** { *; }

# --- 优量汇 (GDT / wind-sdk) ---
-dontwarn com.qq.e.**
-keep class com.qq.e.** { *; }
-dontwarn com.tencent.wind.**
-keep class com.tencent.wind.** { *; }
-keep interface com.tencent.wind.** { *; }

# --- 神蓍 (AdSet / kc.openset) ---
-dontwarn com.kc.openset.**
-keep class com.kc.openset.** { *; }
-keep interface com.kc.openset.** { *; }
-keep class * implements com.kc.openset.** { *; }
-keep class com.kc.openset.util.OSETFileProvider { *; }
-keep @com.qihoo.SdkProtected.OSETSDK.Keep class ** { *; }
-keep,allowobfuscation interface com.qihoo.SdkProtected.OSETSDK.Keep

# --- 百度 (Baidu) ---
-dontwarn com.baidu.mobads.sdk.api.**
-keep class com.baidu.mobads.** { *; }
-keep class com.style.widget.** { *; }
-keep class com.component.** { *; }
-keep class com.baidu.ad.magic.flute.** { *; }
-keep class com.baidu.mobstat.forbes.** { *; }
-keepclassmembers class * extends android.app.Activity { public void *(android.view.View); }
-keepclassmembers enum * { public static **[] values(); public static ** valueOf(java.lang.String); }

# --- 快手 (Kwai) ---
-dontwarn org.chromium.**
-keep class org.chromium.** { *; }
-keep class aegon.chrome.** { *; }
-keep class com.kwai.** { *; }

# --- OAID & MSA ---
-dontwarn com.bun.supplier.**
-keep class com.bun.** { *; }
-keep class com.asus.msa.** { *; }
-keep class com.huawei.hms.ads.identifier.** { *; }
-keep class com.netease.nis.sdkwrapper.** { *; }
-keep class com.samsung.android.deviceidservice.** { *; }
-keep class com.zui.** { *; }
-keep class XI.** { *; }

