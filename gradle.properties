# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# 优化编译性能的JVM参数
org.gradle.jvmargs=-Xmx4096m -Dfile.encoding=UTF-8 -XX:+UseG1GC -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -XX:+UseStringDeduplication

# Kotlin编译器优化设置
kotlin.daemon.jvmargs=-Xmx3072m -XX:+UseG1GC -XX:MaxMetaspaceSize=512m
kotlin.incremental=true
kotlin.incremental.useClasspathSnapshot=true

# 启用并行编译和缓存优化
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true

# 编译优化选项
org.gradle.daemon=true
org.gradle.workers.max=4

# R8优化配置，减少wind-sdk警告
android.r8.ignoreWarnings=true
# 启用AndroidX以兼容国外广告SDK
# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Automatically convert third-party libraries to use AndroidX
android.enableJetifier=true
# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official
# Enables namespacing of each library's R class so that its R class includes only the
# resources declared in the library itself and none from the library's dependencies,
# thereby reducing the size of the R class for that library
android.nonTransitiveRClass=true

# Signing configuration
# 请填写你的密钥信息
RELEASE_STORE_PASSWORD=nihaoshijie233
RELEASE_KEY_ALIAS=mountainsurvival
RELEASE_KEY_PASSWORD=js42fiqn