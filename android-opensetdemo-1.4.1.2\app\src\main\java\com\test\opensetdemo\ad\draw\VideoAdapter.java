package com.test.opensetdemo.ad.draw;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.test.opensetdemo.R;

import java.util.List;


public class VideoAdapter extends RecyclerView.Adapter<VideoAdapter.VideoHolder> {

    private List<VideoBean> data;

    public VideoAdapter(List<VideoBean> data) {
        this.data = data;
    }

    @NonNull
    @Override
    public VideoHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        return new VideoHolder(LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.item_video, viewGroup, false));
    }

    @Override
    public void onBindViewHolder(@NonNull VideoHolder holder, int i) {
        holder.fl.setVisibility(View.GONE);
        holder.fl.removeAllViews();
        holder.content.setVisibility(View.GONE);
        if (data.get(i).getAd() == null) {
            holder.content.setVisibility(View.VISIBLE);

            holder.content.setText(data.get(i).getContent());
        } else {
            holder.fl.setVisibility(View.VISIBLE);
            holder.fl.removeAllViews();
            if (data.get(i).getAd().getParent() != null) {
                ((ViewGroup) data.get(i).getAd().getParent()).removeAllViews();
            }
            holder.fl.addView(data.get(i).getAd());
        }
    }

    @Override
    public int getItemCount() {
        return data == null ? 0 : data.size();
    }

    class VideoHolder extends RecyclerView.ViewHolder {

        private TextView content;
        private FrameLayout fl;

        public VideoHolder(@NonNull View itemView) {
            super(itemView);
            content = itemView.findViewById(R.id.content);
            fl = itemView.findViewById(R.id.fl);
        }
    }
}
