<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.test.opensetdemo">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application
        android:name=".MyApplication"
        android:allowBackup="true"
        android:exported="true"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/test_icon"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:value="1">

        <activity android:name=".ad.AppOpenActivity"
            android:screenOrientation="portrait"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".MainActivity"
            android:launchMode="singleTask" />
        <activity android:name=".ad.AdBannerActivity" />
        <activity android:name=".ad.AdInsertActivity" />
        <activity android:name=".ad.NativeActivity" />
        <activity android:name=".ad.AdRewardActivity" />
        <activity android:name=".content.NovelActivity" />
        <activity android:name=".content.GameActivity" />
        <activity android:name=".WebViewActivity" />
<!--        <activity android:name=".ad.MREClActivity" />-->


    </application>

</manifest>