<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/include_title" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <Button
                android:id="@+id/btn_load"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="20dp"
                android:background="@drawable/bg_main_information"
                android:gravity="center"
                android:text="加载原生信息流"
                android:textColor="@android:color/white" />

            <Button
                android:id="@+id/btn_show"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="20dp"
                android:background="@drawable/bg_btn_gray"
                android:gravity="center"
                android:text="显示原生信息流"
                android:textColor="@android:color/white" />

            <LinearLayout
                android:id="@+id/ll_information"
                android:layout_width="match_parent"
                android:layout_height="436dp"
                android:orientation="vertical">

            </LinearLayout>
        </LinearLayout>
    </ScrollView>
</LinearLayout>