!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="f4175231-d0d9-4126-a36d-8995a7494683",e._sentryDebugIdIdentifier="sentry-dbid-f4175231-d0d9-4126-a36d-8995a7494683")}catch(e){}}();var _global="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};_global.SENTRY_RELEASE={id:"6f8e85ef7e996f564ad171983d53e71d8be3b1f5"},(self.webpackChunkunity_ads_sdk_webview=self.webpackChunkunity_ads_sdk_webview||[]).push([[796],{92393:function(e,t,i){i.r(t),i(11397),i(53133),i(86102),i(49922),i(20462),i(20144),i(79730),i(7300),i(9347),i(48298),i(74837),i(66964),i(99143),i(3960),i(99029),i(95929),i(62141);var n=i(49114);function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function o(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function a(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?o(Object(i),!0).forEach((function(t){c(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):o(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function c(e,t,i){return(t=function(e){var t=function(e,t){if("object"!==r(e)||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var n=i.call(e,t);if("object"!==r(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"===r(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}var s=a(a({},n.default),{},{common:a(a({},n.default.common),{},{advertisement:"广告",back:"返回",buildInfo:"版本信息",campaignPosition:"{campaignNumber}{campaignLength}的广告",confirm:"确认",emailAddress:"电子邮件地址",experiencePersonalization:"体验个性化服务",free:"免费",get:"获取",install:"安装",invalidEmailAddress:"无效的电子邮件格式。请再试一次。",no:"否",personalizationSettings:"个性化服务设置",price:"价格",report:"举报",reportAd:"举报广告",requestYourData:"申请您的数据",sponsored:"发起",submit:"提交",yes:"是"}),cta:{installNow:"立即安装",learnMore:"了解更多"},numbers:{million:"百万",thousand:"千"},privacy:{callToActionBanner:{message:"Unity 在此应用程序中为您提供个性化广告服务体验。{Link}了解 Unity 的更多信息及您的隐私选择",tapHere:"点击此处"},captcha:{confirmMessage:"已确认",dataMissingError:"出现了错误。请稍后重试。",genericError:"出现了错误。请稍后重试。",rateLimited:"速率受限",retryMessage:"错误。请再试一次。",title:"我们需要验证该用户是人类。请查看以下 16 张图片，选择不符合主题的那张图片。"},common:{agreeAll:"同意全部",cancel:"取消",clickHere:"点击此处",confirmationHeader:"您确定吗？",disagreeAll:"反对全部",permissionsPicker:{adsDescription:"我同意接受 Unity 的个性化服务体验和成效衡量",dataLeavesTerritoryDescription:"我同意将我的个人信息转移到中华人民共和国境外",externalDescription:"我也同意接受第三方广告合作伙伴提供的个性化广告，包括使用人口统计信息和其他数据的广告",gameExpDescription:"我也同意使用从 Unity 相关服务（Unity Analytics 和 Unity Mediation）中收集的数据来获得个性化服务体验"},saveChoices:"保存我的选择",showChoices:"显示我的选择"},description:"此广告由 Unity Ads 提供。\n\n    此应用程序的开发者启用了隐私控制，以提供能够更好地保护隐私的体验，或者表示此应用程序针对未满 13 岁的儿童。因此，我们仅根据上下文数据（例如您正在玩的游戏）向您提供广告。\n\n请查看我们的{privacyPolicy}，阅读有关我们数据保护实践的完整描述。您可以通过更改设备设置选择退出 Unity Ads 的个性化体验和广告计划。",ironSourcePrivacyPolicy:"ironSource 的隐私政策",legalTextSections:{changingPrivacyChoices:{description:"您可以随时在广告出现时或出现后通过点击 Unity Data Privacy（Unity 数据隐私）图标来访问这些选项。",title:"更改您的隐私选择"},changingYourPrivacyChoice:{description:"您可以随时在广告出现时或出现后通过点击 Unity Data Privacy（Unity 数据隐私）图标来访问这些选项。",title:"更改您的隐私选择"},collectedInfo:{descriptionP1:"我们用于广告个性化的大部分信息都基于可在设备级别重置的广告 ID（通常为 Apple 广告标识符或 Google 广告标识符）以及您的 IP 地址。",descriptionP2:"我们将使用此信息来显示我们认为您可能喜欢的其他游戏的广告或可能对您有用的虚拟物品。我们甚至可能决定不向您展示某个广告。",descriptionP3:"根据您的隐私选择，您的应用程序交互数据可能与一个唯一的 Unity ID 有关，并且保留长达 12 个月。我们也会将与您的广告 ID 有关的个人数据记录保留 30 天；但我们可能会出于账单管理和预防欺诈的目的，将使用您的个人数据的交易记录保存长达 180 天。",title:"我们收集哪些信息以及我们如何使用信息"},dataTransfer:{description:"请注意 Unity 可能会将您的数据转移到美国境内的服务器并进行处理。我们通过内部协议以及与第三方处理人的协议，努力确保您的数据获得适用法律规定您应当享有的同等保护措施，这包括从欧洲经济区收集您的数据时将会遵守欧洲经济区适用法律的要求。有关数据转移的更多信息，请访问 Unity 的{privacyPolicy}。",title:"数据转移"},infoCollectedByAdsPartners:{descriptionP1:"我们的合作伙伴可能会根据您的偏好和其独立收集的家庭人口统计信息，使用您的广告 ID 和 IP 地址构建您的个人资料。人口统计信息可能包括婚姻状况或大致收入等。请访问我们的第三方隐私政策，查看您可以如何访问或删除其拥有的关于您的信息。",descriptionP2:"合作伙伴还可能通过您的手机收集信息，包括您按压屏幕的力度（压力点测量）。他们收集此信息的目的是，评估与应用程序的互动是否是由真实的人做出（不均匀的压力点），亦或是由恶意行为者投放到市场的试图误导广告网络的机器人做出，以防止出现欺诈行为。",title:"我们的广告合作伙伴收集哪些信息"},infoCollectedByMeasurementPartners:{description:"我们的合作伙伴可能会使用您的广告 ID 和 IP 地址、广告活动的相关信息（例如，创意广告和推广活动的名称）以及您查看广告的应用程序和有限的设备信息（例如，设备型号和操作系统），以便验证您确实看到了广告或以其他方式将广告应用程序的安装归因于 Unity。请访问我们的第三方移动端成效衡量合作伙伴的隐私政策，查看您可以如何访问或删除他们拥有的关于您的信息。",title:"我们的移动端成效衡量合作伙伴收集哪些信息"},ironSourceDataSubjectRight:{description:"请访问{ironSourcePrivacyPolicy}，了解我们的关联公司 ironSource 如何收集和使用您的数据。如需针对 ironSource 可能收集的关于您的数据行使相关数据访问权利和/或删除权利，请{privacyOperation}。",descriptionCoppa:"请访问{ironSourcePrivacyPolicy}，了解我们的关联公司 ironSource 如何收集和使用您的数据。如需针对 ironSource 可能收集的关于您的数据行使相关数据访问权利和/或删除权利，请{privacyOperation}。",title:"ironSource 数据主体权利"},ourPrivacyPolicySection:{description:"我们在{privacyPolicy}中解释了我们如何管理您的数据，包括我们在全球哪些地区以哪些方式传输数据进行数据处理，以及我们如何与投放广告的合作伙伴合作。这些合作伙伴可能会从 Unity 以外的其他来源收集与您的广告 ID 有关的信息，以进一步为您提供个性化的广告。请访问这些第三方隐私政策，查看他们可能拥有的编撰数据。",title:"我们的隐私政策  "},thirdPartyAdPartners:{title:"查看我们的第三方广告合作伙伴"},thirdPartyMobileMeasurementPartners:{title:"查看我们的第三方移动端成效衡量合作伙伴"},unityAnalyticsAndMediation:{descriptionP1:"Unity 可能会使用基于您应用程序使用情况的额外信息为您提供个性化服务体验，如来自 Unity Analytics 和 Unity Mediation 服务的数据。",descriptionP2:"Unity Analytics 服务为应用程序开发者提供工具，从而轻松了解游戏表现和玩家行为，让 Unity 能够收集您在应用程序中花费的时间、您的游戏会话数以及您是否进行了应用内购等信息。",descriptionP3:"Unity Mediation 服务为应用程序开发者提供工具，从而管理多广告网络及向您展示的广告，让 Unity 能够收集您在应用程序中看到的广告等信息，并收集可归因于您的游戏会话的广告收益信息。",title:"Unity Analytics 和 Mediation Services"}},privacyPolicy:"隐私政策",screens:a(a({},n.default.privacy.screens),{},{ageGate:{description:"为确保我们为您提供最好的游戏体验，包括您的隐私选择和隐私保护，请确认您的年龄：",textOlder:"我{ageGateLimit} 岁或以上",textYounger:"我{underAgeGateLimit} 岁或以下",title:"开始使用前，请验证您的年龄"},consent:{descriptionP1:"Unity 和我们的部分第三方广告合作伙伴将收集设备数据，并且会使用移动设备标识符来为您提供个性化广告体验。",descriptionP2:"我们使用设备数据来跟踪您与哪些广告交互，以确定您最感兴趣的广告，并停止向您显示您显然不感兴趣的游戏或产品广告。例如，我们会跟踪您是否从广告中下载了应用程序，以确定您喜欢哪种类型的应用程序。此外，我们的部分第三方合作伙伴也可能会收集您的某些人口统计数据，他们会使用这些数据为您提供个性化广告体验。",descriptionP3:"作为个性化广告流程的一部分，Unity 还将与我们首选的移动成效衡量合作伙伴分享类似的广告交互信息，以衡量广告活动是否成功，并确定您是否与 Unity 向您展示的广告进行了互动并因所展示的广告向 Unity 及其第三方合作伙伴进行了付费。",descriptionP4:"您可以随时在广告出现时或出现后通过点击 Unity Data Privacy（Unity 数据隐私）图标来访问这些选项。",title:"Unity Cookie 能否收集和使用您的数据来为您量身定制广告体验？"},consentChoices:{title:"Unity Cookie 能否收集和使用您的数据来为您量身定制广告体验？"},experiencePersonalization:{choicesSavedMessage:"您的选择已成功保存！",description:"您可以选择继续拥有个性化的体验，亦或继续拥有通用型体验。两种选项均适用于直接来自 Unity 的广告及来自其广告合作伙伴的广告。请注意，如果您已经选择了全网退出，Unity 将继续尊重您之前的隐私选择，此时无需进一步选择退出。",radioButtonTitleNo:"不，我不想接受个性化的体验。这意味着您将退出个性化广告计划。",radioButtonTitleYes:"是，我同意接受个性化的体验。这意味着您将加入个性化广告计划。",title:"体验个性化服务"},personalizationSettings:{choicesSavedMessage:"隐私选择已保存！",description:"Unity Cookie 能否收集和使用您的数据来为您量身定制广告体验？",title:"个性化服务设置"},privacyHome:{detailedHeader:"广告允许开发者免费提供精品应用程序。我们会尝试根据您使用的应用程序、您使用的设备以及您所在的国家/地区，为您展示最感兴趣的应用程序和产品。\n\n    我们用于游戏个性化的大部分信息都基于您设备的广告 ID（Apple 广告标识符或 Google 广告标识符）以及您的 IP 地址。\n\n    Unity 可能会使用基于您应用程序使用情况的额外信息为您提供个性化服务体验，如来自 Unity Analytics 和 Unity Mediation 服务的数据。Unity Analytics 服务为应用程序开发者提供工具，从而轻松了解游戏表现和玩家行为，让 Unity 能够收集您在应用程序中花费的时间、您的游戏会话数以及您是否进行了应用内购等信息。Unity Mediation 服务为应用程序开发者提供工具，从而管理多广告网络及向您展示的广告，让 Unity 能够收集您在应用程序中看到的广告等信息，并收集可归因于您的游戏会话的广告收益信息。\n\n    根据您的隐私选择，您的应用程序交互数据可能与一个唯一的 Unity ID 有关，并且保留长达 12 个月。我们也会将与您的广告 ID 有关的个人数据记录保留 30 天；但我们可能会出于账单管理和预防欺诈的目的，将使用您的个人数据的交易记录保存长达 180 天。",simpleHeader:"此广告由 Unity 提供。Unity 通过您的应用程序使用情况以及您与广告的交互情况来收集信息，以便提供与您更加切身相关的个性化用户体验，并向您展示您更有可能感兴趣的广告。",textHeaderCoppa:"此广告由 Unity Ads 提供。此应用程序的开发者启用了隐私控制，以提供能够更好地保护隐私的体验，或者表示此应用程序针对未满 13 岁的儿童。因此，我们仅根据上下文数据（例如您正在玩的游戏）向您提供广告。"}}),sections:{deleteData:{dataDisclosureNotice:"*请注意，如果您有正在处理的数据披露要求，该报告中可能不包含您要求删除的与该游戏有关的数据。",deleteDataLink:"此处",description:"您可以点击{deleteData}，以删除 Unity 拥有的有关您设备的任何数据。",header:"删除您的数据",requestSentConfirmation:"我们将处理您的请求，您的数据将被删除。请注意，出于资料处理的目的，该过程将需要 30 天才能完成。我们也将立即帮您退出个性化广告。我们可能会出于开票和预防欺诈的目的将您的数据保留 180 天。"},networkWideOptOut:{buttonOptOut:"选择退出全网个性化服务",textDescription:"您可以选择退出整个 Unity 平台的个性化体验。这将适用于直接来自 Unity 的广告以及来自其广告合作伙伴的广告。请注意，在 Unity 提供广告或体验的情况下，此选项适用于您的所有应用程序，但前提是 Unity 在此应用程序中拥有适用于您的全网标识符。如果此类标识符不可用，Unity 将无法实现全网范围内的退出。但是，如果其他应用程序具有全网标识符，则您可以从该应用程序提交此类请求。",textError:"出现了错误，请稍后重试。",textHeader:"选择全网退出",textNwoNotAvailable:"由于开发者启用了隐私控制和/或出于您个人的隐私选择和设备设置，Unity 并未持有您与此应用程序相关的全网个人资料，因此我们目前无法为您提供退出全网个性化服务的选项。",textOptIn:"此处",textOptInAlert:"您已选择加入全网个性化体验。",textOptInDescription:"如果您想要重新启用全网个性化广告体验，您可以点击{optIn}，选择重新加入。请注意，这将在使用 Unity 平台的所有应用程序中重新启用全面个性化体验，这取决于 Unity 是否具有适用的设备级标识符（如上所述）以及您在选择加入之后可能会做出的任何应用程序级别的隐私选择。",textOptOutAlert:"您已选择退出全网个性化体验。"},privacyPolicy:{textDescription:"我们在{privacyPolicy}中解释了我们如何管理您的数据，包括我们在全球哪些地区以哪些方式传输数据进行数据处理，以及我们如何与投放广告的合作伙伴合作。这些合作伙伴可能会从 Unity 以外的其他来源收集与您的广告 ID 有关的信息，以进一步为您提供个性化的广告。请访问这些第三方隐私政策，查看他们可能拥有的编撰数据。",textDescriptionCoppa:"请查看我们的{privacyPolicy}，阅读有关我们数据保护实践的完整描述。您可以随时在广告出现时或出现后通过点击 Unity Data Privacy（Unity 数据隐私）图标来查看隐私信息。",textHeader:"我们的隐私政策  "}},title:"关于 Unity 隐私政策的说明",views:{buildInfo:{labelApp:"app",labelAppVersion:"appVersion",labelCampaign:"campaign",labelCreativeId:"creativeId",labelGroup:"Group",labelOsVersion:"osVersion",labelPlatform:"platform",labelSdk:"sdk",labelTimeStamp:"timestamp",labelUserAgent:"userAgent",labelWebview:"webview",labelWebviewHash:"webviewHash"},reportAd:{radioButtonDoesNotLookRight:"广告看起来不好",radioButtonDontLikeThisAd:"我不喜欢此广告",radioButtonNotShowing:"广告没有显示",radioButtonOffensive:"广告很冒犯人",radioButtonOther:"其他",textDescription:"您当前的广告有问题吗？您可以向 Unity 举报此广告，给予我们反馈。举报将关闭此广告，如果该广告还未播放完成，您将失去任何奖励。",textDisclaimer:"免责声明：举报将关闭此广告，如果该广告还未播放完成，您将失去任何奖励。",textSuccessAlert:"您已经成功举报一条广告。在举报后，我们将关闭此广告，如果该广告还未播放完成，您将失去任何奖励。"},requestData:{textDescription:"您有权要求查看我们为此游戏收集的您的相关数据。要查看数据，请向我们提供您的电子邮件地址，以便我们向您分享安全、个性化的数据请求页面链接。我们不会将您的电子邮件地址用于任何其他目的。",textErrorAlert:"您的尝试次数已达到上限。如需申请查看数据，请在 24 小时后重试。",textSuccessAlert:"谢谢。您应该很快就会收到一封电子邮件，其中包含数据申请链接。"}}},reward:{description:"您将失去奖励",resumeButtonText:"继续播放视频",rewardGranted:"已获得奖励！",skipButtonText:"关闭视频",title:"是否关闭视频？"}});t.default=s}}]);
//# sourceMappingURL=zh_Hans.af0327c2.js.map