// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    repositories {
        google()
        jcenter()
        maven { url 'https://jitpack.io' }

        //bidmachine
        maven { url "https://artifactory.bidmachine.io/bidmachine" }
        //ironsource
        maven { url "https://android-sdk.is.com" }
        //mintegral
        maven { url "https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_oversea" }
        //pangle
        maven { url "https://artifact.bytedance.com/repository/pangle" }

        //        adset
        maven {
            allowInsecureProtocol = true
            url "http://maven.shenshiads.com/nexus/repository/adset/"
        }
        //Anythink(Core)
        maven {
            url "https://jfrog.anythinktech.com/artifactory/overseas_sdk"
        }

    }
    dependencies {
        classpath 'com.android.tools.build:gradle:4.2.0'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        jcenter()
        maven { url "https://jitpack.io" }

        //bidmachine
        maven { url "https://artifactory.bidmachine.io/bidmachine" }
        //ironsource
        maven { url "https://android-sdk.is.com" }
        //mintegral
        maven { url "https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_oversea" }
        //pangle
        maven { url "https://artifact.bytedance.com/repository/pangle" }

        //        adset
        maven {
            allowInsecureProtocol = true
            url "http://maven.shenshiads.com/nexus/repository/adset/"
        }
        //Anythink(Core)
        maven {
            url "https://jfrog.anythinktech.com/artifactory/overseas_sdk"
        }

    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
