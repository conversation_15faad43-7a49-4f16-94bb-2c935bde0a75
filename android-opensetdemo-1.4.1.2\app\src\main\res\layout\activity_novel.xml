<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/include_title" />

    <Button
        android:id="@+id/btn_show"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="20dp"
        android:background="@drawable/bg_main_novel"
        android:gravity="center"
        android:text="显示小说内容"
        android:textColor="@android:color/white"
        android:visibility="gone"
        />


    <Button
        android:id="@+id/btn_sign_novel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="20dp"
        android:background="@drawable/bg_main_novel"
        android:gravity="center"
        android:text="单本小说30s任务"
        android:textColor="@android:color/white" />
</LinearLayout>