<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#40000000" />
            <corners android:radius="8dp" />
            <stroke android:width="3dp" android:color="#ffffff" />
        </shape>
    </item>
    
    <!-- 正常状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#80000000" />
            <corners android:radius="8dp" />
            <stroke android:width="2dp" android:color="#ffffff" />
        </shape>
    </item>
</selector>
