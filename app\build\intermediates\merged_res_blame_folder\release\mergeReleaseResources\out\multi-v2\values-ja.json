{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-mergeReleaseResources-30:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5d5fa9c9e36bc912c1cb51114343e1e1\\transformed\\material-1.9.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,253,320,384,453,534,616,701,805,881,944,1028,1092,1150,1231,1292,1356,1411,1470,1527,1581,1674,1730,1787,1841,1907,2007,2083,2164,2286,2348,2410,2489,2542,2593,2659,2729,2799,2876,2940,3011,3079,3142,3221,3284,3364,3446,3518,3589,3661,3709,3773,3848,3925,3987,4051,4114,4200,4284,4365,4450,4507,4562", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,66,63,68,80,81,84,103,75,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,80,121,61,61,78,52,50,65,69,69,76,63,70,67,62,78,62,79,81,71,70,71,47,63,74,76,61,63,62,85,83,80,84,56,54,72", "endOffsets": "248,315,379,448,529,611,696,800,876,939,1023,1087,1145,1226,1287,1351,1406,1465,1522,1576,1669,1725,1782,1836,1902,2002,2078,2159,2281,2343,2405,2484,2537,2588,2654,2724,2794,2871,2935,3006,3074,3137,3216,3279,3359,3441,3513,3584,3656,3704,3768,3843,3920,3982,4046,4109,4195,4279,4360,4445,4502,4557,4630"}, "to": {"startLines": "2,42,43,44,45,46,52,53,56,73,77,89,90,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3455,3522,3586,3655,3736,4124,4209,4435,6161,6381,7280,7344,8544,8625,8686,8750,8805,8864,8921,8975,9068,9124,9181,9235,9301,9401,9477,9558,9680,9742,9804,9883,9936,9987,10053,10123,10193,10270,10334,10405,10473,10536,10615,10678,10758,10840,10912,10983,11055,11103,11167,11242,11319,11381,11445,11508,11594,11678,11759,11844,11901,13012", "endLines": "5,42,43,44,45,46,52,53,56,73,77,89,90,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,176", "endColumns": "12,66,63,68,80,81,84,103,75,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,80,121,61,61,78,52,50,65,69,69,76,63,70,67,62,78,62,79,81,71,70,71,47,63,74,76,61,63,62,85,83,80,84,56,54,72", "endOffsets": "298,3517,3581,3650,3731,3813,4204,4308,4506,6219,6460,7339,7397,8620,8681,8745,8800,8859,8916,8970,9063,9119,9176,9230,9296,9396,9472,9553,9675,9737,9799,9878,9931,9982,10048,10118,10188,10265,10329,10400,10468,10531,10610,10673,10753,10835,10907,10978,11050,11098,11162,11237,11314,11376,11440,11503,11589,11673,11754,11839,11896,11951,13080"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-ja\\strings.xml", "from": {"startLines": "97,96,93,92,110,99,98,1,2,60,80,59,81,113,56,154,107,47,106,140,141,139,137,136,144,143,134,133,148,69,71,79,46,45,6,54,55,68,67,44,11,82,63,61,64,62,9,32,17,28,29,25,21,31,19,20,26,23,30,15,16,27,22,18,24,112,83,8,7,86,157,158,75,162,78,161,159,160,76,77,48,151,39,37,40,38,36,35,41,153,152,88,111,100,95,94,101,89,105,104,103,102,70,72,87,53,5,51,12,10,129,130,128,117,118,116,147,121,122,120,125,126,124,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5672,5612,5392,5338,6475,5815,5757,16,61,3568,4743,3511,4815,6621,3418,10198,6410,3040,6360,9343,9527,9263,8978,8894,9682,9603,8624,8541,9897,4072,4168,4685,2992,2927,182,3333,3377,3946,3894,2838,401,4887,3747,3622,3810,3686,313,1573,756,1361,1414,1201,969,1519,871,921,1251,1084,1466,636,695,1308,1034,809,1142,6572,4951,269,226,5040,10276,10349,4359,10632,4635,10555,10425,10491,4412,4522,3087,10037,2591,1776,2645,2519,1716,1648,2703,10137,10078,5180,6518,5905,5529,5466,5999,5227,6269,6214,6146,6098,4118,4262,5125,3289,137,3185,551,353,8206,8430,8105,6803,7137,6693,9768,7334,7571,7233,7764,8005,7656,3246", "endColumns": "84,59,73,53,42,89,57,44,56,53,71,56,71,50,68,52,38,46,49,183,74,79,283,83,66,78,268,82,110,45,93,57,47,64,43,43,40,125,51,88,149,63,62,63,60,60,39,49,52,52,51,49,64,53,49,47,56,57,52,58,60,52,49,61,58,48,65,43,42,84,72,75,52,88,49,76,65,63,109,112,71,40,53,742,57,71,59,67,109,60,58,46,53,93,82,62,98,87,90,54,67,47,49,73,54,43,44,60,65,47,223,89,100,333,94,109,128,236,83,100,240,98,107,42", "endOffsets": "5752,5667,5461,5387,6513,5900,5810,56,113,3617,4810,3563,4882,6667,3482,10246,6444,3082,6405,9522,9597,9338,9257,8973,9744,9677,8888,8619,10003,4113,4257,4738,3035,2987,221,3372,3413,4067,3941,2922,546,4946,3805,3681,3866,3742,348,1618,804,1409,1461,1246,1029,1568,916,964,1303,1137,1514,690,751,1356,1079,866,1196,6616,5012,308,264,5120,10344,10420,4407,10716,4680,10627,10486,10550,4517,4630,3154,10073,2640,2514,2698,2586,1771,1711,2808,10193,10132,5222,6567,5994,5607,5524,6093,5310,6355,6264,6209,6141,4163,4331,5175,3328,177,3241,612,396,8425,8515,8201,7132,7227,6798,9892,7566,7650,7329,8000,8099,7759,3284"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,47,48,49,50,51,54,55,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,74,75,76,78,79,80,81,82,83,84,85,86,87,88,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,200,201,202,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2889,2974,3034,3108,3162,3205,3295,3353,3398,3818,3872,3944,4001,4073,4313,4382,4511,4550,4597,4647,4831,4906,4986,5270,5354,5421,5500,5769,5852,5963,6009,6103,6224,6272,6337,6465,6509,6550,6676,6728,6817,6967,7031,7094,7158,7219,7402,7442,7492,7545,7598,7650,7700,7765,7819,7869,7917,7974,8032,8085,8144,8205,8258,8308,8370,8429,8478,11956,12000,12043,12128,12201,12277,12330,12419,12469,12546,12612,12676,12786,12899,12971,13085,13139,13882,13940,14012,14072,14140,14250,14311,14370,14417,14471,14565,14648,14711,14810,14898,14989,15044,15112,15160,15210,15363,15418,15462,15608,15669,15735,15783,16007,16097,16198,16532,16627,16737,16866,17103,17187,17288,17529,17628,17736", "endColumns": "84,59,73,53,42,89,57,44,56,53,71,56,71,50,68,52,38,46,49,183,74,79,283,83,66,78,268,82,110,45,93,57,47,64,43,43,40,125,51,88,149,63,62,63,60,60,39,49,52,52,51,49,64,53,49,47,56,57,52,58,60,52,49,61,58,48,65,43,42,84,72,75,52,88,49,76,65,63,109,112,71,40,53,742,57,71,59,67,109,60,58,46,53,93,82,62,98,87,90,54,67,47,49,73,54,43,44,60,65,47,223,89,100,333,94,109,128,236,83,100,240,98,107,42", "endOffsets": "2969,3029,3103,3157,3200,3290,3348,3393,3450,3867,3939,3996,4068,4119,4377,4430,4545,4592,4642,4826,4901,4981,5265,5349,5416,5495,5764,5847,5958,6004,6098,6156,6267,6332,6376,6504,6545,6671,6723,6812,6962,7026,7089,7153,7214,7275,7437,7487,7540,7593,7645,7695,7760,7814,7864,7912,7969,8027,8080,8139,8200,8253,8303,8365,8424,8473,8539,11995,12038,12123,12196,12272,12325,12414,12464,12541,12607,12671,12781,12894,12966,13007,13134,13877,13935,14007,14067,14135,14245,14306,14365,14412,14466,14560,14643,14706,14805,14893,14984,15039,15107,15155,15205,15279,15413,15457,15502,15664,15730,15778,16002,16092,16193,16527,16622,16732,16861,17098,17182,17283,17524,17623,17731,17774"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b7d68e85c64c5740c775d2c6236c08f5\\transformed\\core-1.9.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "203", "startColumns": "4", "startOffsets": "15507", "endColumns": "100", "endOffsets": "15603"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bde3453fac62cb9a4dc37198ed238829\\transformed\\appcompat-1.6.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "303,400,493,598,680,778,886,964,1039,1130,1223,1318,1412,1512,1605,1700,1794,1885,1976,2054,2156,2254,2349,2452,2548,2644,2792,15284", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "395,488,593,675,773,881,959,1034,1125,1218,1313,1407,1507,1600,1695,1789,1880,1971,2049,2151,2249,2344,2447,2543,2639,2787,2884,15358"}}]}]}