<lint-module
    format="1"
    dir="E:\Ai\AiCode\game\MountainSurvival\code\app"
    name=":app"
    type="APP"
    maven="MountainSurvival:app:unspecified"
    agpVersion="8.7.2"
    buildFolder="build"
    bootClassPath="E:\Other\Sdk\platforms\android-35\android.jar;E:\Other\Sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-35"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
