package com.ainative.mountainsurvival

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*

/**
 * 广告统计管理器
 * 跟踪广告表现、收益和用户行为
 */
object AdStatsManager {
    
    private const val TAG = "AdStatsManager"
    private const val PREFS_NAME = "ad_stats"
    private const val KEY_TOTAL_REVENUE = "total_revenue"
    private const val KEY_TOTAL_IMPRESSIONS = "total_impressions"
    private const val KEY_NETWORK_STATS = "network_stats"
    private const val KEY_DAILY_STATS = "daily_stats"
    
    private lateinit var prefs: SharedPreferences
    
    /**
     * 初始化统计管理器
     */
    fun initialize(context: Context) {
        prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        Log.d(TAG, "广告统计管理器初始化完成")
    }
    
    /**
     * 记录广告展示
     */
    fun recordAdImpression(network: AdapterManager.AdNetwork, revenue: Double) {
        if (!::prefs.isInitialized) {
            Log.w(TAG, "统计管理器未初始化")
            return
        }
        
        val editor = prefs.edit()
        
        // 更新总收益
        val totalRevenue = prefs.getFloat(KEY_TOTAL_REVENUE, 0f) + revenue.toFloat()
        editor.putFloat(KEY_TOTAL_REVENUE, totalRevenue)
        
        // 更新总展示次数
        val totalImpressions = prefs.getInt(KEY_TOTAL_IMPRESSIONS, 0) + 1
        editor.putInt(KEY_TOTAL_IMPRESSIONS, totalImpressions)
        
        // 更新网络统计
        updateNetworkStats(editor, network, revenue)
        
        // 更新每日统计
        updateDailyStats(editor, revenue)
        
        editor.apply()
        
        Log.d(TAG, "记录广告展示: ${network.displayName}, 收益: $${String.format("%.4f", revenue)}")
        Log.d(TAG, "累计收益: $${String.format("%.4f", totalRevenue)}, 累计展示: $totalImpressions")
    }
    
    /**
     * 更新网络统计
     */
    private fun updateNetworkStats(editor: SharedPreferences.Editor, network: AdapterManager.AdNetwork, revenue: Double) {
        val networkStatsJson = prefs.getString(KEY_NETWORK_STATS, "{}")
        val networkStats = JSONObject(networkStatsJson ?: "{}")
        
        val networkName = network.name
        val networkData = if (networkStats.has(networkName)) {
            networkStats.getJSONObject(networkName)
        } else {
            JSONObject()
        }
        
        // 更新网络数据
        val impressions = networkData.optInt("impressions", 0) + 1
        val totalRevenue = networkData.optDouble("revenue", 0.0) + revenue
        val avgRevenue = totalRevenue / impressions
        
        networkData.put("impressions", impressions)
        networkData.put("revenue", totalRevenue)
        networkData.put("avgRevenue", avgRevenue)
        networkData.put("displayName", network.displayName)
        networkData.put("lastUpdate", System.currentTimeMillis())
        
        networkStats.put(networkName, networkData)
        editor.putString(KEY_NETWORK_STATS, networkStats.toString())
    }
    
    /**
     * 更新每日统计
     */
    private fun updateDailyStats(editor: SharedPreferences.Editor, revenue: Double) {
        val today = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
        val dailyStatsJson = prefs.getString(KEY_DAILY_STATS, "{}")
        val dailyStats = JSONObject(dailyStatsJson ?: "{}")
        
        val todayData = if (dailyStats.has(today)) {
            dailyStats.getJSONObject(today)
        } else {
            JSONObject()
        }
        
        val impressions = todayData.optInt("impressions", 0) + 1
        val totalRevenue = todayData.optDouble("revenue", 0.0) + revenue
        
        todayData.put("impressions", impressions)
        todayData.put("revenue", totalRevenue)
        todayData.put("date", today)
        
        dailyStats.put(today, todayData)
        editor.putString(KEY_DAILY_STATS, dailyStats.toString())
    }
    
    /**
     * 获取总收益
     */
    fun getTotalRevenue(): Double {
        return if (::prefs.isInitialized) {
            prefs.getFloat(KEY_TOTAL_REVENUE, 0f).toDouble()
        } else {
            0.0
        }
    }
    
    /**
     * 获取总展示次数
     */
    fun getTotalImpressions(): Int {
        return if (::prefs.isInitialized) {
            prefs.getInt(KEY_TOTAL_IMPRESSIONS, 0)
        } else {
            0
        }
    }
    
    /**
     * 获取网络统计
     */
    fun getNetworkStats(): Map<String, Map<String, Any>> {
        if (!::prefs.isInitialized) return emptyMap()
        
        val networkStatsJson = prefs.getString(KEY_NETWORK_STATS, "{}")
        val networkStats = JSONObject(networkStatsJson ?: "{}")
        val result = mutableMapOf<String, Map<String, Any>>()
        
        networkStats.keys().forEach { networkName ->
            val networkData = networkStats.getJSONObject(networkName)
            result[networkName] = mapOf(
                "displayName" to networkData.optString("displayName", networkName),
                "impressions" to networkData.optInt("impressions", 0),
                "revenue" to networkData.optDouble("revenue", 0.0),
                "avgRevenue" to networkData.optDouble("avgRevenue", 0.0),
                "lastUpdate" to networkData.optLong("lastUpdate", 0)
            )
        }
        
        return result
    }
    
    /**
     * 获取今日统计
     */
    fun getTodayStats(): Map<String, Any> {
        if (!::prefs.isInitialized) return emptyMap()
        
        val today = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
        val dailyStatsJson = prefs.getString(KEY_DAILY_STATS, "{}")
        val dailyStats = JSONObject(dailyStatsJson ?: "{}")
        
        return if (dailyStats.has(today)) {
            val todayData = dailyStats.getJSONObject(today)
            mapOf(
                "date" to today,
                "impressions" to todayData.optInt("impressions", 0),
                "revenue" to todayData.optDouble("revenue", 0.0)
            )
        } else {
            mapOf(
                "date" to today,
                "impressions" to 0,
                "revenue" to 0.0
            )
        }
    }
    
    /**
     * 获取统计报告
     */
    fun getStatsReport(): String {
        if (!::prefs.isInitialized) return "统计管理器未初始化"
        
        val totalRevenue = getTotalRevenue()
        val totalImpressions = getTotalImpressions()
        val avgRevenue = if (totalImpressions > 0) totalRevenue / totalImpressions else 0.0
        val todayStats = getTodayStats()
        val networkStats = getNetworkStats()
        
        val report = StringBuilder()
        report.append("=== 广告统计报告 ===\n")
        report.append("总收益: $${String.format("%.4f", totalRevenue)}\n")
        report.append("总展示: $totalImpressions 次\n")
        report.append("平均收益: $${String.format("%.4f", avgRevenue)} / 次\n")
        report.append("\n今日统计:\n")
        report.append("- 展示: ${todayStats["impressions"]} 次\n")
        report.append("- 收益: $${String.format("%.4f", todayStats["revenue"] as Double)}\n")
        
        if (networkStats.isNotEmpty()) {
            report.append("\n网络表现:\n")
            networkStats.entries.sortedByDescending { 
                (it.value["revenue"] as Double) 
            }.forEach { (networkName, stats) ->
                report.append("- ${stats["displayName"]}: ")
                report.append("${stats["impressions"]}次, ")
                report.append("$${String.format("%.4f", stats["revenue"] as Double)}, ")
                report.append("平均$${String.format("%.4f", stats["avgRevenue"] as Double)}\n")
            }
        }
        
        return report.toString()
    }
    
    /**
     * 清除统计数据
     */
    fun clearStats() {
        if (::prefs.isInitialized) {
            prefs.edit().clear().apply()
            Log.d(TAG, "广告统计数据已清除")
        }
    }
}
