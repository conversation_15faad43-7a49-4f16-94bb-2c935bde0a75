{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-mergeReleaseResources-30:/values-ms/values-ms.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bde3453fac62cb9a4dc37198ed238829\\transformed\\appcompat-1.6.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "329,440,545,653,740,844,955,1034,1112,1203,1296,1391,1485,1583,1676,1771,1865,1956,2047,2127,2239,2347,2444,2553,2657,2764,2923,19458", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "435,540,648,735,839,950,1029,1107,1198,1291,1386,1480,1578,1671,1766,1860,1951,2042,2122,2234,2342,2439,2548,2652,2759,2918,3019,19534"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-ms\\strings.xml", "from": {"startLines": "97,96,93,92,110,99,98,1,2,60,80,59,81,113,56,154,107,47,106,140,141,139,137,136,144,143,134,133,148,69,71,79,46,45,6,54,55,68,67,44,11,82,63,61,64,62,9,32,17,28,29,25,21,31,19,20,26,23,30,15,16,27,22,18,24,112,83,8,7,86,157,158,75,162,78,161,159,160,76,77,48,151,39,37,40,38,36,35,41,153,152,88,111,100,95,94,101,89,105,104,103,102,70,72,87,53,5,51,12,10,129,130,128,117,118,116,147,121,122,120,125,126,124,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7816,7745,7468,7407,8884,8005,7932,16,75,4911,6603,4841,6698,9070,4715,15132,8821,4272,8760,13785,14153,13665,13140,13024,14383,14276,12510,12393,14736,5619,5748,6532,4215,4136,211,4618,4669,5389,5325,4024,458,6789,5140,4988,5219,5063,366,1825,1008,1613,1666,1453,1221,1771,1123,1173,1503,1336,1718,881,947,1560,1286,1061,1394,9009,6879,316,266,6994,15249,15361,6041,15746,6468,15644,15475,15565,6104,6287,4325,14946,3757,2054,3813,3668,1988,1903,3878,15057,14987,7177,8943,8124,7637,7554,8262,7235,8609,8536,8463,8403,5676,5915,7110,4569,157,4448,768,410,11812,12247,11669,9320,10016,9157,14520,10303,10789,10156,11078,11524,10922,4518", "endColumns": "115,70,85,60,58,118,72,58,56,76,94,69,90,56,93,67,38,52,60,367,121,119,523,115,103,106,512,116,176,56,166,70,56,78,54,50,45,229,63,111,309,89,78,74,78,76,43,49,52,52,51,49,64,53,49,47,56,57,52,65,60,52,49,61,58,60,90,49,49,115,111,113,62,149,63,101,89,78,182,180,91,40,55,1613,64,88,65,84,109,74,69,57,65,137,107,82,140,141,150,72,72,59,71,100,66,48,53,69,81,47,434,116,142,695,138,162,215,485,131,146,445,143,155,50", "endOffsets": "7927,7811,7549,7463,8938,8119,8000,70,127,4983,6693,4906,6784,9122,4804,15195,8855,4320,8816,14148,14270,13780,13659,13135,14482,14378,13018,12505,14908,5671,5910,6598,4267,4210,261,4664,4710,5614,5384,4131,763,6874,5214,5058,5293,5135,405,1870,1056,1661,1713,1498,1281,1820,1168,1216,1555,1389,1766,942,1003,1608,1331,1118,1448,9065,6965,361,311,7105,15356,15470,6099,15891,6527,15741,15560,15639,6282,6463,4412,14982,3808,3663,3873,3752,2049,1983,3983,15127,15052,7230,9004,8257,7740,7632,8398,7372,8755,8604,8531,8458,5743,6011,7172,4613,206,4513,845,453,12242,12359,11807,10011,10150,9315,14731,10784,10916,10298,11519,11663,11073,4564"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,47,48,49,50,51,54,55,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,74,75,76,78,79,80,81,82,83,84,85,86,87,88,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,200,201,202,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3024,3140,3211,3297,3358,3417,3536,3609,3668,4150,4227,4322,4392,4483,4759,4853,5004,5043,5096,5157,5525,5647,5767,6291,6407,6511,6618,7131,7248,7425,7482,7649,7785,7842,7921,8069,8120,8166,8396,8460,8572,8882,8972,9051,9126,9205,9406,9450,9500,9553,9606,9658,9708,9773,9827,9877,9925,9982,10040,10093,10159,10220,10273,10323,10385,10444,10505,14389,14439,14489,14605,14717,14831,14894,15044,15108,15210,15300,15379,15562,15743,15835,15962,16018,17632,17697,17786,17852,17937,18047,18122,18192,18250,18316,18454,18562,18645,18786,18928,19079,19152,19225,19285,19357,19539,19606,19655,19810,19880,19962,20010,20445,20562,20705,21401,21540,21703,21919,22405,22537,22684,23130,23274,23430", "endColumns": "115,70,85,60,58,118,72,58,56,76,94,69,90,56,93,67,38,52,60,367,121,119,523,115,103,106,512,116,176,56,166,70,56,78,54,50,45,229,63,111,309,89,78,74,78,76,43,49,52,52,51,49,64,53,49,47,56,57,52,65,60,52,49,61,58,60,90,49,49,115,111,113,62,149,63,101,89,78,182,180,91,40,55,1613,64,88,65,84,109,74,69,57,65,137,107,82,140,141,150,72,72,59,71,100,66,48,53,69,81,47,434,116,142,695,138,162,215,485,131,146,445,143,155,50", "endOffsets": "3135,3206,3292,3353,3412,3531,3604,3663,3720,4222,4317,4387,4478,4535,4848,4916,5038,5091,5152,5520,5642,5762,6286,6402,6506,6613,7126,7243,7420,7477,7644,7715,7837,7916,7971,8115,8161,8391,8455,8567,8877,8967,9046,9121,9200,9277,9445,9495,9548,9601,9653,9703,9768,9822,9872,9920,9977,10035,10088,10154,10215,10268,10318,10380,10439,10500,10591,14434,14484,14600,14712,14826,14889,15039,15103,15205,15295,15374,15557,15738,15830,15871,16013,17627,17692,17781,17847,17932,18042,18117,18187,18245,18311,18449,18557,18640,18781,18923,19074,19147,19220,19280,19352,19453,19601,19650,19704,19875,19957,20005,20440,20557,20700,21396,21535,21698,21914,22400,22532,22679,23125,23269,23425,23476"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5d5fa9c9e36bc912c1cb51114343e1e1\\transformed\\material-1.9.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,279,359,438,525,617,704,807,923,1006,1071,1164,1229,1288,1375,1437,1499,1559,1625,1687,1741,1849,1906,1967,2022,2093,2213,2304,2390,2538,2624,2710,2798,2851,2902,2968,3039,3117,3200,3273,3349,3422,3493,3585,3658,3748,3841,3915,3986,4077,4129,4197,4281,4366,4428,4492,4555,4659,4765,4861,4969,5026,5081", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,79,78,86,91,86,102,115,82,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,85,147,85,85,87,52,50,65,70,77,82,72,75,72,70,91,72,89,92,73,70,90,51,67,83,84,61,63,62,103,105,95,107,56,54,85", "endOffsets": "274,354,433,520,612,699,802,918,1001,1066,1159,1224,1283,1370,1432,1494,1554,1620,1682,1736,1844,1901,1962,2017,2088,2208,2299,2385,2533,2619,2705,2793,2846,2897,2963,3034,3112,3195,3268,3344,3417,3488,3580,3653,3743,3836,3910,3981,4072,4124,4192,4276,4361,4423,4487,4550,4654,4760,4856,4964,5021,5076,5162"}, "to": {"startLines": "2,42,43,44,45,46,52,53,56,73,77,89,90,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3725,3805,3884,3971,4063,4540,4643,4921,7720,7976,9282,9347,10596,10683,10745,10807,10867,10933,10995,11049,11157,11214,11275,11330,11401,11521,11612,11698,11846,11932,12018,12106,12159,12210,12276,12347,12425,12508,12581,12657,12730,12801,12893,12966,13056,13149,13223,13294,13385,13437,13505,13589,13674,13736,13800,13863,13967,14073,14169,14277,14334,15876", "endLines": "5,42,43,44,45,46,52,53,56,73,77,89,90,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,176", "endColumns": "12,79,78,86,91,86,102,115,82,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,85,147,85,85,87,52,50,65,70,77,82,72,75,72,70,91,72,89,92,73,70,90,51,67,83,84,61,63,62,103,105,95,107,56,54,85", "endOffsets": "324,3800,3879,3966,4058,4145,4638,4754,4999,7780,8064,9342,9401,10678,10740,10802,10862,10928,10990,11044,11152,11209,11270,11325,11396,11516,11607,11693,11841,11927,12013,12101,12154,12205,12271,12342,12420,12503,12576,12652,12725,12796,12888,12961,13051,13144,13218,13289,13380,13432,13500,13584,13669,13731,13795,13858,13962,14068,14164,14272,14329,14384,15957"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b7d68e85c64c5740c775d2c6236c08f5\\transformed\\core-1.9.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "203", "startColumns": "4", "startOffsets": "19709", "endColumns": "100", "endOffsets": "19805"}}]}]}