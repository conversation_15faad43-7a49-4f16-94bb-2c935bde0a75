{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-mergeDebugResources-30:/values-ko/values-ko.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b7d68e85c64c5740c775d2c6236c08f5\\transformed\\core-1.9.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "203", "startColumns": "4", "startOffsets": "15798", "endColumns": "100", "endOffsets": "15894"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-ko\\strings.xml", "from": {"startLines": "97,96,93,92,110,99,98,1,2,60,80,59,81,113,56,154,107,47,106,140,141,139,137,136,144,143,134,133,148,69,71,79,46,45,6,54,55,68,67,44,11,82,63,61,64,62,9,32,17,28,29,25,21,31,19,20,26,23,30,15,16,27,22,18,24,112,83,8,7,86,157,158,75,162,78,161,159,160,76,77,48,151,39,37,40,38,36,35,41,153,152,88,111,100,95,94,101,89,105,104,103,102,70,72,87,53,5,51,12,10,129,130,128,117,118,116,147,121,122,120,125,126,124,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5854,5796,5569,5516,6673,6003,5945,16,61,3632,4889,3574,4966,6819,3475,10587,6610,3094,6562,9690,9903,9615,9299,9219,10061,9987,8917,8839,10284,4170,4275,4831,3046,2980,183,3389,3434,4023,3969,2893,401,5044,3818,3689,3883,3755,316,1579,762,1367,1420,1207,975,1525,877,927,1257,1090,1472,642,701,1314,1040,815,1148,6770,5113,271,227,5207,10667,10751,4477,11047,4781,10966,10831,10900,4531,4658,3141,10428,2647,1776,2699,2576,1717,1650,2759,10527,10469,5353,6717,6097,5709,5646,6198,5402,6471,6415,6350,6302,4220,4375,5294,3345,138,3243,556,356,8485,8728,8389,6992,7371,6890,10147,7571,7834,7472,8029,8284,7926,3302", "endColumns": "90,57,76,52,43,93,57,44,56,56,76,57,77,49,73,53,38,46,47,212,82,74,314,79,65,73,300,77,117,49,99,57,47,65,43,44,40,146,53,86,154,68,64,65,61,62,39,46,52,52,51,49,64,53,49,47,56,57,52,58,60,52,49,61,58,48,69,44,43,86,83,79,53,96,49,80,68,65,126,122,77,40,51,799,59,70,58,66,109,59,57,48,52,100,86,62,103,89,90,55,64,47,54,78,58,43,44,58,65,44,242,89,95,378,99,101,136,262,90,98,254,103,102,42", "endOffsets": "5940,5849,5641,5564,6712,6092,5998,56,113,3684,4961,3627,5039,6864,3544,10636,6644,3136,6605,9898,9981,9685,9609,9294,10122,10056,9213,8912,10397,4215,4370,4884,3089,3041,222,3429,3470,4165,4018,2975,551,5108,3878,3750,3940,3813,351,1621,810,1415,1467,1252,1035,1574,922,970,1309,1143,1520,696,757,1362,1085,872,1202,6814,5178,311,266,5289,10746,10826,4526,11139,4826,11042,10895,10961,4653,4776,3214,10464,2694,2571,2754,2642,1771,1712,2864,10582,10522,5397,6765,6193,5791,5704,6297,5487,6557,6466,6410,6345,4270,4449,5348,3384,178,3297,617,396,8723,8813,8480,7366,7466,6987,10279,7829,7920,7566,8279,8383,8024,3340"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,47,48,49,50,51,54,55,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,74,75,76,78,79,80,81,82,83,84,85,86,87,88,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,200,201,202,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2879,2970,3028,3105,3158,3202,3296,3354,3399,3807,3864,3941,3999,4077,4316,4390,4519,4558,4605,4653,4866,4949,5024,5339,5419,5485,5559,5860,5938,6056,6106,6206,6326,6374,6440,6565,6610,6651,6798,6852,6939,7094,7163,7228,7294,7356,7538,7578,7625,7678,7731,7783,7833,7898,7952,8002,8050,8107,8165,8218,8277,8338,8391,8441,8503,8562,8611,12095,12140,12184,12271,12355,12435,12489,12586,12636,12717,12786,12852,12979,13102,13180,13293,13345,14145,14205,14276,14335,14402,14512,14572,14630,14679,14732,14833,14920,14983,15087,15177,15268,15324,15389,15437,15492,15650,15709,15753,15899,15958,16024,16069,16312,16402,16498,16877,16977,17079,17216,17479,17570,17669,17924,18028,18131", "endColumns": "90,57,76,52,43,93,57,44,56,56,76,57,77,49,73,53,38,46,47,212,82,74,314,79,65,73,300,77,117,49,99,57,47,65,43,44,40,146,53,86,154,68,64,65,61,62,39,46,52,52,51,49,64,53,49,47,56,57,52,58,60,52,49,61,58,48,69,44,43,86,83,79,53,96,49,80,68,65,126,122,77,40,51,799,59,70,58,66,109,59,57,48,52,100,86,62,103,89,90,55,64,47,54,78,58,43,44,58,65,44,242,89,95,378,99,101,136,262,90,98,254,103,102,42", "endOffsets": "2965,3023,3100,3153,3197,3291,3349,3394,3451,3859,3936,3994,4072,4122,4385,4439,4553,4600,4648,4861,4944,5019,5334,5414,5480,5554,5855,5933,6051,6101,6201,6259,6369,6435,6479,6605,6646,6793,6847,6934,7089,7158,7223,7289,7351,7414,7573,7620,7673,7726,7778,7828,7893,7947,7997,8045,8102,8160,8213,8272,8333,8386,8436,8498,8557,8606,8676,12135,12179,12266,12350,12430,12484,12581,12631,12712,12781,12847,12974,13097,13175,13216,13340,14140,14200,14271,14330,14397,14507,14567,14625,14674,14727,14828,14915,14978,15082,15172,15263,15319,15384,15432,15487,15566,15704,15748,15793,15953,16019,16064,16307,16397,16493,16872,16972,17074,17211,17474,17565,17664,17919,18023,18126,18169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5d5fa9c9e36bc912c1cb51114343e1e1\\transformed\\material-1.9.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,249,314,378,447,521,600,683,789,864,926,1007,1069,1126,1213,1273,1331,1389,1448,1505,1559,1654,1710,1767,1821,1887,1991,2066,2143,2264,2329,2394,2473,2523,2574,2640,2704,2774,2851,2919,2990,3057,3127,3207,3284,3364,3446,3518,3583,3655,3703,3767,3842,3919,3981,4045,4108,4192,4271,4351,4431,4485,4540", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,64,63,68,73,78,82,105,74,61,80,61,56,86,59,57,57,58,56,53,94,55,56,53,65,103,74,76,120,64,64,78,49,50,65,63,69,76,67,70,66,69,79,76,79,81,71,64,71,47,63,74,76,61,63,62,83,78,79,79,53,54,71", "endOffsets": "244,309,373,442,516,595,678,784,859,921,1002,1064,1121,1208,1268,1326,1384,1443,1500,1554,1649,1705,1762,1816,1882,1986,2061,2138,2259,2324,2389,2468,2518,2569,2635,2699,2769,2846,2914,2985,3052,3122,3202,3279,3359,3441,3513,3578,3650,3698,3762,3837,3914,3976,4040,4103,4187,4266,4346,4426,4480,4535,4607"}, "to": {"startLines": "2,42,43,44,45,46,52,53,56,73,77,89,90,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3456,3521,3585,3654,3728,4127,4210,4444,6264,6484,7419,7481,8681,8768,8828,8886,8944,9003,9060,9114,9209,9265,9322,9376,9442,9546,9621,9698,9819,9884,9949,10028,10078,10129,10195,10259,10329,10406,10474,10545,10612,10682,10762,10839,10919,11001,11073,11138,11210,11258,11322,11397,11474,11536,11600,11663,11747,11826,11906,11986,12040,13221", "endLines": "5,42,43,44,45,46,52,53,56,73,77,89,90,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,176", "endColumns": "12,64,63,68,73,78,82,105,74,61,80,61,56,86,59,57,57,58,56,53,94,55,56,53,65,103,74,76,120,64,64,78,49,50,65,63,69,76,67,70,66,69,79,76,79,81,71,64,71,47,63,74,76,61,63,62,83,78,79,79,53,54,71", "endOffsets": "294,3516,3580,3649,3723,3802,4205,4311,4514,6321,6560,7476,7533,8763,8823,8881,8939,8998,9055,9109,9204,9260,9317,9371,9437,9541,9616,9693,9814,9879,9944,10023,10073,10124,10190,10254,10324,10401,10469,10540,10607,10677,10757,10834,10914,10996,11068,11133,11205,11253,11317,11392,11469,11531,11595,11658,11742,11821,11901,11981,12035,12090,13288"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bde3453fac62cb9a4dc37198ed238829\\transformed\\appcompat-1.6.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,2685", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,2759"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,396,490,591,673,771,877,957,1032,1123,1216,1311,1405,1505,1598,1693,1787,1878,1969,2049,2147,2241,2336,2436,2533,2633,2785,15571", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "391,485,586,668,766,872,952,1027,1118,1211,1306,1400,1500,1593,1688,1782,1873,1964,2044,2142,2236,2331,2431,2528,2628,2780,2874,15645"}}]}]}