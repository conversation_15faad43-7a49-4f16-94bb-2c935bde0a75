# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "viewbinding"
    version: "8.7.2"
  }
  digests {
    sha256: "*\2344\t\354!%u\227\250Z= K\f~\320k\345\002\212\345u\207\332\251N\220\311\036\032\001"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.6.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.6.0"
  }
  digests {
    sha256: "`\261\v^\365v\233yW\001r\340\025\270\025\224\005\311/\003K\250\213\223\221\251wX\234\235\353N"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.0.21"
  }
  digests {
    sha256: "\363\034\305?\020Z~H\300\223h;\275T7V\035\0223\222\005\023wKG\b\005d\033\355\274\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "13.0"
  }
  digests {
    sha256: "\254\342\241\r\310\342\325\3754\222^\312\300>I\210\262\300\370Qe\f\224\270\316\364\233\241\275\021\024x"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "2.0.21"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.0"
  }
  digests {
    sha256: "\005\266(\004D\033\f\232\031 \266\267\325\317s)\244\342KbXG\2162\261\360F\312\001\220\tF"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.8.0"
  }
  digests {
    sha256: "L\210\235\035\230\003\365\362\353l\025\222\246\267\346#i\254v`\311\356\341Z\272\026\376\300Y\0266f"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.9.0"
  }
  digests {
    sha256: "\311m`U\0231\241\226\332\305KtZ\246B\315\a\216\370\233o&qF\267\005\362\302\313\357\005-"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.6.1"
  }
  digests {
    sha256: "~\245W;\223\253\253\323\27521$Q\306\352H\246b\260:\024\r\332\201\256\276uwj \244\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.6.0"
  }
  digests {
    sha256: "\177\br>\312\276\373\246\026\326\fqK\016\23210\033\324\320y/\314yF\301G\234W\375-("
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.9.0"
  }
  digests {
    sha256: "\213\332>\343\250\210\207\325Ofy\373kl\327\210b\237s#J\311\034\213\276\331$\347!\354\205\270"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.3.0"
  }
  digests {
    sha256: "\253\375)\310Un[\3202Z\237v\232\271\351\321T\377JU\025\304v\315\325\242\250([\033\031\334"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.0.0"
  }
  digests {
    sha256: "U\225\244\016\'\212{9\372x\240\224\220\343\327\363\372\251\\{\001DqH\2758\265\255\340`\\5"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.5.1"
  }
  digests {
    sha256: "3\260\327=\302\360(\374\3535\231\272\312\276V<=\266\322o5\023\330\211YXcSjJ\310\300"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.1.0"
  }
  digests {
    sha256: "\376\0227\277\002\235\006>\177)\3769\256\257s\357t\310\260\243e\204\206\374)\323\305C&e8\211"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.1.0"
  }
  digests {
    sha256: "\335wa[\323\335\'Z\373\021\266-\362[\256F\261\vJ\021|\323yC\257E\275\313\370uXR"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.5.1"
  }
  digests {
    sha256: " \255\025 \366%\317E^j\375r\220\230\203\006\323\251\210n\372\231>\b`\373\253\364\273?{\332"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.9.0"
  }
  digests {
    sha256: "\025B\241\337{\351\b\311_\356\221\270\333\300?\331t\365?\021\330J\205\330\201\371ZRU\034@Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.5.1"
  }
  digests {
    sha256: "\024\242}_\270\241Ck\033}\354\030\276\272\246l\203\f\333\274\216(\250\034\345\370[|3\263\256\235"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.5.1"
  }
  digests {
    sha256: "\204\201\024\037\227\360\346!=\323?\314\211\247\204\304\275\021\246\377}Gy\241\317j\016\237\275\277$\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.5.1"
  }
  digests {
    sha256: "\356y!\003\312$\213\372\361P\304Z\223\207\036L\367\350\316\272\271\220\340\366/}\345\324\377/ \237"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.0"
  }
  digests {
    sha256: "-\345(\326\211\216\225\357\002\r\"\331\377\337\235\037w\313\335\223\371-9\337\252]\\C\260\303\021\310"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.6.1"
  }
  digests {
    sha256: "\226\036\275\350\023\207y\242\231C\f\243%\250n(\304\220Rz\207\272Q\203b\372E\304L~~\225"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.6.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.6.1"
  }
  digests {
    sha256: ":\223\377\320R\204FC\300\376\371P\256Ux\333G\314\313\351\347\027mh\0233\030.#+\260\361"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.6.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.6.1"
  }
  digests {
    sha256: "\333\221]\277I5xc\336\026i\377\237\335\216\220\b\326_\343W\257l\316\232\3460C\255_f\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.2.0"
  }
  digests {
    sha256: "\363\032\006\301P\354\2600s\365Zo{\vt\242@\246\250\327\'\301L\347g&\320 W\r\372\214"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.4.1"
  }
  digests {
    sha256: "\333d\233>\372$\343\020R\024S\020\260\002\333\221\3324k?\211\300\223\3548\303\004m\264^yN"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.2.0"
  }
  digests {
    sha256: "\177\372MFM\235\262Y\374\240\315\265\017\275J\266=hr\274\332YF\213\237uUPL}Z\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.3.6"
  }
  digests {
    sha256: "\022\360\203\033O\b\t-]\332\',\031#\301\032\002/\362\f\357\376\323\350\001u\036!\273\215\034\036"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.0.0"
  }
  digests {
    sha256: "\310&\t\316\330\304\230\360\247\001\243\017\266w\033\267H\b`\332\356\204\330.\n\201\356\206\355\367\2729"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.1.4"
  }
  digests {
    sha256: "\r\367\024\300\265\036Tq\016\277tn\264i\3233\027k\273<\262\237\200w]\303\312N\263\026%\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.0.4"
  }
  digests {
    sha256: ">G\177M\3421\345\213%\365\251\222\363\276E\351}3,4\243\232\236>}Kx\256\n\302%o"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-v4"
    version: "1.0.0"
  }
  digests {
    sha256: "x\376\301H_\0178\212GI\002-\325\024\026\205q\'\315%D\256\034?\320\261e\211\005T\200\260"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media"
    artifactId: "media"
    version: "1.0.0"
  }
  digests {
    sha256: "\262;R{+\254\207\fJtQ\346\230-q2\344\023\350\215\177\'\333\353\037\307d\nr\f\331\356"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-ui"
    version: "1.0.0"
  }
  digests {
    sha256: "\r\022`\306\347\346\2437\370u\337q\265\026\223\036p?qn\220\210\230\027\315: \372Z\303\331G"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.0.0"
  }
  digests {
    sha256: "v\277\373|\357\277x\a\224\330\201p\002\332\321V/>\'\300\251\367F\326$\001\310\355\263\n\356\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.swiperefreshlayout"
    artifactId: "swiperefreshlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\227a\263\250\t\311\260\223\375\006\243\304\273\306Eum\354\016\225\265\311\332A\233\311\362\243\363\002n\215"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.asynclayoutinflater"
    artifactId: "asynclayoutinflater"
    version: "1.0.0"
  }
  digests {
    sha256: "\367\352\266\fW\255\335\224\273\006\'X2\376v\000a\033\352\252\341\241\354Y|#\031V\372\371l\213"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.9.0"
  }
  digests {
    sha256: "l\3025\231y&\236M\236\335\316}\204h-+\260j5\241N\334\350\006\277\r\246\350\324\323\030\006"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.15.0"
  }
  digests {
    sha256: "\006pGqCI\347x\232[\333\372\331\321\300\257\237:\036\262\214U\240\356?h\346\202\371\005\304\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.3.1"
  }
  digests {
    sha256: "L\376\324+\334\301\226\321\036\233\020\332h\301\371l\324\275\244\315\205!\347(_bD,\f\021\336\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.1.0-beta02"
  }
  digests {
    sha256: "\272\372\303\312\231\036\326\212,|\246\3775)f\322\000\261.f\243B\321\017I|\270\026\217Y\005J"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.2.0"
  }
  digests {
    sha256: "\241\340Y\263\274\vC\245\215\354\016\376\315\312\250\234\202\322\274\245R\352[\254\366elF\350S\025~"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.shenshi-hw"
    artifactId: "ad-openset-sdk"
    version: "1.4.1.2"
  }
  digests {
    sha256: ",K\323\350\033U\205\322\025\210^]\331\005\334!\026\275E\226K\331\'\374\2550\313\3216\301\265\235"
  }
  repo_index {
    value: 5
  }
}
library {
  maven_library {
    groupId: "io.github.aliyun-sls"
    artifactId: "aliyun-log-android-sdk"
    version: "2.7.13"
  }
  digests {
    sha256: "\306s_\316\317\266\027\271\310\rX/\332p\231!u\224\000\020a\216%!\270\227W=\2405\337\242"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.8.1"
  }
  digests {
    sha256: "\361=\365\030\233\207\r\316\324I\364\301B\344V\276PW\2470=3b\305\2069\020\005l\020\217\351"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "2.7.0"
  }
  digests {
    sha256: "c\262\002\272\336\r\002V\330\243\272s\a\016\002I\200\345\357\213\337\001\r\325\312\375\233/\312,\206\271"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "glide"
    version: "4.9.0"
  }
  digests {
    sha256: "\033\364\202D/\316\201\252\220e\245\351~r\0209\331!\314E\367\'\251\207\276_\032i\370D\331U"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "gifdecoder"
    version: "4.9.0"
  }
  digests {
    sha256: "~\351@*\341\304\217\254\2222\266~\201\370\201\302\027\271\a\263%.I\316W\275\271y7\353\262p"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "disklrucache"
    version: "4.9.0"
  }
  digests {
    sha256: "F\226\250\023@\353k\356\342\032\271?p>\326\347\256I\373L\343\274/\274Tn[\254\322\033\226\271"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "annotations"
    version: "4.9.0"
  }
  digests {
    sha256: "p*u!\313?m~U\355\326n\220\275\241\241\227[\257\227\035%\367[uc\205y\370k\306\233"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.multidex"
    artifactId: "multidex"
    version: "2.0.1"
  }
  digests {
    sha256: "B\3352\377\237\227\370Wq\270* \000:\215p\366\212\267\264\2722\211d1,\340s&\223\333\t"
  }
  repo_index {
  }
}
library_dependencies {
  library_dep_index: 1
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
}
library_dependencies {
  library_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 4
  library_dep_index: 5
  library_dep_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 5
  library_dep_index: 3
}
library_dependencies {
  library_index: 6
  library_dep_index: 3
  library_dep_index: 7
}
library_dependencies {
  library_index: 7
  library_dep_index: 3
}
library_dependencies {
  library_index: 9
  library_dep_index: 10
  library_dep_index: 1
  library_dep_index: 31
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 21
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 38
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 16
  library_dep_index: 22
  library_dep_index: 46
  library_dep_index: 25
  library_dep_index: 3
  library_dep_index: 31
}
library_dependencies {
  library_index: 10
  library_dep_index: 1
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 16
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 30
  library_dep_index: 3
}
library_dependencies {
  library_index: 11
  library_dep_index: 1
}
library_dependencies {
  library_index: 12
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 14
  library_dep_index: 16
  library_dep_index: 20
  library_dep_index: 21
}
library_dependencies {
  library_index: 13
  library_dep_index: 3
}
library_dependencies {
  library_index: 14
  library_dep_index: 15
  library_dep_index: 1
}
library_dependencies {
  library_index: 16
  library_dep_index: 1
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 19
}
library_dependencies {
  library_index: 17
  library_dep_index: 1
}
library_dependencies {
  library_index: 18
  library_dep_index: 1
  library_dep_index: 17
}
library_dependencies {
  library_index: 19
  library_dep_index: 1
}
library_dependencies {
  library_index: 20
  library_dep_index: 1
  library_dep_index: 11
}
library_dependencies {
  library_index: 21
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 3
  library_dep_index: 12
}
library_dependencies {
  library_index: 22
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 23
}
library_dependencies {
  library_index: 23
  library_dep_index: 1
  library_dep_index: 21
  library_dep_index: 24
  library_dep_index: 22
  library_dep_index: 25
  library_dep_index: 3
  library_dep_index: 26
}
library_dependencies {
  library_index: 24
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 19
}
library_dependencies {
  library_index: 25
  library_dep_index: 1
  library_dep_index: 17
  library_dep_index: 19
  library_dep_index: 3
}
library_dependencies {
  library_index: 26
  library_dep_index: 27
  library_dep_index: 29
  library_dep_index: 6
}
library_dependencies {
  library_index: 27
  library_dep_index: 28
}
library_dependencies {
  library_index: 28
  library_dep_index: 29
  library_dep_index: 6
  library_dep_index: 5
}
library_dependencies {
  library_index: 29
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
}
library_dependencies {
  library_index: 30
  library_dep_index: 1
}
library_dependencies {
  library_index: 31
  library_dep_index: 1
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 9
}
library_dependencies {
  library_index: 32
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 11
}
library_dependencies {
  library_index: 33
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 11
}
library_dependencies {
  library_index: 34
  library_dep_index: 1
}
library_dependencies {
  library_index: 35
  library_dep_index: 1
}
library_dependencies {
  library_index: 36
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 37
}
library_dependencies {
  library_index: 37
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 11
}
library_dependencies {
  library_index: 38
  library_dep_index: 1
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 39
  library_dep_index: 40
}
library_dependencies {
  library_index: 39
  library_dep_index: 16
  library_dep_index: 40
  library_dep_index: 1
}
library_dependencies {
  library_index: 40
  library_dep_index: 1
  library_dep_index: 30
}
library_dependencies {
  library_index: 41
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 38
}
library_dependencies {
  library_index: 42
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 11
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 10
  library_dep_index: 24
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 13
}
library_dependencies {
  library_index: 43
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 37
}
library_dependencies {
  library_index: 44
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 45
  library_dep_index: 22
}
library_dependencies {
  library_index: 45
  library_dep_index: 18
  library_dep_index: 24
  library_dep_index: 17
}
library_dependencies {
  library_index: 46
  library_dep_index: 1
}
library_dependencies {
  library_index: 47
  library_dep_index: 9
  library_dep_index: 12
  library_dep_index: 48
}
library_dependencies {
  library_index: 49
  library_dep_index: 12
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 55
  library_dep_index: 42
}
library_dependencies {
  library_index: 50
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 20
}
library_dependencies {
  library_index: 51
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 52
  library_dep_index: 44
  library_dep_index: 53
  library_dep_index: 54
}
library_dependencies {
  library_index: 52
  library_dep_index: 1
}
library_dependencies {
  library_index: 53
  library_dep_index: 1
}
library_dependencies {
  library_index: 54
  library_dep_index: 1
}
library_dependencies {
  library_index: 55
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 51
  library_dep_index: 37
  library_dep_index: 43
  library_dep_index: 56
  library_dep_index: 36
  library_dep_index: 57
  library_dep_index: 34
  library_dep_index: 58
  library_dep_index: 59
  library_dep_index: 35
}
library_dependencies {
  library_index: 56
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 37
  library_dep_index: 11
}
library_dependencies {
  library_index: 57
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 37
}
library_dependencies {
  library_index: 58
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 34
}
library_dependencies {
  library_index: 59
  library_dep_index: 1
  library_dep_index: 12
}
library_dependencies {
  library_index: 60
  library_dep_index: 61
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 62
  library_dep_index: 56
  library_dep_index: 47
  library_dep_index: 12
  library_dep_index: 36
  library_dep_index: 63
  library_dep_index: 13
  library_dep_index: 42
  library_dep_index: 16
  library_dep_index: 64
  library_dep_index: 67
  library_dep_index: 32
  library_dep_index: 66
}
library_dependencies {
  library_index: 62
  library_dep_index: 1
}
library_dependencies {
  library_index: 63
  library_dep_index: 12
  library_dep_index: 11
  library_dep_index: 51
}
library_dependencies {
  library_index: 64
  library_dep_index: 1
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 37
  library_dep_index: 65
  library_dep_index: 66
}
library_dependencies {
  library_index: 65
  library_dep_index: 21
  library_dep_index: 3
}
library_dependencies {
  library_index: 66
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 42
  library_dep_index: 64
}
library_dependencies {
  library_index: 67
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 11
}
library_dependencies {
  library_index: 68
  library_dep_index: 69
  library_dep_index: 70
  library_dep_index: 72
}
library_dependencies {
  library_index: 69
  library_dep_index: 1
}
library_dependencies {
  library_index: 70
  library_dep_index: 71
  library_dep_index: 3
}
library_dependencies {
  library_index: 71
  library_dep_index: 3
  library_dep_index: 5
}
library_dependencies {
  library_index: 72
  library_dep_index: 73
  library_dep_index: 74
  library_dep_index: 75
  library_dep_index: 42
  library_dep_index: 33
}
library_dependencies {
  library_index: 73
  library_dep_index: 1
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 3
  dependency_index: 8
  dependency_index: 9
  dependency_index: 47
  dependency_index: 49
  dependency_index: 60
  dependency_index: 64
  dependency_index: 68
  dependency_index: 76
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://artifact.bytedance.com/repository/Volcengine/"
  }
}
repositories {
  maven_repo {
    url: "https://artifact.bytedance.com/repository/pangle"
  }
}
repositories {
  maven_repo {
    url: "https://developer.hihonor.com/repo"
  }
}
repositories {
  maven_repo {
    url: "http://maven.shenshiads.com/nexus/repository/adset/"
  }
}
