{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-mergeReleaseResources-30:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bde3453fac62cb9a4dc37198ed238829\\transformed\\appcompat-1.6.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "328,430,543,651,736,837,965,1051,1132,1224,1318,1415,1509,1609,1703,1799,1895,1987,2079,2161,2268,2379,2478,2586,2694,2801,2960,20022", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "425,538,646,731,832,960,1046,1127,1219,1313,1410,1504,1604,1698,1794,1890,1982,2074,2156,2263,2374,2473,2581,2689,2796,2955,3054,20100"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5d5fa9c9e36bc912c1cb51114343e1e1\\transformed\\material-1.9.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,359,438,525,626,722,826,948,1029,1094,1189,1270,1333,1422,1486,1555,1618,1692,1756,1812,1930,1988,2050,2106,2186,2325,2414,2496,2637,2718,2798,2888,2944,3000,3066,3145,3227,3315,3389,3466,3536,3615,3699,3783,3875,3975,4049,4130,4232,4285,4352,4445,4534,4596,4660,4723,4836,4929,5033,5127,5187,5247", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,80,78,86,100,95,103,121,80,64,94,80,62,88,63,68,62,73,63,55,117,57,61,55,79,138,88,81,140,80,79,89,55,55,65,78,81,87,73,76,69,78,83,83,91,99,73,80,101,52,66,92,88,61,63,62,112,92,103,93,59,59,82", "endOffsets": "273,354,433,520,621,717,821,943,1024,1089,1184,1265,1328,1417,1481,1550,1613,1687,1751,1807,1925,1983,2045,2101,2181,2320,2409,2491,2632,2713,2793,2883,2939,2995,3061,3140,3222,3310,3384,3461,3531,3610,3694,3778,3870,3970,4044,4125,4227,4280,4347,4440,4529,4591,4655,4718,4831,4924,5028,5122,5182,5242,5325"}, "to": {"startLines": "2,42,43,44,45,46,52,53,56,73,77,89,90,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3777,3858,3937,4024,4125,4660,4764,5046,7848,8101,9429,9510,10780,10869,10933,11002,11065,11139,11203,11259,11377,11435,11497,11553,11633,11772,11861,11943,12084,12165,12245,12335,12391,12447,12513,12592,12674,12762,12836,12913,12983,13062,13146,13230,13322,13422,13496,13577,13679,13732,13799,13892,13981,14043,14107,14170,14283,14376,14480,14574,14634,16280", "endLines": "5,42,43,44,45,46,52,53,56,73,77,89,90,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,176", "endColumns": "12,80,78,86,100,95,103,121,80,64,94,80,62,88,63,68,62,73,63,55,117,57,61,55,79,138,88,81,140,80,79,89,55,55,65,78,81,87,73,76,69,78,83,83,91,99,73,80,101,52,66,92,88,61,63,62,112,92,103,93,59,59,82", "endOffsets": "323,3853,3932,4019,4120,4216,4759,4881,5122,7908,8191,9505,9568,10864,10928,10997,11060,11134,11198,11254,11372,11430,11492,11548,11628,11767,11856,11938,12079,12160,12240,12330,12386,12442,12508,12587,12669,12757,12831,12908,12978,13057,13141,13225,13317,13417,13491,13572,13674,13727,13794,13887,13976,14038,14102,14165,14278,14371,14475,14569,14629,14689,16358"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-es\\strings.xml", "from": {"startLines": "97,96,93,92,110,99,98,1,2,60,80,59,81,113,56,154,107,47,106,140,141,139,137,136,144,143,134,133,148,69,71,79,46,45,6,54,55,68,67,44,11,82,63,61,64,62,9,32,17,28,29,25,21,31,19,20,26,23,30,15,16,27,22,18,24,112,83,8,7,86,157,158,75,162,78,161,159,160,76,77,48,151,39,37,40,38,36,35,41,153,152,88,111,100,95,94,101,89,105,104,103,102,70,72,87,53,5,51,12,10,129,130,128,117,118,116,147,121,122,120,125,126,124,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8168,8095,7827,7765,9242,8363,8289,16,81,5192,6890,5111,7003,9424,4985,15599,9179,4521,9121,14225,14593,14099,13608,13486,14828,14720,12952,12831,15176,5909,6025,6818,4462,4387,224,4893,4940,5698,5624,4270,484,7109,5435,5269,5513,5350,390,1872,1055,1660,1713,1500,1268,1818,1170,1220,1550,1383,1765,922,994,1607,1333,1108,1441,9364,7207,332,278,7331,15718,15831,6313,16241,6750,16126,15943,16040,6379,6567,4575,15406,4009,2131,4065,3902,2062,1962,4127,15522,15447,7528,9300,8486,7992,7912,8625,7587,8970,8903,8829,8766,5962,6196,7459,4840,171,4708,799,434,12267,12687,12114,9678,10408,9519,14953,10714,11223,10556,11517,11956,11349,4779", "endColumns": "120,72,84,61,57,122,73,64,56,76,112,80,105,61,95,63,38,53,57,367,125,125,489,121,86,107,532,120,192,52,170,71,58,74,53,46,44,210,73,116,314,97,77,80,81,84,43,52,52,52,51,49,64,53,49,47,56,57,52,71,60,52,49,61,58,59,99,57,53,127,112,111,65,175,67,114,96,85,187,182,100,40,55,1770,61,106,68,99,109,76,74,58,63,138,102,79,140,138,150,66,73,62,62,88,68,52,52,70,88,49,419,111,152,729,146,158,222,508,124,157,438,156,167,60", "endOffsets": "8284,8163,7907,7822,9295,8481,8358,76,133,5264,6998,5187,7104,9481,5076,15658,9213,4570,9174,14588,14714,14220,14093,13603,14910,14823,13480,12947,15364,5957,6191,6885,4516,4457,273,4935,4980,5904,5693,4382,794,7202,5508,5345,5590,5430,429,1920,1103,1708,1760,1545,1328,1867,1215,1263,1602,1436,1813,989,1050,1655,1378,1165,1495,9419,7302,385,327,7454,15826,15938,6374,16412,6813,16236,16035,16121,6562,6745,4671,15442,4060,3897,4122,4004,2126,2057,4232,15594,15517,7582,9359,8620,8090,7987,8761,7721,9116,8965,8898,8824,6020,6280,7523,4888,219,4774,883,479,12682,12794,12262,10403,10550,9673,15171,11218,11343,10709,11951,12108,11512,4835"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,47,48,49,50,51,54,55,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,74,75,76,78,79,80,81,82,83,84,85,86,87,88,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,200,201,202,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3059,3180,3253,3338,3400,3458,3581,3655,3720,4221,4298,4411,4492,4598,4886,4982,5127,5166,5220,5278,5646,5772,5898,6388,6510,6597,6705,7238,7359,7552,7605,7776,7913,7972,8047,8196,8243,8288,8499,8573,8690,9005,9103,9181,9262,9344,9573,9617,9670,9723,9776,9828,9878,9943,9997,10047,10095,10152,10210,10263,10335,10396,10449,10499,10561,10620,10680,14694,14752,14806,14934,15047,15159,15225,15401,15469,15584,15681,15767,15955,16138,16239,16363,16419,18190,18252,18359,18428,18528,18638,18715,18790,18849,18913,19052,19155,19235,19376,19515,19666,19733,19807,19870,19933,20105,20174,20227,20381,20452,20541,20591,21011,21123,21276,22006,22153,22312,22535,23044,23169,23327,23766,23923,24091", "endColumns": "120,72,84,61,57,122,73,64,56,76,112,80,105,61,95,63,38,53,57,367,125,125,489,121,86,107,532,120,192,52,170,71,58,74,53,46,44,210,73,116,314,97,77,80,81,84,43,52,52,52,51,49,64,53,49,47,56,57,52,71,60,52,49,61,58,59,99,57,53,127,112,111,65,175,67,114,96,85,187,182,100,40,55,1770,61,106,68,99,109,76,74,58,63,138,102,79,140,138,150,66,73,62,62,88,68,52,52,70,88,49,419,111,152,729,146,158,222,508,124,157,438,156,167,60", "endOffsets": "3175,3248,3333,3395,3453,3576,3650,3715,3772,4293,4406,4487,4593,4655,4977,5041,5161,5215,5273,5641,5767,5893,6383,6505,6592,6700,7233,7354,7547,7600,7771,7843,7967,8042,8096,8238,8283,8494,8568,8685,9000,9098,9176,9257,9339,9424,9612,9665,9718,9771,9823,9873,9938,9992,10042,10090,10147,10205,10258,10330,10391,10444,10494,10556,10615,10675,10775,14747,14801,14929,15042,15154,15220,15396,15464,15579,15676,15762,15950,16133,16234,16275,16414,18185,18247,18354,18423,18523,18633,18710,18785,18844,18908,19047,19150,19230,19371,19510,19661,19728,19802,19865,19928,20017,20169,20222,20275,20447,20536,20586,21006,21118,21271,22001,22148,22307,22530,23039,23164,23322,23761,23918,24086,24147"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b7d68e85c64c5740c775d2c6236c08f5\\transformed\\core-1.9.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "203", "startColumns": "4", "startOffsets": "20280", "endColumns": "100", "endOffsets": "20376"}}]}]}