apply plugin: 'com.android.application'

android {
    signingConfigs {
        release {
            storeFile file('../app/open.jks')
            storePassword 'shck1234'
            keyAlias = 'key'
            keyPassword 'shck1234'
        }
        debug {
            storeFile file('../app/open.jks')
            storePassword 'shck1234'
            keyAlias = 'key'
            keyPassword 'shck1234'
        }
    }
    dataBinding {
        enabled = true
    }
    dexOptions {
        javaMaxHeapSize '4g'
    }
    compileSdkVersion 33
    buildToolsVersion "33.0.3"
    defaultConfig {
        applicationId "com.shck.ouyinapp.hw"
        minSdkVersion 21
        targetSdkVersion 33
        versionCode 1410
        versionName "1.4.1.0"
        multiDexEnabled true
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        ndk {
            abiFilters 'armeabi', 'arm64-v8a', 'armeabi-v7a'
        }
    }
    sourceSets.main {
        jni.srcDirs = []
        jniLibs.srcDirs = ['libs']
    }
    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }
    configurations.configureEach {
        //每隔24小时检查远程依赖是否存在更新
//    resolutionStrategy.cacheChangingModulesFor 24, 'hours'
        //每隔10分钟..
        resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
        // 采用动态版本声明的依赖缓存10分钟
        resolutionStrategy.cacheDynamicVersionsFor 0, 'seconds'
    }



    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    repositories {
        flatDir {
            dirs 'libs'
        }
    }

}

dependencies {

    implementation fileTree(include: ['*.jar'], dir: 'libs')
    //noinspection GradleCompatible
    implementation 'com.android.support:appcompat-v7:28.0.0'
    implementation 'com.android.support:multidex:1.0.3'

    //core
    implementation 'com.shenshi-hw:ad-openset-sdk:1.4.1.2'

    //BidMachine
    implementation 'com.shenshi-hw:ad-bidmachine-adapter:3.1.1.2'

    //bigo
    implementation 'com.shenshi-hw:ad-bigo-adapter:5.3.0.1'

    //DT(fyber)
    implementation 'com.shenshi-hw:ad-fyber-adapter:8.3.7.1'

    //inmobi
    implementation 'com.shenshi-hw:ad-inmobi-adapter:10.8.2.1'

    //ironsource
    implementation 'com.shenshi-hw:ad-ironsource-adapter:8.7.0.0.1'

    //kwai
    implementation 'com.shenshi-hw:ad-kwai-adapter:1.2.15.2'

    //applovin
    implementation 'com.shenshi-hw:ad-max-adapter:13.3.1.1'

    //mintegral
    implementation 'com.shenshi-hw:ad-mingetral-adapter:16.9.71.1'

    //pangle
    implementation 'com.shenshi-hw:ad-pangle-adapter:7.2.0.6.1'

    //topon(ADX)
    implementation 'com.shenshi-hw:ad-topon-adapter:6.5.08.1'

    //unity
    implementation 'com.shenshi-hw:ad-unity-adapter:4.14.0.1'

    //vungle
    implementation 'com.shenshi-hw:ad-vungle-adapter:7.5.0.1'

}

configurations.configureEach {
    resolutionStrategy.force "org.jetbrains.kotlin:kotlin-stdlib:1.9.0"
    resolutionStrategy.force "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.0"
    resolutionStrategy.force "org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.0"
}