{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-mergeDebugResources-30:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5d5fa9c9e36bc912c1cb51114343e1e1\\transformed\\material-1.9.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,363,444,527,636,731,829,959,1044,1110,1207,1290,1356,1458,1523,1598,1654,1733,1793,1847,1969,2028,2090,2144,2226,2361,2453,2537,2681,2760,2841,2934,2989,3040,3106,3186,3267,3370,3443,3521,3594,3666,3759,3831,3923,4015,4089,4173,4265,4322,4388,4471,4558,4620,4684,4747,4849,4947,5044,5145,5204,5259", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,79,80,82,108,94,97,129,84,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,83,143,78,80,92,54,50,65,79,80,102,72,77,72,71,92,71,91,91,73,83,91,56,65,82,86,61,63,62,101,97,96,100,58,54,88", "endOffsets": "278,358,439,522,631,726,824,954,1039,1105,1202,1285,1351,1453,1518,1593,1649,1728,1788,1842,1964,2023,2085,2139,2221,2356,2448,2532,2676,2755,2836,2929,2984,3035,3101,3181,3262,3365,3438,3516,3589,3661,3754,3826,3918,4010,4084,4168,4260,4317,4383,4466,4553,4615,4679,4742,4844,4942,5039,5140,5199,5254,5343"}, "to": {"startLines": "2,42,43,44,45,46,52,53,56,73,77,89,90,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3863,3943,4024,4107,4216,4746,4844,5130,8057,8316,9742,9825,11104,11206,11271,11346,11402,11481,11541,11595,11717,11776,11838,11892,11974,12109,12201,12285,12429,12508,12589,12682,12737,12788,12854,12934,13015,13118,13191,13269,13342,13414,13507,13579,13671,13763,13837,13921,14013,14070,14136,14219,14306,14368,14432,14495,14597,14695,14792,14893,14952,16714", "endLines": "5,42,43,44,45,46,52,53,56,73,77,89,90,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,176", "endColumns": "12,79,80,82,108,94,97,129,84,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,83,143,78,80,92,54,50,65,79,80,102,72,77,72,71,92,71,91,91,73,83,91,56,65,82,86,61,63,62,101,97,96,100,58,54,88", "endOffsets": "328,3938,4019,4102,4211,4306,4839,4969,5210,8118,8408,9820,9886,11201,11266,11341,11397,11476,11536,11590,11712,11771,11833,11887,11969,12104,12196,12280,12424,12503,12584,12677,12732,12783,12849,12929,13010,13113,13186,13264,13337,13409,13502,13574,13666,13758,13832,13916,14008,14065,14131,14214,14301,14363,14427,14490,14592,14690,14787,14888,14947,15002,16798"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bde3453fac62cb9a4dc37198ed238829\\transformed\\appcompat-1.6.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,559,669,751,857,987,1065,1141,1232,1325,1423,1518,1618,1711,1804,1899,1990,2081,2167,2277,2388,2491,2602,2710,2817,2976,20719", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,554,664,746,852,982,1060,1136,1227,1320,1418,1513,1613,1706,1799,1894,1985,2076,2162,2272,2383,2486,2597,2705,2812,2971,3070,20801"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b7d68e85c64c5740c775d2c6236c08f5\\transformed\\core-1.9.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "203", "startColumns": "4", "startOffsets": "20989", "endColumns": "100", "endOffsets": "21085"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-fr\\strings.xml", "from": {"startLines": "97,96,93,92,110,99,98,1,2,60,80,59,81,113,56,154,107,47,106,140,141,139,137,136,144,143,134,133,148,69,71,79,46,45,6,54,55,68,67,44,11,82,63,61,64,62,9,32,17,28,29,25,21,31,19,20,26,23,30,15,16,27,22,18,24,112,83,8,7,86,157,158,75,162,78,161,159,160,76,77,48,151,39,37,40,38,36,35,41,153,152,88,111,100,95,94,101,89,105,104,103,102,70,72,87,53,5,51,12,10,129,130,128,117,118,116,147,121,122,120,125,126,124,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8591,8506,8194,8122,9761,8808,8721,16,72,5368,7222,5291,7332,9954,5172,16437,9698,4673,9639,15044,15424,14935,14375,14265,15638,15543,13670,13558,16001,6129,6262,7145,4610,4533,218,5063,5123,5898,5828,4407,479,7442,5625,5445,5708,5536,385,1889,1072,1677,1730,1517,1285,1835,1187,1237,1567,1400,1782,935,1011,1624,1350,1125,1458,9891,7543,326,271,7670,16555,16683,6589,17131,7079,17016,16806,16919,6655,6874,4727,16236,4136,2148,4196,4032,2081,1984,4266,16354,16277,7875,9826,8947,8386,8291,9100,7942,9479,9407,9324,9262,6186,6459,7799,5012,162,4873,822,429,12936,13409,12799,10206,11007,10048,15763,11327,11864,11175,12162,12642,12006,4949", "endColumns": "129,84,96,71,64,138,86,55,56,76,109,76,109,60,90,64,38,53,58,379,117,108,558,109,84,94,593,111,196,56,196,76,62,76,52,59,48,230,69,125,342,100,82,90,85,88,43,51,52,52,51,49,64,53,49,47,56,57,52,75,60,52,49,61,58,62,99,58,54,128,127,122,65,180,65,114,112,96,218,204,109,40,59,1883,69,103,66,96,109,82,76,66,64,152,119,94,161,146,159,71,82,61,75,101,75,50,55,75,78,49,472,116,136,800,166,157,237,536,140,151,479,155,155,62", "endOffsets": "8716,8586,8286,8189,9821,8942,8803,67,124,5440,7327,5363,7437,10010,5258,16497,9732,4722,9693,15419,15537,15039,14929,14370,15718,15633,14259,13665,16193,6181,6454,7217,4668,4605,266,5118,5167,6124,5893,4528,817,7538,5703,5531,5789,5620,424,1936,1120,1725,1777,1562,1345,1884,1232,1280,1619,1453,1830,1006,1067,1672,1395,1182,1512,9949,7638,380,321,7794,16678,16801,6650,17307,7140,17126,16914,17011,6869,7074,4832,16272,4191,4027,4261,4131,2143,2076,4371,16432,16349,7937,9886,9095,8501,8381,9257,8084,9634,9474,9402,9319,6257,6556,7870,5058,213,4944,896,474,13404,13521,12931,11002,11169,10201,15996,11859,12000,11322,12637,12793,12157,5007"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,47,48,49,50,51,54,55,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,74,75,76,78,79,80,81,82,83,84,85,86,87,88,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,200,201,202,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3075,3205,3290,3387,3459,3524,3663,3750,3806,4311,4388,4498,4575,4685,4974,5065,5215,5254,5308,5367,5747,5865,5974,6533,6643,6728,6823,7417,7529,7726,7783,7980,8123,8186,8263,8413,8473,8522,8753,8823,8949,9292,9393,9476,9567,9653,9891,9935,9987,10040,10093,10145,10195,10260,10314,10364,10412,10469,10527,10580,10656,10717,10770,10820,10882,10941,11004,15007,15066,15121,15250,15378,15501,15567,15748,15814,15929,16042,16139,16358,16563,16673,16803,16863,18747,18817,18921,18988,19085,19195,19278,19355,19422,19487,19640,19760,19855,20017,20164,20324,20396,20479,20541,20617,20806,20882,20933,21090,21166,21245,21295,21768,21885,22022,22823,22990,23148,23386,23923,24064,24216,24696,24852,25008", "endColumns": "129,84,96,71,64,138,86,55,56,76,109,76,109,60,90,64,38,53,58,379,117,108,558,109,84,94,593,111,196,56,196,76,62,76,52,59,48,230,69,125,342,100,82,90,85,88,43,51,52,52,51,49,64,53,49,47,56,57,52,75,60,52,49,61,58,62,99,58,54,128,127,122,65,180,65,114,112,96,218,204,109,40,59,1883,69,103,66,96,109,82,76,66,64,152,119,94,161,146,159,71,82,61,75,101,75,50,55,75,78,49,472,116,136,800,166,157,237,536,140,151,479,155,155,62", "endOffsets": "3200,3285,3382,3454,3519,3658,3745,3801,3858,4383,4493,4570,4680,4741,5060,5125,5249,5303,5362,5742,5860,5969,6528,6638,6723,6818,7412,7524,7721,7778,7975,8052,8181,8258,8311,8468,8517,8748,8818,8944,9287,9388,9471,9562,9648,9737,9930,9982,10035,10088,10140,10190,10255,10309,10359,10407,10464,10522,10575,10651,10712,10765,10815,10877,10936,10999,11099,15061,15116,15245,15373,15496,15562,15743,15809,15924,16037,16134,16353,16558,16668,16709,16858,18742,18812,18916,18983,19080,19190,19273,19350,19417,19482,19635,19755,19850,20012,20159,20319,20391,20474,20536,20612,20714,20877,20928,20984,21161,21240,21290,21763,21880,22017,22818,22985,23143,23381,23918,24059,24211,24691,24847,25003,25066"}}]}]}