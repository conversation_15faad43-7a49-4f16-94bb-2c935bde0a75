package com.ainative.mountainsurvival

import android.app.Activity
import android.content.Context
import android.util.Log
import com.kc.openset.ad.reward.OSETRewardVideo
import com.kc.openset.ad.listener.OSETRewardListener
import com.kc.openset.config.OSETSDK

/**
 * 广告管理器
 * 负责处理激励视频广告的加载和显示
 */
object AdManager {
    private const val TAG = "AdManager"
    
    /**
     * 资源类型枚举
     */
    enum class ResourceType(val displayName: String, val rewardAmount: Int) {
        WARMTH("体温", 50),
        STAMINA("体力", 50),
        FIREWOOD("木柴", 20),
        FOOD("食物", 10)
    }

    /**
     * 广告回调接口
     */
    interface AdCallback {
        fun onAdLoadSuccess()
        fun onAdLoadFailed(error: String)
        fun onAdShowSuccess()
        fun onAdShowFailed(error: String)
        fun onAdRewarded(resourceType: ResourceType)
        fun onAdClosed()
    }

    /**
     * 复活广告回调接口
     */
    interface ReviveAdCallback {
        fun onAdLoadSuccess()
        fun onAdLoadFailed(error: String)
        fun onAdShowSuccess()
        fun onAdShowFailed(error: String)
        fun onReviveSuccess()
        fun onAdClosed()
    }

    /**
     * 显示激励视频广告获取资源
     * @param activity 当前Activity
     * @param resourceType 资源类型
     * @param callback 广告回调
     */
    fun showRewardVideoForResource(
        activity: Activity,
        resourceType: ResourceType,
        callback: AdCallback
    ) {
        Log.d(TAG, "开始加载激励视频广告，资源类型: ${resourceType.displayName}")
        Log.d(TAG, "使用广告位ID: ${MountainSurvivalApplication.REWARD_VIDEO_AD_ID}")
        Log.d(TAG, "应用包名: ${activity.packageName}")

        // 检查广告SDK是否已初始化
        if (!OSETSDK.getInstance().isInit()) {
            Log.w(TAG, "广告SDK未初始化，无法显示广告")
            callback.onAdLoadFailed("广告SDK未初始化")
            return
        }

        try {
            // 国外SDK使用直接showAd方式（参考官方示例）
            OSETRewardVideo.getInstance()
                .setContext(activity)
                .setUserId("mountain_survival_user")
                .setPosId(MountainSurvivalApplication.REWARD_VIDEO_AD_ID)
                .showAd(activity, object : OSETRewardListener {
                    override fun onShow(adId: String?) {
                        Log.d(TAG, "激励视频广告开始播放, adId: $adId")
                        callback.onAdLoadSuccess()
                        callback.onAdShowSuccess()
                    }

                    override fun onError(errorCode: String?, errorMessage: String?) {
                        Log.e(TAG, "激励视频广告失败: $errorCode - $errorMessage")

                        // 分析错误原因
                        val errorAnalysis = when {
                            errorMessage?.contains("成功") == true -> "可能是没有可用的广告源，建议添加广告渠道适配器"
                            errorCode == "1" -> "广告加载失败，可能是网络问题或广告源配置问题"
                            else -> "未知错误"
                        }

                        Log.w(TAG, "错误分析: $errorAnalysis")
                        callback.onAdLoadFailed("广告失败: $errorMessage ($errorAnalysis)")
                    }

                    override fun onClick() {
                        Log.d(TAG, "激励视频广告被点击")
                    }

                    override fun onClose(adId: String?) {
                        Log.d(TAG, "激励视频广告关闭, adId: $adId")
                        callback.onAdClosed()
                    }

                    override fun onVideoStart() {
                        Log.d(TAG, "激励视频开始播放")
                    }

                    override fun onVideoEnd(adId: String?) {
                        Log.d(TAG, "激励视频播放完成, adId: $adId")
                    }

                    override fun onReward(adId: String?, reward: Double) {
                        Log.d(TAG, "激励视频广告奖励触发，资源类型: ${resourceType.displayName}, adId: $adId, reward: $reward")
                        callback.onAdRewarded(resourceType)
                    }

                    override fun onServiceResponse(responseCode: Int) {
                        Log.d(TAG, "激励视频广告服务响应: $responseCode")
                    }
                })
        } catch (e: Exception) {
            Log.e(TAG, "激励视频广告调用异常", e)
            callback.onAdLoadFailed("广告调用异常: ${e.message}")
        }
    }

    /**
     * 显示复活广告
     * @param activity 当前Activity
     * @param callback 复活广告回调
     */
    fun showReviveAd(
        activity: Activity,
        callback: ReviveAdCallback
    ) {
        Log.d(TAG, "开始加载复活广告")

        // 检查广告SDK是否已初始化
        if (!OSETSDK.getInstance().isInit()) {
            Log.w(TAG, "广告SDK未初始化，无法显示复活广告")
            callback.onAdLoadFailed("广告SDK未初始化")
            return
        }

        try {
            // 国外SDK使用直接showAd方式（参考官方示例）
            OSETRewardVideo.getInstance()
                .setContext(activity)
                .setUserId("mountain_survival_user")
                .setPosId(MountainSurvivalApplication.REWARD_VIDEO_AD_ID)
                .showAd(activity, object : OSETRewardListener {
                    override fun onShow(adId: String?) {
                        Log.d(TAG, "复活广告开始播放, adId: $adId")
                        callback.onAdLoadSuccess()
                        callback.onAdShowSuccess()
                    }

                    override fun onError(errorCode: String?, errorMessage: String?) {
                        Log.e(TAG, "复活广告失败: $errorCode - $errorMessage")

                        // 分析错误原因
                        val errorAnalysis = when {
                            errorMessage?.contains("成功") == true -> "可能是没有可用的广告源，建议添加广告渠道适配器"
                            errorCode == "1" -> "广告加载失败，可能是网络问题或广告源配置问题"
                            else -> "未知错误"
                        }

                        Log.w(TAG, "错误分析: $errorAnalysis")
                        callback.onAdLoadFailed("复活广告失败: $errorMessage ($errorAnalysis)")
                    }

                    override fun onClick() {
                        Log.d(TAG, "复活广告被点击")
                    }

                    override fun onClose(adId: String?) {
                        Log.d(TAG, "复活广告关闭, adId: $adId")
                        callback.onAdClosed()
                    }

                    override fun onVideoStart() {
                        Log.d(TAG, "复活广告视频开始播放")
                    }

                    override fun onVideoEnd(adId: String?) {
                        Log.d(TAG, "复活广告视频播放完成, adId: $adId")
                    }

                    override fun onReward(adId: String?, reward: Double) {
                        Log.d(TAG, "复活广告奖励触发，角色复活, adId: $adId, reward: $reward")
                        callback.onReviveSuccess()
                    }

                    override fun onServiceResponse(responseCode: Int) {
                        Log.d(TAG, "复活广告服务响应: $responseCode")
                    }
                })
        } catch (e: Exception) {
            Log.e(TAG, "复活广告调用异常", e)
            callback.onAdLoadFailed("复活广告调用异常: ${e.message}")
        }
    }

    /**
     * 预加载激励视频广告
     * @param activity 当前Activity
     */
    fun preloadRewardVideo(activity: Activity) {
        Log.d(TAG, "预加载激励视频广告")

        // 检查广告SDK是否已初始化
        if (!OSETSDK.getInstance().isInit()) {
            Log.w(TAG, "广告SDK未初始化，跳过预加载")
            return
        }

        try {
            OSETRewardVideo.getInstance()
                .setContext(activity)
                .setUserId("mountain_survival_user")
                .setPosId(MountainSurvivalApplication.REWARD_VIDEO_AD_ID)
                .startLoad()
        } catch (e: Exception) {
            Log.e(TAG, "预加载激励视频广告异常", e)
        }
    }

    /**
     * 预加载激励视频广告（用于Application中调用）
     * 注意：由于OSETRewardVideo需要Activity上下文，在Application中无法直接预加载
     * 建议在第一个Activity启动时调用preloadRewardVideo方法
     */
    fun preloadRewardedAd(context: Context) {
        Log.d(TAG, "注意：OSETRewardVideo需要Activity上下文，无法在Application中预加载")
        Log.d(TAG, "建议在Activity中调用preloadRewardVideo方法进行预加载")
    }
}
