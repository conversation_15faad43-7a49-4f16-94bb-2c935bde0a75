<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#80000000"
    android:gravity="center"
    android:padding="20dp">

    <!-- 隐私政策弹窗 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/dialog_background"
        android:padding="24dp"
        android:layout_marginHorizontal="16dp">

        <!-- 标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/privacy_policy_title"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:gravity="center"
            android:layout_marginBottom="8dp" />

        <!-- 副标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/privacy_policy_subtitle"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#666666"
            android:gravity="center"
            android:layout_marginBottom="16dp" />

        <!-- 内容区域 - 可滚动 -->
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:maxHeight="400dp"
            android:layout_marginBottom="20dp">

            <TextView
                android:id="@+id/privacyContentTextView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/privacy_policy_content"
                android:textSize="14sp"
                android:textColor="#333333"
                android:lineSpacingExtra="4dp"
                android:padding="8dp" />

        </ScrollView>

        <!-- 按钮区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <!-- 不同意按钮 -->
            <Button
                android:id="@+id/disagreeButton"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:text="@string/privacy_policy_disagree"
                android:textColor="#666666"
                android:textSize="16sp"
                android:background="@drawable/button_background_gray"
                android:layout_marginEnd="8dp" />

            <!-- 同意按钮 -->
            <Button
                android:id="@+id/agreeButton"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:text="@string/privacy_policy_agree"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:background="@drawable/button_background_black"
                android:layout_marginStart="8dp" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
