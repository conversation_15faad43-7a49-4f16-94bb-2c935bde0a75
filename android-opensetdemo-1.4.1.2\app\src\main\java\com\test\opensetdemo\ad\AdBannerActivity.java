package com.test.opensetdemo.ad;

import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.Toast;

import com.kc.openset.ad.banner.OSETBanner;
import com.kc.openset.ad.listener.OSETBannerListener;
import com.test.opensetdemo.BaseActivity;
import com.test.opensetdemo.Common;
import com.test.opensetdemo.R;

public class AdBannerActivity extends BaseActivity {

    private Button btnShow;
    private FrameLayout flBanner;

    @Override
    public int getLayoutId() {
        return R.layout.activity_banner;
    }

    @Override
    public void initView() {
        initTitle("Banner");
        flBanner = findViewById(R.id.fl);
        btnShow = findViewById(R.id.btn_show);
        btnShow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                flBanner.removeAllViews();
                showBanner();
            }
        });
    }

    private void showBanner() {
        OSETBanner.getInstance().setContext(this).setPosId(Common.POS_ID_Banner).loadAd(this, new OSETBannerListener() {

            @Override
            public void onError(String s, String s1) {
                Toast.makeText(activity, "onError", Toast.LENGTH_SHORT).show();
                Log.e("openseterror", "code:" + s + "----message:" + s1);
            }

            @Override
            public void onShow(View view) {
                Toast.makeText(activity, "onShow", Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onClose(View view) {
                Toast.makeText(activity, "onClose", Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onClick(View view) {
                Toast.makeText(activity, "onClick", Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onLoad(View view) {
                flBanner.addView(view);
            }

        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }
}
