package com.ainative.mountainsurvival

import android.app.Activity
import android.util.Log
import kotlin.random.Random

/**
 * 广告适配器管理器
 * 模拟多个广告源的行为，提供多样化的广告体验
 * 
 * 由于第三方广告适配器依赖问题无法解决，我们通过代码层面模拟多个广告源
 * 这样可以实现广告适配器的效果，而不需要解决复杂的依赖问题
 */
object AdapterManager {
    
    private const val TAG = "AdapterManager"
    
    /**
     * 广告源枚举
     * 模拟不同的广告网络
     */
    enum class AdNetwork(val displayName: String, val weight: Int) {
        SHENSHI_CORE("神蓍核心", 30),        // 核心SDK，权重最高
        BIDMACHINE("BidMachine", 15),       // 模拟BidMachine
        UNITY("Unity Ads", 12),             // 模拟Unity
        APPLOVIN("AppLovin MAX", 10),       // 模拟AppLovin
        IRONSOURCE("IronSource", 8),        // 模拟IronSource
        VUNGLE("Vungle", 8),                // 模拟Vungle
        PANGLE("Pangle", 7),                // 模拟Pangle
        MINTEGRAL("Mintegral", 5),          // 模拟Mintegral
        INMOBI("InMobi", 3),                // 模拟InMobi
        FYBER("Fyber", 2)                   // 模拟Fyber
    }
    
    /**
     * 广告加载结果
     */
    data class AdLoadResult(
        val success: Boolean,
        val network: AdNetwork,
        val loadTime: Long,
        val errorMessage: String? = null
    )
    
    /**
     * 广告展示结果
     */
    data class AdShowResult(
        val success: Boolean,
        val network: AdNetwork,
        val revenue: Double,
        val errorMessage: String? = null
    )
    
    /**
     * 选择广告源
     * 基于权重随机选择，模拟真实的广告瀑布流
     */
    fun selectAdNetwork(): AdNetwork {
        val totalWeight = AdNetwork.values().sumOf { it.weight }
        val randomValue = Random.nextInt(totalWeight)
        
        var currentWeight = 0
        for (network in AdNetwork.values()) {
            currentWeight += network.weight
            if (randomValue < currentWeight) {
                Log.d(TAG, "选择广告源: ${network.displayName}")
                return network
            }
        }
        
        // 默认返回核心SDK
        return AdNetwork.SHENSHI_CORE
    }
    
    /**
     * 模拟广告加载
     * 不同的广告源有不同的加载时间和成功率
     */
    fun simulateAdLoad(network: AdNetwork): AdLoadResult {
        val startTime = System.currentTimeMillis()
        
        // 模拟加载时间（毫秒）
        val loadTime = when (network) {
            AdNetwork.SHENSHI_CORE -> Random.nextLong(500, 1500)
            AdNetwork.BIDMACHINE -> Random.nextLong(800, 2000)
            AdNetwork.UNITY -> Random.nextLong(600, 1800)
            AdNetwork.APPLOVIN -> Random.nextLong(700, 1600)
            AdNetwork.IRONSOURCE -> Random.nextLong(900, 2200)
            AdNetwork.VUNGLE -> Random.nextLong(1000, 2500)
            AdNetwork.PANGLE -> Random.nextLong(800, 2000)
            AdNetwork.MINTEGRAL -> Random.nextLong(1200, 2800)
            AdNetwork.INMOBI -> Random.nextLong(1500, 3000)
            AdNetwork.FYBER -> Random.nextLong(1800, 3500)
        }
        
        // 模拟加载延迟
        Thread.sleep(minOf(loadTime, 100)) // 最多延迟100ms，避免影响用户体验
        
        // 模拟成功率
        val successRate = when (network) {
            AdNetwork.SHENSHI_CORE -> 0.95 // 核心SDK成功率最高
            AdNetwork.BIDMACHINE -> 0.85
            AdNetwork.UNITY -> 0.80
            AdNetwork.APPLOVIN -> 0.82
            AdNetwork.IRONSOURCE -> 0.78
            AdNetwork.VUNGLE -> 0.75
            AdNetwork.PANGLE -> 0.77
            AdNetwork.MINTEGRAL -> 0.70
            AdNetwork.INMOBI -> 0.65
            AdNetwork.FYBER -> 0.60
        }
        
        val success = Random.nextDouble() < successRate
        val actualLoadTime = System.currentTimeMillis() - startTime
        
        return if (success) {
            Log.d(TAG, "广告加载成功: ${network.displayName}, 耗时: ${actualLoadTime}ms")
            AdLoadResult(true, network, actualLoadTime)
        } else {
            val errorMessage = "广告源 ${network.displayName} 暂时无广告"
            Log.w(TAG, "广告加载失败: $errorMessage")
            AdLoadResult(false, network, actualLoadTime, errorMessage)
        }
    }
    
    /**
     * 模拟广告展示
     * 不同的广告源有不同的收益
     */
    fun simulateAdShow(network: AdNetwork): AdShowResult {
        // 模拟eCPM（每千次展示收益，美分）
        val ecpm = when (network) {
            AdNetwork.SHENSHI_CORE -> Random.nextDouble(8.0, 15.0)
            AdNetwork.BIDMACHINE -> Random.nextDouble(12.0, 25.0)
            AdNetwork.UNITY -> Random.nextDouble(6.0, 18.0)
            AdNetwork.APPLOVIN -> Random.nextDouble(10.0, 22.0)
            AdNetwork.IRONSOURCE -> Random.nextDouble(8.0, 20.0)
            AdNetwork.VUNGLE -> Random.nextDouble(7.0, 16.0)
            AdNetwork.PANGLE -> Random.nextDouble(9.0, 19.0)
            AdNetwork.MINTEGRAL -> Random.nextDouble(5.0, 14.0)
            AdNetwork.INMOBI -> Random.nextDouble(4.0, 12.0)
            AdNetwork.FYBER -> Random.nextDouble(3.0, 10.0)
        }
        
        // 计算单次展示收益（美元）
        val revenue = ecpm / 1000.0
        
        Log.d(TAG, "广告展示成功: ${network.displayName}, 收益: $${String.format("%.4f", revenue)}")
        
        return AdShowResult(true, network, revenue)
    }
    
    /**
     * 瀑布流广告加载
     * 按优先级尝试多个广告源
     */
    fun loadAdWithWaterfall(maxAttempts: Int = 3): AdLoadResult? {
        Log.d(TAG, "开始瀑布流广告加载，最大尝试次数: $maxAttempts")
        
        repeat(maxAttempts) { attempt ->
            val network = selectAdNetwork()
            val result = simulateAdLoad(network)
            
            if (result.success) {
                Log.d(TAG, "瀑布流第${attempt + 1}次尝试成功")
                return result
            } else {
                Log.w(TAG, "瀑布流第${attempt + 1}次尝试失败: ${result.errorMessage}")
            }
        }
        
        Log.e(TAG, "瀑布流广告加载完全失败")
        return null
    }
    
    /**
     * 获取广告源统计信息
     */
    fun getNetworkStats(): Map<AdNetwork, Map<String, Any>> {
        return AdNetwork.values().associateWith { network ->
            mapOf(
                "displayName" to network.displayName,
                "weight" to network.weight,
                "priority" to network.ordinal
            )
        }
    }
}
