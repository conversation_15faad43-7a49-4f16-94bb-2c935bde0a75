package com.test.opensetdemo;

import android.app.Application;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;
import android.util.Log;
import android.webkit.WebView;

import androidx.multidex.MultiDex;

import com.kc.openset.config.OSETSDK;
import com.kc.openset.listener.OSETInitListener;
//import com.tencent.bugly.crashreport.CrashReport;


public class MyApplication extends Application {

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            Log.e("aaaaaa", "进程名：" + getProcessName());
        }
    }

    @Override
    public void onCreate() {
        super.onCreate();

        MultiDex.install(this);

        Log.e("aaaaaaaadfsdf", "当前版本号：" + OSETSDK.getInstance().getVersionName() + "——android O版本号：" + Build.VERSION_CODES.O);
    }


    public void setuserId(String userId) {
        putString(this, "userId", userId);
    }

    public String getUserId() {
        String userId;
        if (getString(this, "userId").equals("")) {
            userId = createRandom();
            setuserId(userId);
        } else {
            userId = getString(this, "userId");
        }
        return userId;
    }

    public static void putString(Context context, String key, String value) {
        if (context == null) {
            return;
        }
        SharedPreferences sharedPreferences = context.getSharedPreferences("adsetDemo", 0);
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putString(key, value);
        editor.commit();
    }

    public static String getString(Context context, String key) {
        if (context == null) {
            return "";
        }
        SharedPreferences sharedPreferences = context.getSharedPreferences("adsetDemo", 0);
        return sharedPreferences.getString(key, "");
    }

    /**
     * 生成16位随机码
     *
     * @return
     */
    public static String createRandom() {
        String a = System.currentTimeMillis() + "";
        String str = "";
        for (int i = 0; i < 8; i++) {
            // 你想生成几个字符的，就把9改成几，如果改成１,那就生成一个随机字母．
            str = str + (char) (Math.random() * 26 + 'a');
        }
        a += str;
        return a;
    }
}
