int anim abc_fade_in 0x7f010000
int anim abc_fade_out 0x7f010001
int anim abc_grow_fade_in_from_bottom 0x7f010002
int anim abc_popup_enter 0x7f010003
int anim abc_popup_exit 0x7f010004
int anim abc_shrink_fade_out_from_bottom 0x7f010005
int anim abc_slide_in_bottom 0x7f010006
int anim abc_slide_in_top 0x7f010007
int anim abc_slide_out_bottom 0x7f010008
int anim abc_slide_out_top 0x7f010009
int anim abc_tooltip_enter 0x7f01000a
int anim abc_tooltip_exit 0x7f01000b
int anim btn_checkbox_to_checked_box_inner_merged_animation 0x7f01000c
int anim btn_checkbox_to_checked_box_outer_merged_animation 0x7f01000d
int anim btn_checkbox_to_checked_icon_null_animation 0x7f01000e
int anim btn_checkbox_to_unchecked_box_inner_merged_animation 0x7f01000f
int anim btn_checkbox_to_unchecked_check_path_merged_animation 0x7f010010
int anim btn_checkbox_to_unchecked_icon_null_animation 0x7f010011
int anim btn_radio_to_off_mtrl_dot_group_animation 0x7f010012
int anim btn_radio_to_off_mtrl_ring_outer_animation 0x7f010013
int anim btn_radio_to_off_mtrl_ring_outer_path_animation 0x7f010014
int anim btn_radio_to_on_mtrl_dot_group_animation 0x7f010015
int anim btn_radio_to_on_mtrl_ring_outer_animation 0x7f010016
int anim btn_radio_to_on_mtrl_ring_outer_path_animation 0x7f010017
int anim design_bottom_sheet_slide_in 0x7f010018
int anim design_bottom_sheet_slide_out 0x7f010019
int anim design_snackbar_in 0x7f01001a
int anim design_snackbar_out 0x7f01001b
int anim fragment_fast_out_extra_slow_in 0x7f01001c
int anim linear_indeterminate_line1_head_interpolator 0x7f01001d
int anim linear_indeterminate_line1_tail_interpolator 0x7f01001e
int anim linear_indeterminate_line2_head_interpolator 0x7f01001f
int anim linear_indeterminate_line2_tail_interpolator 0x7f010020
int anim m3_bottom_sheet_slide_in 0x7f010021
int anim m3_bottom_sheet_slide_out 0x7f010022
int anim m3_motion_fade_enter 0x7f010023
int anim m3_motion_fade_exit 0x7f010024
int anim m3_side_sheet_slide_in 0x7f010025
int anim m3_side_sheet_slide_out 0x7f010026
int anim mtrl_bottom_sheet_slide_in 0x7f010027
int anim mtrl_bottom_sheet_slide_out 0x7f010028
int anim mtrl_card_lowers_interpolator 0x7f010029
int animator design_appbar_state_list_animator 0x7f020000
int animator design_fab_hide_motion_spec 0x7f020001
int animator design_fab_show_motion_spec 0x7f020002
int animator fragment_close_enter 0x7f020003
int animator fragment_close_exit 0x7f020004
int animator fragment_fade_enter 0x7f020005
int animator fragment_fade_exit 0x7f020006
int animator fragment_open_enter 0x7f020007
int animator fragment_open_exit 0x7f020008
int animator m3_appbar_state_list_animator 0x7f020009
int animator m3_btn_elevated_btn_state_list_anim 0x7f02000a
int animator m3_btn_state_list_anim 0x7f02000b
int animator m3_card_elevated_state_list_anim 0x7f02000c
int animator m3_card_state_list_anim 0x7f02000d
int animator m3_chip_state_list_anim 0x7f02000e
int animator m3_elevated_chip_state_list_anim 0x7f02000f
int animator m3_extended_fab_change_size_collapse_motion_spec 0x7f020010
int animator m3_extended_fab_change_size_expand_motion_spec 0x7f020011
int animator m3_extended_fab_hide_motion_spec 0x7f020012
int animator m3_extended_fab_show_motion_spec 0x7f020013
int animator m3_extended_fab_state_list_animator 0x7f020014
int animator mtrl_btn_state_list_anim 0x7f020015
int animator mtrl_btn_unelevated_state_list_anim 0x7f020016
int animator mtrl_card_state_list_anim 0x7f020017
int animator mtrl_chip_state_list_anim 0x7f020018
int animator mtrl_extended_fab_change_size_collapse_motion_spec 0x7f020019
int animator mtrl_extended_fab_change_size_expand_motion_spec 0x7f02001a
int animator mtrl_extended_fab_hide_motion_spec 0x7f02001b
int animator mtrl_extended_fab_show_motion_spec 0x7f02001c
int animator mtrl_extended_fab_state_list_animator 0x7f02001d
int animator mtrl_fab_hide_motion_spec 0x7f02001e
int animator mtrl_fab_show_motion_spec 0x7f02001f
int animator mtrl_fab_transformation_sheet_collapse_spec 0x7f020020
int animator mtrl_fab_transformation_sheet_expand_spec 0x7f020021
int attr SharedValue 0x7f030000
int attr SharedValueId 0x7f030001
int attr actionBarDivider 0x7f030002
int attr actionBarItemBackground 0x7f030003
int attr actionBarPopupTheme 0x7f030004
int attr actionBarSize 0x7f030005
int attr actionBarSplitStyle 0x7f030006
int attr actionBarStyle 0x7f030007
int attr actionBarTabBarStyle 0x7f030008
int attr actionBarTabStyle 0x7f030009
int attr actionBarTabTextStyle 0x7f03000a
int attr actionBarTheme 0x7f03000b
int attr actionBarWidgetTheme 0x7f03000c
int attr actionButtonStyle 0x7f03000d
int attr actionDropDownStyle 0x7f03000e
int attr actionLayout 0x7f03000f
int attr actionMenuTextAppearance 0x7f030010
int attr actionMenuTextColor 0x7f030011
int attr actionModeBackground 0x7f030012
int attr actionModeCloseButtonStyle 0x7f030013
int attr actionModeCloseContentDescription 0x7f030014
int attr actionModeCloseDrawable 0x7f030015
int attr actionModeCopyDrawable 0x7f030016
int attr actionModeCutDrawable 0x7f030017
int attr actionModeFindDrawable 0x7f030018
int attr actionModePasteDrawable 0x7f030019
int attr actionModePopupWindowStyle 0x7f03001a
int attr actionModeSelectAllDrawable 0x7f03001b
int attr actionModeShareDrawable 0x7f03001c
int attr actionModeSplitBackground 0x7f03001d
int attr actionModeStyle 0x7f03001e
int attr actionModeTheme 0x7f03001f
int attr actionModeWebSearchDrawable 0x7f030020
int attr actionOverflowButtonStyle 0x7f030021
int attr actionOverflowMenuStyle 0x7f030022
int attr actionProviderClass 0x7f030023
int attr actionTextColorAlpha 0x7f030024
int attr actionViewClass 0x7f030025
int attr activityChooserViewStyle 0x7f030026
int attr addElevationShadow 0x7f030027
int attr alertDialogButtonGroupStyle 0x7f030028
int attr alertDialogCenterButtons 0x7f030029
int attr alertDialogStyle 0x7f03002a
int attr alertDialogTheme 0x7f03002b
int attr allowStacking 0x7f03002c
int attr alpha 0x7f03002d
int attr alphabeticModifiers 0x7f03002e
int attr altSrc 0x7f03002f
int attr animateCircleAngleTo 0x7f030030
int attr animateMenuItems 0x7f030031
int attr animateNavigationIcon 0x7f030032
int attr animateRelativeTo 0x7f030033
int attr animationMode 0x7f030034
int attr appBarLayoutStyle 0x7f030035
int attr applyMotionScene 0x7f030036
int attr arcMode 0x7f030037
int attr arrowHeadLength 0x7f030038
int attr arrowShaftLength 0x7f030039
int attr attributeName 0x7f03003a
int attr autoCompleteMode 0x7f03003b
int attr autoCompleteTextViewStyle 0x7f03003c
int attr autoShowKeyboard 0x7f03003d
int attr autoSizeMaxTextSize 0x7f03003e
int attr autoSizeMinTextSize 0x7f03003f
int attr autoSizePresetSizes 0x7f030040
int attr autoSizeStepGranularity 0x7f030041
int attr autoSizeTextType 0x7f030042
int attr autoTransition 0x7f030043
int attr background 0x7f030044
int attr backgroundColor 0x7f030045
int attr backgroundInsetBottom 0x7f030046
int attr backgroundInsetEnd 0x7f030047
int attr backgroundInsetStart 0x7f030048
int attr backgroundInsetTop 0x7f030049
int attr backgroundOverlayColorAlpha 0x7f03004a
int attr backgroundSplit 0x7f03004b
int attr backgroundStacked 0x7f03004c
int attr backgroundTint 0x7f03004d
int attr backgroundTintMode 0x7f03004e
int attr badgeGravity 0x7f03004f
int attr badgeHeight 0x7f030050
int attr badgeRadius 0x7f030051
int attr badgeShapeAppearance 0x7f030052
int attr badgeShapeAppearanceOverlay 0x7f030053
int attr badgeStyle 0x7f030054
int attr badgeTextAppearance 0x7f030055
int attr badgeTextColor 0x7f030056
int attr badgeWidePadding 0x7f030057
int attr badgeWidth 0x7f030058
int attr badgeWithTextHeight 0x7f030059
int attr badgeWithTextRadius 0x7f03005a
int attr badgeWithTextShapeAppearance 0x7f03005b
int attr badgeWithTextShapeAppearanceOverlay 0x7f03005c
int attr badgeWithTextWidth 0x7f03005d
int attr barLength 0x7f03005e
int attr barrierAllowsGoneWidgets 0x7f03005f
int attr barrierDirection 0x7f030060
int attr barrierMargin 0x7f030061
int attr behavior_autoHide 0x7f030062
int attr behavior_autoShrink 0x7f030063
int attr behavior_draggable 0x7f030064
int attr behavior_expandedOffset 0x7f030065
int attr behavior_fitToContents 0x7f030066
int attr behavior_halfExpandedRatio 0x7f030067
int attr behavior_hideable 0x7f030068
int attr behavior_overlapTop 0x7f030069
int attr behavior_peekHeight 0x7f03006a
int attr behavior_saveFlags 0x7f03006b
int attr behavior_significantVelocityThreshold 0x7f03006c
int attr behavior_skipCollapsed 0x7f03006d
int attr blendSrc 0x7f03006e
int attr borderRound 0x7f03006f
int attr borderRoundPercent 0x7f030070
int attr borderWidth 0x7f030071
int attr borderlessButtonStyle 0x7f030072
int attr bottomAppBarStyle 0x7f030073
int attr bottomInsetScrimEnabled 0x7f030074
int attr bottomNavigationStyle 0x7f030075
int attr bottomSheetDialogTheme 0x7f030076
int attr bottomSheetDragHandleStyle 0x7f030077
int attr bottomSheetStyle 0x7f030078
int attr boxBackgroundColor 0x7f030079
int attr boxBackgroundMode 0x7f03007a
int attr boxCollapsedPaddingTop 0x7f03007b
int attr boxCornerRadiusBottomEnd 0x7f03007c
int attr boxCornerRadiusBottomStart 0x7f03007d
int attr boxCornerRadiusTopEnd 0x7f03007e
int attr boxCornerRadiusTopStart 0x7f03007f
int attr boxStrokeColor 0x7f030080
int attr boxStrokeErrorColor 0x7f030081
int attr boxStrokeWidth 0x7f030082
int attr boxStrokeWidthFocused 0x7f030083
int attr brightness 0x7f030084
int attr buttonBarButtonStyle 0x7f030085
int attr buttonBarNegativeButtonStyle 0x7f030086
int attr buttonBarNeutralButtonStyle 0x7f030087
int attr buttonBarPositiveButtonStyle 0x7f030088
int attr buttonBarStyle 0x7f030089
int attr buttonCompat 0x7f03008a
int attr buttonGravity 0x7f03008b
int attr buttonIcon 0x7f03008c
int attr buttonIconDimen 0x7f03008d
int attr buttonIconTint 0x7f03008e
int attr buttonIconTintMode 0x7f03008f
int attr buttonPanelSideLayout 0x7f030090
int attr buttonStyle 0x7f030091
int attr buttonStyleSmall 0x7f030092
int attr buttonTint 0x7f030093
int attr buttonTintMode 0x7f030094
int attr cardBackgroundColor 0x7f030095
int attr cardCornerRadius 0x7f030096
int attr cardElevation 0x7f030097
int attr cardForegroundColor 0x7f030098
int attr cardMaxElevation 0x7f030099
int attr cardPreventCornerOverlap 0x7f03009a
int attr cardUseCompatPadding 0x7f03009b
int attr cardViewStyle 0x7f03009c
int attr carousel_backwardTransition 0x7f03009d
int attr carousel_emptyViewsBehavior 0x7f03009e
int attr carousel_firstView 0x7f03009f
int attr carousel_forwardTransition 0x7f0300a0
int attr carousel_infinite 0x7f0300a1
int attr carousel_nextState 0x7f0300a2
int attr carousel_previousState 0x7f0300a3
int attr carousel_touchUpMode 0x7f0300a4
int attr carousel_touchUp_dampeningFactor 0x7f0300a5
int attr carousel_touchUp_velocityThreshold 0x7f0300a6
int attr centerIfNoTextEnabled 0x7f0300a7
int attr chainUseRtl 0x7f0300a8
int attr checkMarkCompat 0x7f0300a9
int attr checkMarkTint 0x7f0300aa
int attr checkMarkTintMode 0x7f0300ab
int attr checkboxStyle 0x7f0300ac
int attr checkedButton 0x7f0300ad
int attr checkedChip 0x7f0300ae
int attr checkedIcon 0x7f0300af
int attr checkedIconEnabled 0x7f0300b0
int attr checkedIconGravity 0x7f0300b1
int attr checkedIconMargin 0x7f0300b2
int attr checkedIconSize 0x7f0300b3
int attr checkedIconTint 0x7f0300b4
int attr checkedIconVisible 0x7f0300b5
int attr checkedState 0x7f0300b6
int attr checkedTextViewStyle 0x7f0300b7
int attr chipBackgroundColor 0x7f0300b8
int attr chipCornerRadius 0x7f0300b9
int attr chipEndPadding 0x7f0300ba
int attr chipGroupStyle 0x7f0300bb
int attr chipIcon 0x7f0300bc
int attr chipIconEnabled 0x7f0300bd
int attr chipIconSize 0x7f0300be
int attr chipIconTint 0x7f0300bf
int attr chipIconVisible 0x7f0300c0
int attr chipMinHeight 0x7f0300c1
int attr chipMinTouchTargetSize 0x7f0300c2
int attr chipSpacing 0x7f0300c3
int attr chipSpacingHorizontal 0x7f0300c4
int attr chipSpacingVertical 0x7f0300c5
int attr chipStandaloneStyle 0x7f0300c6
int attr chipStartPadding 0x7f0300c7
int attr chipStrokeColor 0x7f0300c8
int attr chipStrokeWidth 0x7f0300c9
int attr chipStyle 0x7f0300ca
int attr chipSurfaceColor 0x7f0300cb
int attr circleRadius 0x7f0300cc
int attr circularProgressIndicatorStyle 0x7f0300cd
int attr circularflow_angles 0x7f0300ce
int attr circularflow_defaultAngle 0x7f0300cf
int attr circularflow_defaultRadius 0x7f0300d0
int attr circularflow_radiusInDP 0x7f0300d1
int attr circularflow_viewCenter 0x7f0300d2
int attr clearsTag 0x7f0300d3
int attr clickAction 0x7f0300d4
int attr clockFaceBackgroundColor 0x7f0300d5
int attr clockHandColor 0x7f0300d6
int attr clockIcon 0x7f0300d7
int attr clockNumberTextColor 0x7f0300d8
int attr closeIcon 0x7f0300d9
int attr closeIconEnabled 0x7f0300da
int attr closeIconEndPadding 0x7f0300db
int attr closeIconSize 0x7f0300dc
int attr closeIconStartPadding 0x7f0300dd
int attr closeIconTint 0x7f0300de
int attr closeIconVisible 0x7f0300df
int attr closeItemLayout 0x7f0300e0
int attr collapseContentDescription 0x7f0300e1
int attr collapseIcon 0x7f0300e2
int attr collapsedSize 0x7f0300e3
int attr collapsedTitleGravity 0x7f0300e4
int attr collapsedTitleTextAppearance 0x7f0300e5
int attr collapsedTitleTextColor 0x7f0300e6
int attr collapsingToolbarLayoutLargeSize 0x7f0300e7
int attr collapsingToolbarLayoutLargeStyle 0x7f0300e8
int attr collapsingToolbarLayoutMediumSize 0x7f0300e9
int attr collapsingToolbarLayoutMediumStyle 0x7f0300ea
int attr collapsingToolbarLayoutStyle 0x7f0300eb
int attr color 0x7f0300ec
int attr colorAccent 0x7f0300ed
int attr colorBackgroundFloating 0x7f0300ee
int attr colorButtonNormal 0x7f0300ef
int attr colorContainer 0x7f0300f0
int attr colorControlActivated 0x7f0300f1
int attr colorControlHighlight 0x7f0300f2
int attr colorControlNormal 0x7f0300f3
int attr colorError 0x7f0300f4
int attr colorErrorContainer 0x7f0300f5
int attr colorOnBackground 0x7f0300f6
int attr colorOnContainer 0x7f0300f7
int attr colorOnContainerUnchecked 0x7f0300f8
int attr colorOnError 0x7f0300f9
int attr colorOnErrorContainer 0x7f0300fa
int attr colorOnPrimary 0x7f0300fb
int attr colorOnPrimaryContainer 0x7f0300fc
int attr colorOnPrimaryFixed 0x7f0300fd
int attr colorOnPrimaryFixedVariant 0x7f0300fe
int attr colorOnPrimarySurface 0x7f0300ff
int attr colorOnSecondary 0x7f030100
int attr colorOnSecondaryContainer 0x7f030101
int attr colorOnSecondaryFixed 0x7f030102
int attr colorOnSecondaryFixedVariant 0x7f030103
int attr colorOnSurface 0x7f030104
int attr colorOnSurfaceInverse 0x7f030105
int attr colorOnSurfaceVariant 0x7f030106
int attr colorOnTertiary 0x7f030107
int attr colorOnTertiaryContainer 0x7f030108
int attr colorOnTertiaryFixed 0x7f030109
int attr colorOnTertiaryFixedVariant 0x7f03010a
int attr colorOutline 0x7f03010b
int attr colorOutlineVariant 0x7f03010c
int attr colorPrimary 0x7f03010d
int attr colorPrimaryContainer 0x7f03010e
int attr colorPrimaryDark 0x7f03010f
int attr colorPrimaryFixed 0x7f030110
int attr colorPrimaryFixedDim 0x7f030111
int attr colorPrimaryInverse 0x7f030112
int attr colorPrimarySurface 0x7f030113
int attr colorPrimaryVariant 0x7f030114
int attr colorSecondary 0x7f030115
int attr colorSecondaryContainer 0x7f030116
int attr colorSecondaryFixed 0x7f030117
int attr colorSecondaryFixedDim 0x7f030118
int attr colorSecondaryVariant 0x7f030119
int attr colorSurface 0x7f03011a
int attr colorSurfaceBright 0x7f03011b
int attr colorSurfaceContainer 0x7f03011c
int attr colorSurfaceContainerHigh 0x7f03011d
int attr colorSurfaceContainerHighest 0x7f03011e
int attr colorSurfaceContainerLow 0x7f03011f
int attr colorSurfaceContainerLowest 0x7f030120
int attr colorSurfaceDim 0x7f030121
int attr colorSurfaceInverse 0x7f030122
int attr colorSurfaceVariant 0x7f030123
int attr colorSwitchThumbNormal 0x7f030124
int attr colorTertiary 0x7f030125
int attr colorTertiaryContainer 0x7f030126
int attr colorTertiaryFixed 0x7f030127
int attr colorTertiaryFixedDim 0x7f030128
int attr commitIcon 0x7f030129
int attr compatShadowEnabled 0x7f03012a
int attr constraintRotate 0x7f03012b
int attr constraintSet 0x7f03012c
int attr constraintSetEnd 0x7f03012d
int attr constraintSetStart 0x7f03012e
int attr constraint_referenced_ids 0x7f03012f
int attr constraint_referenced_tags 0x7f030130
int attr constraints 0x7f030131
int attr content 0x7f030132
int attr contentDescription 0x7f030133
int attr contentInsetEnd 0x7f030134
int attr contentInsetEndWithActions 0x7f030135
int attr contentInsetLeft 0x7f030136
int attr contentInsetRight 0x7f030137
int attr contentInsetStart 0x7f030138
int attr contentInsetStartWithNavigation 0x7f030139
int attr contentPadding 0x7f03013a
int attr contentPaddingBottom 0x7f03013b
int attr contentPaddingEnd 0x7f03013c
int attr contentPaddingLeft 0x7f03013d
int attr contentPaddingRight 0x7f03013e
int attr contentPaddingStart 0x7f03013f
int attr contentPaddingTop 0x7f030140
int attr contentScrim 0x7f030141
int attr contrast 0x7f030142
int attr controlBackground 0x7f030143
int attr coordinatorLayoutStyle 0x7f030144
int attr coplanarSiblingViewId 0x7f030145
int attr cornerFamily 0x7f030146
int attr cornerFamilyBottomLeft 0x7f030147
int attr cornerFamilyBottomRight 0x7f030148
int attr cornerFamilyTopLeft 0x7f030149
int attr cornerFamilyTopRight 0x7f03014a
int attr cornerRadius 0x7f03014b
int attr cornerSize 0x7f03014c
int attr cornerSizeBottomLeft 0x7f03014d
int attr cornerSizeBottomRight 0x7f03014e
int attr cornerSizeTopLeft 0x7f03014f
int attr cornerSizeTopRight 0x7f030150
int attr counterEnabled 0x7f030151
int attr counterMaxLength 0x7f030152
int attr counterOverflowTextAppearance 0x7f030153
int attr counterOverflowTextColor 0x7f030154
int attr counterTextAppearance 0x7f030155
int attr counterTextColor 0x7f030156
int attr crossfade 0x7f030157
int attr currentState 0x7f030158
int attr curveFit 0x7f030159
int attr customBoolean 0x7f03015a
int attr customColorDrawableValue 0x7f03015b
int attr customColorValue 0x7f03015c
int attr customDimension 0x7f03015d
int attr customFloatValue 0x7f03015e
int attr customIntegerValue 0x7f03015f
int attr customNavigationLayout 0x7f030160
int attr customPixelDimension 0x7f030161
int attr customReference 0x7f030162
int attr customStringValue 0x7f030163
int attr dayInvalidStyle 0x7f030164
int attr daySelectedStyle 0x7f030165
int attr dayStyle 0x7f030166
int attr dayTodayStyle 0x7f030167
int attr defaultDuration 0x7f030168
int attr defaultMarginsEnabled 0x7f030169
int attr defaultQueryHint 0x7f03016a
int attr defaultScrollFlagsEnabled 0x7f03016b
int attr defaultState 0x7f03016c
int attr deltaPolarAngle 0x7f03016d
int attr deltaPolarRadius 0x7f03016e
int attr deriveConstraintsFrom 0x7f03016f
int attr dialogCornerRadius 0x7f030170
int attr dialogPreferredPadding 0x7f030171
int attr dialogTheme 0x7f030172
int attr displayOptions 0x7f030173
int attr divider 0x7f030174
int attr dividerColor 0x7f030175
int attr dividerHorizontal 0x7f030176
int attr dividerInsetEnd 0x7f030177
int attr dividerInsetStart 0x7f030178
int attr dividerPadding 0x7f030179
int attr dividerThickness 0x7f03017a
int attr dividerVertical 0x7f03017b
int attr dragDirection 0x7f03017c
int attr dragScale 0x7f03017d
int attr dragThreshold 0x7f03017e
int attr drawPath 0x7f03017f
int attr drawableBottomCompat 0x7f030180
int attr drawableEndCompat 0x7f030181
int attr drawableLeftCompat 0x7f030182
int attr drawableRightCompat 0x7f030183
int attr drawableSize 0x7f030184
int attr drawableStartCompat 0x7f030185
int attr drawableTint 0x7f030186
int attr drawableTintMode 0x7f030187
int attr drawableTopCompat 0x7f030188
int attr drawerArrowStyle 0x7f030189
int attr drawerLayoutCornerSize 0x7f03018a
int attr drawerLayoutStyle 0x7f03018b
int attr dropDownListViewStyle 0x7f03018c
int attr dropdownListPreferredItemHeight 0x7f03018d
int attr duration 0x7f03018e
int attr dynamicColorThemeOverlay 0x7f03018f
int attr editTextBackground 0x7f030190
int attr editTextColor 0x7f030191
int attr editTextStyle 0x7f030192
int attr elevation 0x7f030193
int attr elevationOverlayAccentColor 0x7f030194
int attr elevationOverlayColor 0x7f030195
int attr elevationOverlayEnabled 0x7f030196
int attr emojiCompatEnabled 0x7f030197
int attr enableEdgeToEdge 0x7f030198
int attr endIconCheckable 0x7f030199
int attr endIconContentDescription 0x7f03019a
int attr endIconDrawable 0x7f03019b
int attr endIconMinSize 0x7f03019c
int attr endIconMode 0x7f03019d
int attr endIconScaleType 0x7f03019e
int attr endIconTint 0x7f03019f
int attr endIconTintMode 0x7f0301a0
int attr enforceMaterialTheme 0x7f0301a1
int attr enforceTextAppearance 0x7f0301a2
int attr ensureMinTouchTargetSize 0x7f0301a3
int attr errorAccessibilityLabel 0x7f0301a4
int attr errorAccessibilityLiveRegion 0x7f0301a5
int attr errorContentDescription 0x7f0301a6
int attr errorEnabled 0x7f0301a7
int attr errorIconDrawable 0x7f0301a8
int attr errorIconTint 0x7f0301a9
int attr errorIconTintMode 0x7f0301aa
int attr errorShown 0x7f0301ab
int attr errorTextAppearance 0x7f0301ac
int attr errorTextColor 0x7f0301ad
int attr expandActivityOverflowButtonDrawable 0x7f0301ae
int attr expanded 0x7f0301af
int attr expandedHintEnabled 0x7f0301b0
int attr expandedTitleGravity 0x7f0301b1
int attr expandedTitleMargin 0x7f0301b2
int attr expandedTitleMarginBottom 0x7f0301b3
int attr expandedTitleMarginEnd 0x7f0301b4
int attr expandedTitleMarginStart 0x7f0301b5
int attr expandedTitleMarginTop 0x7f0301b6
int attr expandedTitleTextAppearance 0x7f0301b7
int attr expandedTitleTextColor 0x7f0301b8
int attr extendMotionSpec 0x7f0301b9
int attr extendStrategy 0x7f0301ba
int attr extendedFloatingActionButtonPrimaryStyle 0x7f0301bb
int attr extendedFloatingActionButtonSecondaryStyle 0x7f0301bc
int attr extendedFloatingActionButtonStyle 0x7f0301bd
int attr extendedFloatingActionButtonSurfaceStyle 0x7f0301be
int attr extendedFloatingActionButtonTertiaryStyle 0x7f0301bf
int attr extraMultilineHeightEnabled 0x7f0301c0
int attr fabAlignmentMode 0x7f0301c1
int attr fabAlignmentModeEndMargin 0x7f0301c2
int attr fabAnchorMode 0x7f0301c3
int attr fabAnimationMode 0x7f0301c4
int attr fabCradleMargin 0x7f0301c5
int attr fabCradleRoundedCornerRadius 0x7f0301c6
int attr fabCradleVerticalOffset 0x7f0301c7
int attr fabCustomSize 0x7f0301c8
int attr fabSize 0x7f0301c9
int attr fastScrollEnabled 0x7f0301ca
int attr fastScrollHorizontalThumbDrawable 0x7f0301cb
int attr fastScrollHorizontalTrackDrawable 0x7f0301cc
int attr fastScrollVerticalThumbDrawable 0x7f0301cd
int attr fastScrollVerticalTrackDrawable 0x7f0301ce
int attr firstBaselineToTopHeight 0x7f0301cf
int attr floatingActionButtonLargePrimaryStyle 0x7f0301d0
int attr floatingActionButtonLargeSecondaryStyle 0x7f0301d1
int attr floatingActionButtonLargeStyle 0x7f0301d2
int attr floatingActionButtonLargeSurfaceStyle 0x7f0301d3
int attr floatingActionButtonLargeTertiaryStyle 0x7f0301d4
int attr floatingActionButtonPrimaryStyle 0x7f0301d5
int attr floatingActionButtonSecondaryStyle 0x7f0301d6
int attr floatingActionButtonSmallPrimaryStyle 0x7f0301d7
int attr floatingActionButtonSmallSecondaryStyle 0x7f0301d8
int attr floatingActionButtonSmallStyle 0x7f0301d9
int attr floatingActionButtonSmallSurfaceStyle 0x7f0301da
int attr floatingActionButtonSmallTertiaryStyle 0x7f0301db
int attr floatingActionButtonStyle 0x7f0301dc
int attr floatingActionButtonSurfaceStyle 0x7f0301dd
int attr floatingActionButtonTertiaryStyle 0x7f0301de
int attr flow_firstHorizontalBias 0x7f0301df
int attr flow_firstHorizontalStyle 0x7f0301e0
int attr flow_firstVerticalBias 0x7f0301e1
int attr flow_firstVerticalStyle 0x7f0301e2
int attr flow_horizontalAlign 0x7f0301e3
int attr flow_horizontalBias 0x7f0301e4
int attr flow_horizontalGap 0x7f0301e5
int attr flow_horizontalStyle 0x7f0301e6
int attr flow_lastHorizontalBias 0x7f0301e7
int attr flow_lastHorizontalStyle 0x7f0301e8
int attr flow_lastVerticalBias 0x7f0301e9
int attr flow_lastVerticalStyle 0x7f0301ea
int attr flow_maxElementsWrap 0x7f0301eb
int attr flow_padding 0x7f0301ec
int attr flow_verticalAlign 0x7f0301ed
int attr flow_verticalBias 0x7f0301ee
int attr flow_verticalGap 0x7f0301ef
int attr flow_verticalStyle 0x7f0301f0
int attr flow_wrapMode 0x7f0301f1
int attr font 0x7f0301f2
int attr fontFamily 0x7f0301f3
int attr fontProviderAuthority 0x7f0301f4
int attr fontProviderCerts 0x7f0301f5
int attr fontProviderFetchStrategy 0x7f0301f6
int attr fontProviderFetchTimeout 0x7f0301f7
int attr fontProviderPackage 0x7f0301f8
int attr fontProviderQuery 0x7f0301f9
int attr fontProviderSystemFontFamily 0x7f0301fa
int attr fontStyle 0x7f0301fb
int attr fontVariationSettings 0x7f0301fc
int attr fontWeight 0x7f0301fd
int attr forceApplySystemWindowInsetTop 0x7f0301fe
int attr forceDefaultNavigationOnClickListener 0x7f0301ff
int attr foregroundInsidePadding 0x7f030200
int attr framePosition 0x7f030201
int attr gapBetweenBars 0x7f030202
int attr gestureInsetBottomIgnored 0x7f030203
int attr goIcon 0x7f030204
int attr guidelineUseRtl 0x7f030205
int attr haloColor 0x7f030206
int attr haloRadius 0x7f030207
int attr headerLayout 0x7f030208
int attr height 0x7f030209
int attr helperText 0x7f03020a
int attr helperTextEnabled 0x7f03020b
int attr helperTextTextAppearance 0x7f03020c
int attr helperTextTextColor 0x7f03020d
int attr hideAnimationBehavior 0x7f03020e
int attr hideMotionSpec 0x7f03020f
int attr hideNavigationIcon 0x7f030210
int attr hideOnContentScroll 0x7f030211
int attr hideOnScroll 0x7f030212
int attr hintAnimationEnabled 0x7f030213
int attr hintEnabled 0x7f030214
int attr hintTextAppearance 0x7f030215
int attr hintTextColor 0x7f030216
int attr homeAsUpIndicator 0x7f030217
int attr homeLayout 0x7f030218
int attr horizontalOffset 0x7f030219
int attr horizontalOffsetWithText 0x7f03021a
int attr hoveredFocusedTranslationZ 0x7f03021b
int attr icon 0x7f03021c
int attr iconEndPadding 0x7f03021d
int attr iconGravity 0x7f03021e
int attr iconPadding 0x7f03021f
int attr iconSize 0x7f030220
int attr iconStartPadding 0x7f030221
int attr iconTint 0x7f030222
int attr iconTintMode 0x7f030223
int attr iconifiedByDefault 0x7f030224
int attr ifTagNotSet 0x7f030225
int attr ifTagSet 0x7f030226
int attr imageButtonStyle 0x7f030227
int attr imagePanX 0x7f030228
int attr imagePanY 0x7f030229
int attr imageRotate 0x7f03022a
int attr imageZoom 0x7f03022b
int attr indeterminateAnimationType 0x7f03022c
int attr indeterminateProgressStyle 0x7f03022d
int attr indicatorColor 0x7f03022e
int attr indicatorDirectionCircular 0x7f03022f
int attr indicatorDirectionLinear 0x7f030230
int attr indicatorInset 0x7f030231
int attr indicatorSize 0x7f030232
int attr initialActivityCount 0x7f030233
int attr insetForeground 0x7f030234
int attr isLightTheme 0x7f030235
int attr isMaterial3DynamicColorApplied 0x7f030236
int attr isMaterial3Theme 0x7f030237
int attr isMaterialTheme 0x7f030238
int attr itemActiveIndicatorStyle 0x7f030239
int attr itemBackground 0x7f03023a
int attr itemFillColor 0x7f03023b
int attr itemHorizontalPadding 0x7f03023c
int attr itemHorizontalTranslationEnabled 0x7f03023d
int attr itemIconPadding 0x7f03023e
int attr itemIconSize 0x7f03023f
int attr itemIconTint 0x7f030240
int attr itemMaxLines 0x7f030241
int attr itemMinHeight 0x7f030242
int attr itemPadding 0x7f030243
int attr itemPaddingBottom 0x7f030244
int attr itemPaddingTop 0x7f030245
int attr itemRippleColor 0x7f030246
int attr itemShapeAppearance 0x7f030247
int attr itemShapeAppearanceOverlay 0x7f030248
int attr itemShapeFillColor 0x7f030249
int attr itemShapeInsetBottom 0x7f03024a
int attr itemShapeInsetEnd 0x7f03024b
int attr itemShapeInsetStart 0x7f03024c
int attr itemShapeInsetTop 0x7f03024d
int attr itemSpacing 0x7f03024e
int attr itemStrokeColor 0x7f03024f
int attr itemStrokeWidth 0x7f030250
int attr itemTextAppearance 0x7f030251
int attr itemTextAppearanceActive 0x7f030252
int attr itemTextAppearanceInactive 0x7f030253
int attr itemTextColor 0x7f030254
int attr itemVerticalPadding 0x7f030255
int attr keyPositionType 0x7f030256
int attr keyboardIcon 0x7f030257
int attr keylines 0x7f030258
int attr lStar 0x7f030259
int attr labelBehavior 0x7f03025a
int attr labelStyle 0x7f03025b
int attr labelVisibilityMode 0x7f03025c
int attr lastBaselineToBottomHeight 0x7f03025d
int attr lastItemDecorated 0x7f03025e
int attr layout 0x7f03025f
int attr layoutDescription 0x7f030260
int attr layoutDuringTransition 0x7f030261
int attr layoutManager 0x7f030262
int attr layout_anchor 0x7f030263
int attr layout_anchorGravity 0x7f030264
int attr layout_behavior 0x7f030265
int attr layout_collapseMode 0x7f030266
int attr layout_collapseParallaxMultiplier 0x7f030267
int attr layout_constrainedHeight 0x7f030268
int attr layout_constrainedWidth 0x7f030269
int attr layout_constraintBaseline_creator 0x7f03026a
int attr layout_constraintBaseline_toBaselineOf 0x7f03026b
int attr layout_constraintBaseline_toBottomOf 0x7f03026c
int attr layout_constraintBaseline_toTopOf 0x7f03026d
int attr layout_constraintBottom_creator 0x7f03026e
int attr layout_constraintBottom_toBottomOf 0x7f03026f
int attr layout_constraintBottom_toTopOf 0x7f030270
int attr layout_constraintCircle 0x7f030271
int attr layout_constraintCircleAngle 0x7f030272
int attr layout_constraintCircleRadius 0x7f030273
int attr layout_constraintDimensionRatio 0x7f030274
int attr layout_constraintEnd_toEndOf 0x7f030275
int attr layout_constraintEnd_toStartOf 0x7f030276
int attr layout_constraintGuide_begin 0x7f030277
int attr layout_constraintGuide_end 0x7f030278
int attr layout_constraintGuide_percent 0x7f030279
int attr layout_constraintHeight 0x7f03027a
int attr layout_constraintHeight_default 0x7f03027b
int attr layout_constraintHeight_max 0x7f03027c
int attr layout_constraintHeight_min 0x7f03027d
int attr layout_constraintHeight_percent 0x7f03027e
int attr layout_constraintHorizontal_bias 0x7f03027f
int attr layout_constraintHorizontal_chainStyle 0x7f030280
int attr layout_constraintHorizontal_weight 0x7f030281
int attr layout_constraintLeft_creator 0x7f030282
int attr layout_constraintLeft_toLeftOf 0x7f030283
int attr layout_constraintLeft_toRightOf 0x7f030284
int attr layout_constraintRight_creator 0x7f030285
int attr layout_constraintRight_toLeftOf 0x7f030286
int attr layout_constraintRight_toRightOf 0x7f030287
int attr layout_constraintStart_toEndOf 0x7f030288
int attr layout_constraintStart_toStartOf 0x7f030289
int attr layout_constraintTag 0x7f03028a
int attr layout_constraintTop_creator 0x7f03028b
int attr layout_constraintTop_toBottomOf 0x7f03028c
int attr layout_constraintTop_toTopOf 0x7f03028d
int attr layout_constraintVertical_bias 0x7f03028e
int attr layout_constraintVertical_chainStyle 0x7f03028f
int attr layout_constraintVertical_weight 0x7f030290
int attr layout_constraintWidth 0x7f030291
int attr layout_constraintWidth_default 0x7f030292
int attr layout_constraintWidth_max 0x7f030293
int attr layout_constraintWidth_min 0x7f030294
int attr layout_constraintWidth_percent 0x7f030295
int attr layout_dodgeInsetEdges 0x7f030296
int attr layout_editor_absoluteX 0x7f030297
int attr layout_editor_absoluteY 0x7f030298
int attr layout_goneMarginBaseline 0x7f030299
int attr layout_goneMarginBottom 0x7f03029a
int attr layout_goneMarginEnd 0x7f03029b
int attr layout_goneMarginLeft 0x7f03029c
int attr layout_goneMarginRight 0x7f03029d
int attr layout_goneMarginStart 0x7f03029e
int attr layout_goneMarginTop 0x7f03029f
int attr layout_insetEdge 0x7f0302a0
int attr layout_keyline 0x7f0302a1
int attr layout_marginBaseline 0x7f0302a2
int attr layout_optimizationLevel 0x7f0302a3
int attr layout_scrollEffect 0x7f0302a4
int attr layout_scrollFlags 0x7f0302a5
int attr layout_scrollInterpolator 0x7f0302a6
int attr layout_wrapBehaviorInParent 0x7f0302a7
int attr liftOnScroll 0x7f0302a8
int attr liftOnScrollColor 0x7f0302a9
int attr liftOnScrollTargetViewId 0x7f0302aa
int attr limitBoundsTo 0x7f0302ab
int attr lineHeight 0x7f0302ac
int attr lineSpacing 0x7f0302ad
int attr linearProgressIndicatorStyle 0x7f0302ae
int attr listChoiceBackgroundIndicator 0x7f0302af
int attr listChoiceIndicatorMultipleAnimated 0x7f0302b0
int attr listChoiceIndicatorSingleAnimated 0x7f0302b1
int attr listDividerAlertDialog 0x7f0302b2
int attr listItemLayout 0x7f0302b3
int attr listLayout 0x7f0302b4
int attr listMenuViewStyle 0x7f0302b5
int attr listPopupWindowStyle 0x7f0302b6
int attr listPreferredItemHeight 0x7f0302b7
int attr listPreferredItemHeightLarge 0x7f0302b8
int attr listPreferredItemHeightSmall 0x7f0302b9
int attr listPreferredItemPaddingEnd 0x7f0302ba
int attr listPreferredItemPaddingLeft 0x7f0302bb
int attr listPreferredItemPaddingRight 0x7f0302bc
int attr listPreferredItemPaddingStart 0x7f0302bd
int attr logo 0x7f0302be
int attr logoAdjustViewBounds 0x7f0302bf
int attr logoDescription 0x7f0302c0
int attr logoScaleType 0x7f0302c1
int attr marginHorizontal 0x7f0302c2
int attr marginLeftSystemWindowInsets 0x7f0302c3
int attr marginRightSystemWindowInsets 0x7f0302c4
int attr marginTopSystemWindowInsets 0x7f0302c5
int attr materialAlertDialogBodyTextStyle 0x7f0302c6
int attr materialAlertDialogButtonSpacerVisibility 0x7f0302c7
int attr materialAlertDialogTheme 0x7f0302c8
int attr materialAlertDialogTitleIconStyle 0x7f0302c9
int attr materialAlertDialogTitlePanelStyle 0x7f0302ca
int attr materialAlertDialogTitleTextStyle 0x7f0302cb
int attr materialButtonOutlinedStyle 0x7f0302cc
int attr materialButtonStyle 0x7f0302cd
int attr materialButtonToggleGroupStyle 0x7f0302ce
int attr materialCalendarDay 0x7f0302cf
int attr materialCalendarDayOfWeekLabel 0x7f0302d0
int attr materialCalendarFullscreenTheme 0x7f0302d1
int attr materialCalendarHeaderCancelButton 0x7f0302d2
int attr materialCalendarHeaderConfirmButton 0x7f0302d3
int attr materialCalendarHeaderDivider 0x7f0302d4
int attr materialCalendarHeaderLayout 0x7f0302d5
int attr materialCalendarHeaderSelection 0x7f0302d6
int attr materialCalendarHeaderTitle 0x7f0302d7
int attr materialCalendarHeaderToggleButton 0x7f0302d8
int attr materialCalendarMonth 0x7f0302d9
int attr materialCalendarMonthNavigationButton 0x7f0302da
int attr materialCalendarStyle 0x7f0302db
int attr materialCalendarTheme 0x7f0302dc
int attr materialCalendarYearNavigationButton 0x7f0302dd
int attr materialCardViewElevatedStyle 0x7f0302de
int attr materialCardViewFilledStyle 0x7f0302df
int attr materialCardViewOutlinedStyle 0x7f0302e0
int attr materialCardViewStyle 0x7f0302e1
int attr materialCircleRadius 0x7f0302e2
int attr materialClockStyle 0x7f0302e3
int attr materialDisplayDividerStyle 0x7f0302e4
int attr materialDividerHeavyStyle 0x7f0302e5
int attr materialDividerStyle 0x7f0302e6
int attr materialIconButtonFilledStyle 0x7f0302e7
int attr materialIconButtonFilledTonalStyle 0x7f0302e8
int attr materialIconButtonOutlinedStyle 0x7f0302e9
int attr materialIconButtonStyle 0x7f0302ea
int attr materialSearchBarStyle 0x7f0302eb
int attr materialSearchViewPrefixStyle 0x7f0302ec
int attr materialSearchViewStyle 0x7f0302ed
int attr materialSwitchStyle 0x7f0302ee
int attr materialThemeOverlay 0x7f0302ef
int attr materialTimePickerStyle 0x7f0302f0
int attr materialTimePickerTheme 0x7f0302f1
int attr materialTimePickerTitleStyle 0x7f0302f2
int attr maxAcceleration 0x7f0302f3
int attr maxActionInlineWidth 0x7f0302f4
int attr maxButtonHeight 0x7f0302f5
int attr maxCharacterCount 0x7f0302f6
int attr maxHeight 0x7f0302f7
int attr maxImageSize 0x7f0302f8
int attr maxLines 0x7f0302f9
int attr maxVelocity 0x7f0302fa
int attr maxWidth 0x7f0302fb
int attr measureWithLargestChild 0x7f0302fc
int attr menu 0x7f0302fd
int attr menuAlignmentMode 0x7f0302fe
int attr menuGravity 0x7f0302ff
int attr methodName 0x7f030300
int attr minHeight 0x7f030301
int attr minHideDelay 0x7f030302
int attr minSeparation 0x7f030303
int attr minTouchTargetSize 0x7f030304
int attr minWidth 0x7f030305
int attr mock_diagonalsColor 0x7f030306
int attr mock_label 0x7f030307
int attr mock_labelBackgroundColor 0x7f030308
int attr mock_labelColor 0x7f030309
int attr mock_showDiagonals 0x7f03030a
int attr mock_showLabel 0x7f03030b
int attr motionDebug 0x7f03030c
int attr motionDurationExtraLong1 0x7f03030d
int attr motionDurationExtraLong2 0x7f03030e
int attr motionDurationExtraLong3 0x7f03030f
int attr motionDurationExtraLong4 0x7f030310
int attr motionDurationLong1 0x7f030311
int attr motionDurationLong2 0x7f030312
int attr motionDurationLong3 0x7f030313
int attr motionDurationLong4 0x7f030314
int attr motionDurationMedium1 0x7f030315
int attr motionDurationMedium2 0x7f030316
int attr motionDurationMedium3 0x7f030317
int attr motionDurationMedium4 0x7f030318
int attr motionDurationShort1 0x7f030319
int attr motionDurationShort2 0x7f03031a
int attr motionDurationShort3 0x7f03031b
int attr motionDurationShort4 0x7f03031c
int attr motionEasingAccelerated 0x7f03031d
int attr motionEasingDecelerated 0x7f03031e
int attr motionEasingEmphasized 0x7f03031f
int attr motionEasingEmphasizedAccelerateInterpolator 0x7f030320
int attr motionEasingEmphasizedDecelerateInterpolator 0x7f030321
int attr motionEasingEmphasizedInterpolator 0x7f030322
int attr motionEasingLinear 0x7f030323
int attr motionEasingLinearInterpolator 0x7f030324
int attr motionEasingStandard 0x7f030325
int attr motionEasingStandardAccelerateInterpolator 0x7f030326
int attr motionEasingStandardDecelerateInterpolator 0x7f030327
int attr motionEasingStandardInterpolator 0x7f030328
int attr motionEffect_alpha 0x7f030329
int attr motionEffect_end 0x7f03032a
int attr motionEffect_move 0x7f03032b
int attr motionEffect_start 0x7f03032c
int attr motionEffect_strict 0x7f03032d
int attr motionEffect_translationX 0x7f03032e
int attr motionEffect_translationY 0x7f03032f
int attr motionEffect_viewTransition 0x7f030330
int attr motionInterpolator 0x7f030331
int attr motionPath 0x7f030332
int attr motionPathRotate 0x7f030333
int attr motionProgress 0x7f030334
int attr motionStagger 0x7f030335
int attr motionTarget 0x7f030336
int attr motion_postLayoutCollision 0x7f030337
int attr motion_triggerOnCollision 0x7f030338
int attr moveWhenScrollAtTop 0x7f030339
int attr multiChoiceItemLayout 0x7f03033a
int attr navigationContentDescription 0x7f03033b
int attr navigationIcon 0x7f03033c
int attr navigationIconTint 0x7f03033d
int attr navigationMode 0x7f03033e
int attr navigationRailStyle 0x7f03033f
int attr navigationViewStyle 0x7f030340
int attr nestedScrollFlags 0x7f030341
int attr nestedScrollViewStyle 0x7f030342
int attr nestedScrollable 0x7f030343
int attr number 0x7f030344
int attr numericModifiers 0x7f030345
int attr offsetAlignmentMode 0x7f030346
int attr onCross 0x7f030347
int attr onHide 0x7f030348
int attr onNegativeCross 0x7f030349
int attr onPositiveCross 0x7f03034a
int attr onShow 0x7f03034b
int attr onStateTransition 0x7f03034c
int attr onTouchUp 0x7f03034d
int attr overlapAnchor 0x7f03034e
int attr overlay 0x7f03034f
int attr paddingBottomNoButtons 0x7f030350
int attr paddingBottomSystemWindowInsets 0x7f030351
int attr paddingEnd 0x7f030352
int attr paddingLeftSystemWindowInsets 0x7f030353
int attr paddingRightSystemWindowInsets 0x7f030354
int attr paddingStart 0x7f030355
int attr paddingTopNoTitle 0x7f030356
int attr paddingTopSystemWindowInsets 0x7f030357
int attr panelBackground 0x7f030358
int attr panelMenuListTheme 0x7f030359
int attr panelMenuListWidth 0x7f03035a
int attr passwordToggleContentDescription 0x7f03035b
int attr passwordToggleDrawable 0x7f03035c
int attr passwordToggleEnabled 0x7f03035d
int attr passwordToggleTint 0x7f03035e
int attr passwordToggleTintMode 0x7f03035f
int attr pathMotionArc 0x7f030360
int attr path_percent 0x7f030361
int attr percentHeight 0x7f030362
int attr percentWidth 0x7f030363
int attr percentX 0x7f030364
int attr percentY 0x7f030365
int attr perpendicularPath_percent 0x7f030366
int attr pivotAnchor 0x7f030367
int attr placeholderText 0x7f030368
int attr placeholderTextAppearance 0x7f030369
int attr placeholderTextColor 0x7f03036a
int attr placeholder_emptyVisibility 0x7f03036b
int attr polarRelativeTo 0x7f03036c
int attr popupMenuBackground 0x7f03036d
int attr popupMenuStyle 0x7f03036e
int attr popupTheme 0x7f03036f
int attr popupWindowStyle 0x7f030370
int attr prefixText 0x7f030371
int attr prefixTextAppearance 0x7f030372
int attr prefixTextColor 0x7f030373
int attr preserveIconSpacing 0x7f030374
int attr pressedTranslationZ 0x7f030375
int attr progressBarPadding 0x7f030376
int attr progressBarStyle 0x7f030377
int attr quantizeMotionInterpolator 0x7f030378
int attr quantizeMotionPhase 0x7f030379
int attr quantizeMotionSteps 0x7f03037a
int attr queryBackground 0x7f03037b
int attr queryHint 0x7f03037c
int attr queryPatterns 0x7f03037d
int attr radioButtonStyle 0x7f03037e
int attr rangeFillColor 0x7f03037f
int attr ratingBarStyle 0x7f030380
int attr ratingBarStyleIndicator 0x7f030381
int attr ratingBarStyleSmall 0x7f030382
int attr reactiveGuide_animateChange 0x7f030383
int attr reactiveGuide_applyToAllConstraintSets 0x7f030384
int attr reactiveGuide_applyToConstraintSet 0x7f030385
int attr reactiveGuide_valueId 0x7f030386
int attr recyclerViewStyle 0x7f030387
int attr region_heightLessThan 0x7f030388
int attr region_heightMoreThan 0x7f030389
int attr region_widthLessThan 0x7f03038a
int attr region_widthMoreThan 0x7f03038b
int attr removeEmbeddedFabElevation 0x7f03038c
int attr reverseLayout 0x7f03038d
int attr rippleColor 0x7f03038e
int attr rotationCenterId 0x7f03038f
int attr round 0x7f030390
int attr roundPercent 0x7f030391
int attr saturation 0x7f030392
int attr scaleFromTextSize 0x7f030393
int attr scrimAnimationDuration 0x7f030394
int attr scrimBackground 0x7f030395
int attr scrimVisibleHeightTrigger 0x7f030396
int attr searchHintIcon 0x7f030397
int attr searchIcon 0x7f030398
int attr searchPrefixText 0x7f030399
int attr searchViewStyle 0x7f03039a
int attr seekBarStyle 0x7f03039b
int attr selectableItemBackground 0x7f03039c
int attr selectableItemBackgroundBorderless 0x7f03039d
int attr selectionRequired 0x7f03039e
int attr selectorSize 0x7f03039f
int attr setsTag 0x7f0303a0
int attr shapeAppearance 0x7f0303a1
int attr shapeAppearanceCornerExtraLarge 0x7f0303a2
int attr shapeAppearanceCornerExtraSmall 0x7f0303a3
int attr shapeAppearanceCornerLarge 0x7f0303a4
int attr shapeAppearanceCornerMedium 0x7f0303a5
int attr shapeAppearanceCornerSmall 0x7f0303a6
int attr shapeAppearanceLargeComponent 0x7f0303a7
int attr shapeAppearanceMediumComponent 0x7f0303a8
int attr shapeAppearanceOverlay 0x7f0303a9
int attr shapeAppearanceSmallComponent 0x7f0303aa
int attr shapeCornerFamily 0x7f0303ab
int attr shortcutMatchRequired 0x7f0303ac
int attr shouldRemoveExpandedCorners 0x7f0303ad
int attr showAnimationBehavior 0x7f0303ae
int attr showAsAction 0x7f0303af
int attr showDelay 0x7f0303b0
int attr showDividers 0x7f0303b1
int attr showMotionSpec 0x7f0303b2
int attr showPaths 0x7f0303b3
int attr showText 0x7f0303b4
int attr showTitle 0x7f0303b5
int attr shrinkMotionSpec 0x7f0303b6
int attr sideSheetDialogTheme 0x7f0303b7
int attr sideSheetModalStyle 0x7f0303b8
int attr simpleItemLayout 0x7f0303b9
int attr simpleItemSelectedColor 0x7f0303ba
int attr simpleItemSelectedRippleColor 0x7f0303bb
int attr simpleItems 0x7f0303bc
int attr singleChoiceItemLayout 0x7f0303bd
int attr singleLine 0x7f0303be
int attr singleSelection 0x7f0303bf
int attr sizePercent 0x7f0303c0
int attr sliderStyle 0x7f0303c1
int attr snackbarButtonStyle 0x7f0303c2
int attr snackbarStyle 0x7f0303c3
int attr snackbarTextViewStyle 0x7f0303c4
int attr spanCount 0x7f0303c5
int attr spinBars 0x7f0303c6
int attr spinnerDropDownItemStyle 0x7f0303c7
int attr spinnerStyle 0x7f0303c8
int attr splitTrack 0x7f0303c9
int attr springBoundary 0x7f0303ca
int attr springDamping 0x7f0303cb
int attr springMass 0x7f0303cc
int attr springStiffness 0x7f0303cd
int attr springStopThreshold 0x7f0303ce
int attr srcCompat 0x7f0303cf
int attr stackFromEnd 0x7f0303d0
int attr staggered 0x7f0303d1
int attr startIconCheckable 0x7f0303d2
int attr startIconContentDescription 0x7f0303d3
int attr startIconDrawable 0x7f0303d4
int attr startIconMinSize 0x7f0303d5
int attr startIconScaleType 0x7f0303d6
int attr startIconTint 0x7f0303d7
int attr startIconTintMode 0x7f0303d8
int attr state_above_anchor 0x7f0303d9
int attr state_collapsed 0x7f0303da
int attr state_collapsible 0x7f0303db
int attr state_dragged 0x7f0303dc
int attr state_error 0x7f0303dd
int attr state_indeterminate 0x7f0303de
int attr state_liftable 0x7f0303df
int attr state_lifted 0x7f0303e0
int attr state_with_icon 0x7f0303e1
int attr statusBarBackground 0x7f0303e2
int attr statusBarForeground 0x7f0303e3
int attr statusBarScrim 0x7f0303e4
int attr strokeColor 0x7f0303e5
int attr strokeWidth 0x7f0303e6
int attr subMenuArrow 0x7f0303e7
int attr subheaderColor 0x7f0303e8
int attr subheaderInsetEnd 0x7f0303e9
int attr subheaderInsetStart 0x7f0303ea
int attr subheaderTextAppearance 0x7f0303eb
int attr submitBackground 0x7f0303ec
int attr subtitle 0x7f0303ed
int attr subtitleCentered 0x7f0303ee
int attr subtitleTextAppearance 0x7f0303ef
int attr subtitleTextColor 0x7f0303f0
int attr subtitleTextStyle 0x7f0303f1
int attr suffixText 0x7f0303f2
int attr suffixTextAppearance 0x7f0303f3
int attr suffixTextColor 0x7f0303f4
int attr suggestionRowLayout 0x7f0303f5
int attr switchMinWidth 0x7f0303f6
int attr switchPadding 0x7f0303f7
int attr switchStyle 0x7f0303f8
int attr switchTextAppearance 0x7f0303f9
int attr tabBackground 0x7f0303fa
int attr tabContentStart 0x7f0303fb
int attr tabGravity 0x7f0303fc
int attr tabIconTint 0x7f0303fd
int attr tabIconTintMode 0x7f0303fe
int attr tabIndicator 0x7f0303ff
int attr tabIndicatorAnimationDuration 0x7f030400
int attr tabIndicatorAnimationMode 0x7f030401
int attr tabIndicatorColor 0x7f030402
int attr tabIndicatorFullWidth 0x7f030403
int attr tabIndicatorGravity 0x7f030404
int attr tabIndicatorHeight 0x7f030405
int attr tabInlineLabel 0x7f030406
int attr tabMaxWidth 0x7f030407
int attr tabMinWidth 0x7f030408
int attr tabMode 0x7f030409
int attr tabPadding 0x7f03040a
int attr tabPaddingBottom 0x7f03040b
int attr tabPaddingEnd 0x7f03040c
int attr tabPaddingStart 0x7f03040d
int attr tabPaddingTop 0x7f03040e
int attr tabRippleColor 0x7f03040f
int attr tabSecondaryStyle 0x7f030410
int attr tabSelectedTextAppearance 0x7f030411
int attr tabSelectedTextColor 0x7f030412
int attr tabStyle 0x7f030413
int attr tabTextAppearance 0x7f030414
int attr tabTextColor 0x7f030415
int attr tabUnboundedRipple 0x7f030416
int attr targetId 0x7f030417
int attr telltales_tailColor 0x7f030418
int attr telltales_tailScale 0x7f030419
int attr telltales_velocityMode 0x7f03041a
int attr textAllCaps 0x7f03041b
int attr textAppearanceBody1 0x7f03041c
int attr textAppearanceBody2 0x7f03041d
int attr textAppearanceBodyLarge 0x7f03041e
int attr textAppearanceBodyMedium 0x7f03041f
int attr textAppearanceBodySmall 0x7f030420
int attr textAppearanceButton 0x7f030421
int attr textAppearanceCaption 0x7f030422
int attr textAppearanceDisplayLarge 0x7f030423
int attr textAppearanceDisplayMedium 0x7f030424
int attr textAppearanceDisplaySmall 0x7f030425
int attr textAppearanceHeadline1 0x7f030426
int attr textAppearanceHeadline2 0x7f030427
int attr textAppearanceHeadline3 0x7f030428
int attr textAppearanceHeadline4 0x7f030429
int attr textAppearanceHeadline5 0x7f03042a
int attr textAppearanceHeadline6 0x7f03042b
int attr textAppearanceHeadlineLarge 0x7f03042c
int attr textAppearanceHeadlineMedium 0x7f03042d
int attr textAppearanceHeadlineSmall 0x7f03042e
int attr textAppearanceLabelLarge 0x7f03042f
int attr textAppearanceLabelMedium 0x7f030430
int attr textAppearanceLabelSmall 0x7f030431
int attr textAppearanceLargePopupMenu 0x7f030432
int attr textAppearanceLineHeightEnabled 0x7f030433
int attr textAppearanceListItem 0x7f030434
int attr textAppearanceListItemSecondary 0x7f030435
int attr textAppearanceListItemSmall 0x7f030436
int attr textAppearanceOverline 0x7f030437
int attr textAppearancePopupMenuHeader 0x7f030438
int attr textAppearanceSearchResultSubtitle 0x7f030439
int attr textAppearanceSearchResultTitle 0x7f03043a
int attr textAppearanceSmallPopupMenu 0x7f03043b
int attr textAppearanceSubtitle1 0x7f03043c
int attr textAppearanceSubtitle2 0x7f03043d
int attr textAppearanceTitleLarge 0x7f03043e
int attr textAppearanceTitleMedium 0x7f03043f
int attr textAppearanceTitleSmall 0x7f030440
int attr textBackground 0x7f030441
int attr textBackgroundPanX 0x7f030442
int attr textBackgroundPanY 0x7f030443
int attr textBackgroundRotate 0x7f030444
int attr textBackgroundZoom 0x7f030445
int attr textColorAlertDialogListItem 0x7f030446
int attr textColorSearchUrl 0x7f030447
int attr textEndPadding 0x7f030448
int attr textFillColor 0x7f030449
int attr textInputFilledDenseStyle 0x7f03044a
int attr textInputFilledExposedDropdownMenuStyle 0x7f03044b
int attr textInputFilledStyle 0x7f03044c
int attr textInputLayoutFocusedRectEnabled 0x7f03044d
int attr textInputOutlinedDenseStyle 0x7f03044e
int attr textInputOutlinedExposedDropdownMenuStyle 0x7f03044f
int attr textInputOutlinedStyle 0x7f030450
int attr textInputStyle 0x7f030451
int attr textLocale 0x7f030452
int attr textOutlineColor 0x7f030453
int attr textOutlineThickness 0x7f030454
int attr textPanX 0x7f030455
int attr textPanY 0x7f030456
int attr textStartPadding 0x7f030457
int attr textureBlurFactor 0x7f030458
int attr textureEffect 0x7f030459
int attr textureHeight 0x7f03045a
int attr textureWidth 0x7f03045b
int attr theme 0x7f03045c
int attr thickness 0x7f03045d
int attr thumbColor 0x7f03045e
int attr thumbElevation 0x7f03045f
int attr thumbIcon 0x7f030460
int attr thumbIconTint 0x7f030461
int attr thumbIconTintMode 0x7f030462
int attr thumbRadius 0x7f030463
int attr thumbStrokeColor 0x7f030464
int attr thumbStrokeWidth 0x7f030465
int attr thumbTextPadding 0x7f030466
int attr thumbTint 0x7f030467
int attr thumbTintMode 0x7f030468
int attr tickColor 0x7f030469
int attr tickColorActive 0x7f03046a
int attr tickColorInactive 0x7f03046b
int attr tickMark 0x7f03046c
int attr tickMarkTint 0x7f03046d
int attr tickMarkTintMode 0x7f03046e
int attr tickRadiusActive 0x7f03046f
int attr tickRadiusInactive 0x7f030470
int attr tickVisible 0x7f030471
int attr tint 0x7f030472
int attr tintMode 0x7f030473
int attr tintNavigationIcon 0x7f030474
int attr title 0x7f030475
int attr titleCentered 0x7f030476
int attr titleCollapseMode 0x7f030477
int attr titleEnabled 0x7f030478
int attr titleMargin 0x7f030479
int attr titleMarginBottom 0x7f03047a
int attr titleMarginEnd 0x7f03047b
int attr titleMarginStart 0x7f03047c
int attr titleMarginTop 0x7f03047d
int attr titleMargins 0x7f03047e
int attr titlePositionInterpolator 0x7f03047f
int attr titleTextAppearance 0x7f030480
int attr titleTextColor 0x7f030481
int attr titleTextEllipsize 0x7f030482
int attr titleTextStyle 0x7f030483
int attr toggleCheckedStateOnClick 0x7f030484
int attr toolbarId 0x7f030485
int attr toolbarNavigationButtonStyle 0x7f030486
int attr toolbarStyle 0x7f030487
int attr toolbarSurfaceStyle 0x7f030488
int attr tooltipForegroundColor 0x7f030489
int attr tooltipFrameBackground 0x7f03048a
int attr tooltipStyle 0x7f03048b
int attr tooltipText 0x7f03048c
int attr topInsetScrimEnabled 0x7f03048d
int attr touchAnchorId 0x7f03048e
int attr touchAnchorSide 0x7f03048f
int attr touchRegionId 0x7f030490
int attr track 0x7f030491
int attr trackColor 0x7f030492
int attr trackColorActive 0x7f030493
int attr trackColorInactive 0x7f030494
int attr trackCornerRadius 0x7f030495
int attr trackDecoration 0x7f030496
int attr trackDecorationTint 0x7f030497
int attr trackDecorationTintMode 0x7f030498
int attr trackHeight 0x7f030499
int attr trackThickness 0x7f03049a
int attr trackTint 0x7f03049b
int attr trackTintMode 0x7f03049c
int attr transformPivotTarget 0x7f03049d
int attr transitionDisable 0x7f03049e
int attr transitionEasing 0x7f03049f
int attr transitionFlags 0x7f0304a0
int attr transitionPathRotate 0x7f0304a1
int attr transitionShapeAppearance 0x7f0304a2
int attr triggerId 0x7f0304a3
int attr triggerReceiver 0x7f0304a4
int attr triggerSlack 0x7f0304a5
int attr ttcIndex 0x7f0304a6
int attr upDuration 0x7f0304a7
int attr useCompatPadding 0x7f0304a8
int attr useDrawerArrowDrawable 0x7f0304a9
int attr useMaterialThemeColors 0x7f0304aa
int attr values 0x7f0304ab
int attr verticalOffset 0x7f0304ac
int attr verticalOffsetWithText 0x7f0304ad
int attr viewInflaterClass 0x7f0304ae
int attr viewTransitionMode 0x7f0304af
int attr viewTransitionOnCross 0x7f0304b0
int attr viewTransitionOnNegativeCross 0x7f0304b1
int attr viewTransitionOnPositiveCross 0x7f0304b2
int attr visibilityMode 0x7f0304b3
int attr voiceIcon 0x7f0304b4
int attr warmth 0x7f0304b5
int attr waveDecay 0x7f0304b6
int attr waveOffset 0x7f0304b7
int attr wavePeriod 0x7f0304b8
int attr wavePhase 0x7f0304b9
int attr waveShape 0x7f0304ba
int attr waveVariesBy 0x7f0304bb
int attr windowActionBar 0x7f0304bc
int attr windowActionBarOverlay 0x7f0304bd
int attr windowActionModeOverlay 0x7f0304be
int attr windowFixedHeightMajor 0x7f0304bf
int attr windowFixedHeightMinor 0x7f0304c0
int attr windowFixedWidthMajor 0x7f0304c1
int attr windowFixedWidthMinor 0x7f0304c2
int attr windowMinWidthMajor 0x7f0304c3
int attr windowMinWidthMinor 0x7f0304c4
int attr windowNoTitle 0x7f0304c5
int attr yearSelectedStyle 0x7f0304c6
int attr yearStyle 0x7f0304c7
int attr yearTodayStyle 0x7f0304c8
int bool abc_action_bar_embed_tabs 0x7f040000
int bool abc_config_actionMenuItemAllCaps 0x7f040001
int bool mtrl_btn_textappearance_all_caps 0x7f040002
int color abc_background_cache_hint_selector_material_dark 0x7f050000
int color abc_background_cache_hint_selector_material_light 0x7f050001
int color abc_btn_colored_borderless_text_material 0x7f050002
int color abc_btn_colored_text_material 0x7f050003
int color abc_color_highlight_material 0x7f050004
int color abc_decor_view_status_guard 0x7f050005
int color abc_decor_view_status_guard_light 0x7f050006
int color abc_hint_foreground_material_dark 0x7f050007
int color abc_hint_foreground_material_light 0x7f050008
int color abc_primary_text_disable_only_material_dark 0x7f050009
int color abc_primary_text_disable_only_material_light 0x7f05000a
int color abc_primary_text_material_dark 0x7f05000b
int color abc_primary_text_material_light 0x7f05000c
int color abc_search_url_text 0x7f05000d
int color abc_search_url_text_normal 0x7f05000e
int color abc_search_url_text_pressed 0x7f05000f
int color abc_search_url_text_selected 0x7f050010
int color abc_secondary_text_material_dark 0x7f050011
int color abc_secondary_text_material_light 0x7f050012
int color abc_tint_btn_checkable 0x7f050013
int color abc_tint_default 0x7f050014
int color abc_tint_edittext 0x7f050015
int color abc_tint_seek_thumb 0x7f050016
int color abc_tint_spinner 0x7f050017
int color abc_tint_switch_track 0x7f050018
int color accent_material_dark 0x7f050019
int color accent_material_light 0x7f05001a
int color accent_orange 0x7f05001b
int color androidx_core_ripple_material_light 0x7f05001c
int color androidx_core_secondary_text_default_material_light 0x7f05001d
int color background_floating_material_dark 0x7f05001e
int color background_floating_material_light 0x7f05001f
int color background_light 0x7f050020
int color background_material_dark 0x7f050021
int color background_material_light 0x7f050022
int color black 0x7f050023
int color bright_foreground_disabled_material_dark 0x7f050024
int color bright_foreground_disabled_material_light 0x7f050025
int color bright_foreground_inverse_material_dark 0x7f050026
int color bright_foreground_inverse_material_light 0x7f050027
int color bright_foreground_material_dark 0x7f050028
int color bright_foreground_material_light 0x7f050029
int color button_material_dark 0x7f05002a
int color button_material_light 0x7f05002b
int color cardview_dark_background 0x7f05002c
int color cardview_light_background 0x7f05002d
int color cardview_shadow_end_color 0x7f05002e
int color cardview_shadow_start_color 0x7f05002f
int color design_bottom_navigation_shadow_color 0x7f050030
int color design_box_stroke_color 0x7f050031
int color design_dark_default_color_background 0x7f050032
int color design_dark_default_color_error 0x7f050033
int color design_dark_default_color_on_background 0x7f050034
int color design_dark_default_color_on_error 0x7f050035
int color design_dark_default_color_on_primary 0x7f050036
int color design_dark_default_color_on_secondary 0x7f050037
int color design_dark_default_color_on_surface 0x7f050038
int color design_dark_default_color_primary 0x7f050039
int color design_dark_default_color_primary_dark 0x7f05003a
int color design_dark_default_color_primary_variant 0x7f05003b
int color design_dark_default_color_secondary 0x7f05003c
int color design_dark_default_color_secondary_variant 0x7f05003d
int color design_dark_default_color_surface 0x7f05003e
int color design_default_color_background 0x7f05003f
int color design_default_color_error 0x7f050040
int color design_default_color_on_background 0x7f050041
int color design_default_color_on_error 0x7f050042
int color design_default_color_on_primary 0x7f050043
int color design_default_color_on_secondary 0x7f050044
int color design_default_color_on_surface 0x7f050045
int color design_default_color_primary 0x7f050046
int color design_default_color_primary_dark 0x7f050047
int color design_default_color_primary_variant 0x7f050048
int color design_default_color_secondary 0x7f050049
int color design_default_color_secondary_variant 0x7f05004a
int color design_default_color_surface 0x7f05004b
int color design_error 0x7f05004c
int color design_fab_shadow_end_color 0x7f05004d
int color design_fab_shadow_mid_color 0x7f05004e
int color design_fab_shadow_start_color 0x7f05004f
int color design_fab_stroke_end_inner_color 0x7f050050
int color design_fab_stroke_end_outer_color 0x7f050051
int color design_fab_stroke_top_inner_color 0x7f050052
int color design_fab_stroke_top_outer_color 0x7f050053
int color design_icon_tint 0x7f050054
int color design_snackbar_background_color 0x7f050055
int color dim_foreground_disabled_material_dark 0x7f050056
int color dim_foreground_disabled_material_light 0x7f050057
int color dim_foreground_material_dark 0x7f050058
int color dim_foreground_material_light 0x7f050059
int color error_color_material_dark 0x7f05005a
int color error_color_material_light 0x7f05005b
int color firewood_color 0x7f05005c
int color food_color 0x7f05005d
int color foreground_material_dark 0x7f05005e
int color foreground_material_light 0x7f05005f
int color highlighted_text_material_dark 0x7f050060
int color highlighted_text_material_light 0x7f050061
int color ic_launcher_background 0x7f050062
int color m3_appbar_overlay_color 0x7f050063
int color m3_assist_chip_icon_tint_color 0x7f050064
int color m3_assist_chip_stroke_color 0x7f050065
int color m3_button_background_color_selector 0x7f050066
int color m3_button_foreground_color_selector 0x7f050067
int color m3_button_outline_color_selector 0x7f050068
int color m3_button_ripple_color 0x7f050069
int color m3_button_ripple_color_selector 0x7f05006a
int color m3_calendar_item_disabled_text 0x7f05006b
int color m3_calendar_item_stroke_color 0x7f05006c
int color m3_card_foreground_color 0x7f05006d
int color m3_card_ripple_color 0x7f05006e
int color m3_card_stroke_color 0x7f05006f
int color m3_checkbox_button_icon_tint 0x7f050070
int color m3_checkbox_button_tint 0x7f050071
int color m3_chip_assist_text_color 0x7f050072
int color m3_chip_background_color 0x7f050073
int color m3_chip_ripple_color 0x7f050074
int color m3_chip_stroke_color 0x7f050075
int color m3_chip_text_color 0x7f050076
int color m3_dark_default_color_primary_text 0x7f050077
int color m3_dark_default_color_secondary_text 0x7f050078
int color m3_dark_highlighted_text 0x7f050079
int color m3_dark_hint_foreground 0x7f05007a
int color m3_dark_primary_text_disable_only 0x7f05007b
int color m3_default_color_primary_text 0x7f05007c
int color m3_default_color_secondary_text 0x7f05007d
int color m3_dynamic_dark_default_color_primary_text 0x7f05007e
int color m3_dynamic_dark_default_color_secondary_text 0x7f05007f
int color m3_dynamic_dark_highlighted_text 0x7f050080
int color m3_dynamic_dark_hint_foreground 0x7f050081
int color m3_dynamic_dark_primary_text_disable_only 0x7f050082
int color m3_dynamic_default_color_primary_text 0x7f050083
int color m3_dynamic_default_color_secondary_text 0x7f050084
int color m3_dynamic_highlighted_text 0x7f050085
int color m3_dynamic_hint_foreground 0x7f050086
int color m3_dynamic_primary_text_disable_only 0x7f050087
int color m3_efab_ripple_color_selector 0x7f050088
int color m3_elevated_chip_background_color 0x7f050089
int color m3_fab_efab_background_color_selector 0x7f05008a
int color m3_fab_efab_foreground_color_selector 0x7f05008b
int color m3_fab_ripple_color_selector 0x7f05008c
int color m3_filled_icon_button_container_color_selector 0x7f05008d
int color m3_highlighted_text 0x7f05008e
int color m3_hint_foreground 0x7f05008f
int color m3_icon_button_icon_color_selector 0x7f050090
int color m3_navigation_bar_item_with_indicator_icon_tint 0x7f050091
int color m3_navigation_bar_item_with_indicator_label_tint 0x7f050092
int color m3_navigation_bar_ripple_color_selector 0x7f050093
int color m3_navigation_item_background_color 0x7f050094
int color m3_navigation_item_icon_tint 0x7f050095
int color m3_navigation_item_ripple_color 0x7f050096
int color m3_navigation_item_text_color 0x7f050097
int color m3_navigation_rail_item_with_indicator_icon_tint 0x7f050098
int color m3_navigation_rail_item_with_indicator_label_tint 0x7f050099
int color m3_navigation_rail_ripple_color_selector 0x7f05009a
int color m3_popupmenu_overlay_color 0x7f05009b
int color m3_primary_text_disable_only 0x7f05009c
int color m3_radiobutton_button_tint 0x7f05009d
int color m3_radiobutton_ripple_tint 0x7f05009e
int color m3_ref_palette_black 0x7f05009f
int color m3_ref_palette_dynamic_neutral0 0x7f0500a0
int color m3_ref_palette_dynamic_neutral10 0x7f0500a1
int color m3_ref_palette_dynamic_neutral100 0x7f0500a2
int color m3_ref_palette_dynamic_neutral12 0x7f0500a3
int color m3_ref_palette_dynamic_neutral17 0x7f0500a4
int color m3_ref_palette_dynamic_neutral20 0x7f0500a5
int color m3_ref_palette_dynamic_neutral22 0x7f0500a6
int color m3_ref_palette_dynamic_neutral24 0x7f0500a7
int color m3_ref_palette_dynamic_neutral30 0x7f0500a8
int color m3_ref_palette_dynamic_neutral4 0x7f0500a9
int color m3_ref_palette_dynamic_neutral40 0x7f0500aa
int color m3_ref_palette_dynamic_neutral50 0x7f0500ab
int color m3_ref_palette_dynamic_neutral6 0x7f0500ac
int color m3_ref_palette_dynamic_neutral60 0x7f0500ad
int color m3_ref_palette_dynamic_neutral70 0x7f0500ae
int color m3_ref_palette_dynamic_neutral80 0x7f0500af
int color m3_ref_palette_dynamic_neutral87 0x7f0500b0
int color m3_ref_palette_dynamic_neutral90 0x7f0500b1
int color m3_ref_palette_dynamic_neutral92 0x7f0500b2
int color m3_ref_palette_dynamic_neutral94 0x7f0500b3
int color m3_ref_palette_dynamic_neutral95 0x7f0500b4
int color m3_ref_palette_dynamic_neutral96 0x7f0500b5
int color m3_ref_palette_dynamic_neutral98 0x7f0500b6
int color m3_ref_palette_dynamic_neutral99 0x7f0500b7
int color m3_ref_palette_dynamic_neutral_variant0 0x7f0500b8
int color m3_ref_palette_dynamic_neutral_variant10 0x7f0500b9
int color m3_ref_palette_dynamic_neutral_variant100 0x7f0500ba
int color m3_ref_palette_dynamic_neutral_variant20 0x7f0500bb
int color m3_ref_palette_dynamic_neutral_variant30 0x7f0500bc
int color m3_ref_palette_dynamic_neutral_variant40 0x7f0500bd
int color m3_ref_palette_dynamic_neutral_variant50 0x7f0500be
int color m3_ref_palette_dynamic_neutral_variant60 0x7f0500bf
int color m3_ref_palette_dynamic_neutral_variant70 0x7f0500c0
int color m3_ref_palette_dynamic_neutral_variant80 0x7f0500c1
int color m3_ref_palette_dynamic_neutral_variant90 0x7f0500c2
int color m3_ref_palette_dynamic_neutral_variant95 0x7f0500c3
int color m3_ref_palette_dynamic_neutral_variant99 0x7f0500c4
int color m3_ref_palette_dynamic_primary0 0x7f0500c5
int color m3_ref_palette_dynamic_primary10 0x7f0500c6
int color m3_ref_palette_dynamic_primary100 0x7f0500c7
int color m3_ref_palette_dynamic_primary20 0x7f0500c8
int color m3_ref_palette_dynamic_primary30 0x7f0500c9
int color m3_ref_palette_dynamic_primary40 0x7f0500ca
int color m3_ref_palette_dynamic_primary50 0x7f0500cb
int color m3_ref_palette_dynamic_primary60 0x7f0500cc
int color m3_ref_palette_dynamic_primary70 0x7f0500cd
int color m3_ref_palette_dynamic_primary80 0x7f0500ce
int color m3_ref_palette_dynamic_primary90 0x7f0500cf
int color m3_ref_palette_dynamic_primary95 0x7f0500d0
int color m3_ref_palette_dynamic_primary99 0x7f0500d1
int color m3_ref_palette_dynamic_secondary0 0x7f0500d2
int color m3_ref_palette_dynamic_secondary10 0x7f0500d3
int color m3_ref_palette_dynamic_secondary100 0x7f0500d4
int color m3_ref_palette_dynamic_secondary20 0x7f0500d5
int color m3_ref_palette_dynamic_secondary30 0x7f0500d6
int color m3_ref_palette_dynamic_secondary40 0x7f0500d7
int color m3_ref_palette_dynamic_secondary50 0x7f0500d8
int color m3_ref_palette_dynamic_secondary60 0x7f0500d9
int color m3_ref_palette_dynamic_secondary70 0x7f0500da
int color m3_ref_palette_dynamic_secondary80 0x7f0500db
int color m3_ref_palette_dynamic_secondary90 0x7f0500dc
int color m3_ref_palette_dynamic_secondary95 0x7f0500dd
int color m3_ref_palette_dynamic_secondary99 0x7f0500de
int color m3_ref_palette_dynamic_tertiary0 0x7f0500df
int color m3_ref_palette_dynamic_tertiary10 0x7f0500e0
int color m3_ref_palette_dynamic_tertiary100 0x7f0500e1
int color m3_ref_palette_dynamic_tertiary20 0x7f0500e2
int color m3_ref_palette_dynamic_tertiary30 0x7f0500e3
int color m3_ref_palette_dynamic_tertiary40 0x7f0500e4
int color m3_ref_palette_dynamic_tertiary50 0x7f0500e5
int color m3_ref_palette_dynamic_tertiary60 0x7f0500e6
int color m3_ref_palette_dynamic_tertiary70 0x7f0500e7
int color m3_ref_palette_dynamic_tertiary80 0x7f0500e8
int color m3_ref_palette_dynamic_tertiary90 0x7f0500e9
int color m3_ref_palette_dynamic_tertiary95 0x7f0500ea
int color m3_ref_palette_dynamic_tertiary99 0x7f0500eb
int color m3_ref_palette_error0 0x7f0500ec
int color m3_ref_palette_error10 0x7f0500ed
int color m3_ref_palette_error100 0x7f0500ee
int color m3_ref_palette_error20 0x7f0500ef
int color m3_ref_palette_error30 0x7f0500f0
int color m3_ref_palette_error40 0x7f0500f1
int color m3_ref_palette_error50 0x7f0500f2
int color m3_ref_palette_error60 0x7f0500f3
int color m3_ref_palette_error70 0x7f0500f4
int color m3_ref_palette_error80 0x7f0500f5
int color m3_ref_palette_error90 0x7f0500f6
int color m3_ref_palette_error95 0x7f0500f7
int color m3_ref_palette_error99 0x7f0500f8
int color m3_ref_palette_neutral0 0x7f0500f9
int color m3_ref_palette_neutral10 0x7f0500fa
int color m3_ref_palette_neutral100 0x7f0500fb
int color m3_ref_palette_neutral12 0x7f0500fc
int color m3_ref_palette_neutral17 0x7f0500fd
int color m3_ref_palette_neutral20 0x7f0500fe
int color m3_ref_palette_neutral22 0x7f0500ff
int color m3_ref_palette_neutral24 0x7f050100
int color m3_ref_palette_neutral30 0x7f050101
int color m3_ref_palette_neutral4 0x7f050102
int color m3_ref_palette_neutral40 0x7f050103
int color m3_ref_palette_neutral50 0x7f050104
int color m3_ref_palette_neutral6 0x7f050105
int color m3_ref_palette_neutral60 0x7f050106
int color m3_ref_palette_neutral70 0x7f050107
int color m3_ref_palette_neutral80 0x7f050108
int color m3_ref_palette_neutral87 0x7f050109
int color m3_ref_palette_neutral90 0x7f05010a
int color m3_ref_palette_neutral92 0x7f05010b
int color m3_ref_palette_neutral94 0x7f05010c
int color m3_ref_palette_neutral95 0x7f05010d
int color m3_ref_palette_neutral96 0x7f05010e
int color m3_ref_palette_neutral98 0x7f05010f
int color m3_ref_palette_neutral99 0x7f050110
int color m3_ref_palette_neutral_variant0 0x7f050111
int color m3_ref_palette_neutral_variant10 0x7f050112
int color m3_ref_palette_neutral_variant100 0x7f050113
int color m3_ref_palette_neutral_variant20 0x7f050114
int color m3_ref_palette_neutral_variant30 0x7f050115
int color m3_ref_palette_neutral_variant40 0x7f050116
int color m3_ref_palette_neutral_variant50 0x7f050117
int color m3_ref_palette_neutral_variant60 0x7f050118
int color m3_ref_palette_neutral_variant70 0x7f050119
int color m3_ref_palette_neutral_variant80 0x7f05011a
int color m3_ref_palette_neutral_variant90 0x7f05011b
int color m3_ref_palette_neutral_variant95 0x7f05011c
int color m3_ref_palette_neutral_variant99 0x7f05011d
int color m3_ref_palette_primary0 0x7f05011e
int color m3_ref_palette_primary10 0x7f05011f
int color m3_ref_palette_primary100 0x7f050120
int color m3_ref_palette_primary20 0x7f050121
int color m3_ref_palette_primary30 0x7f050122
int color m3_ref_palette_primary40 0x7f050123
int color m3_ref_palette_primary50 0x7f050124
int color m3_ref_palette_primary60 0x7f050125
int color m3_ref_palette_primary70 0x7f050126
int color m3_ref_palette_primary80 0x7f050127
int color m3_ref_palette_primary90 0x7f050128
int color m3_ref_palette_primary95 0x7f050129
int color m3_ref_palette_primary99 0x7f05012a
int color m3_ref_palette_secondary0 0x7f05012b
int color m3_ref_palette_secondary10 0x7f05012c
int color m3_ref_palette_secondary100 0x7f05012d
int color m3_ref_palette_secondary20 0x7f05012e
int color m3_ref_palette_secondary30 0x7f05012f
int color m3_ref_palette_secondary40 0x7f050130
int color m3_ref_palette_secondary50 0x7f050131
int color m3_ref_palette_secondary60 0x7f050132
int color m3_ref_palette_secondary70 0x7f050133
int color m3_ref_palette_secondary80 0x7f050134
int color m3_ref_palette_secondary90 0x7f050135
int color m3_ref_palette_secondary95 0x7f050136
int color m3_ref_palette_secondary99 0x7f050137
int color m3_ref_palette_tertiary0 0x7f050138
int color m3_ref_palette_tertiary10 0x7f050139
int color m3_ref_palette_tertiary100 0x7f05013a
int color m3_ref_palette_tertiary20 0x7f05013b
int color m3_ref_palette_tertiary30 0x7f05013c
int color m3_ref_palette_tertiary40 0x7f05013d
int color m3_ref_palette_tertiary50 0x7f05013e
int color m3_ref_palette_tertiary60 0x7f05013f
int color m3_ref_palette_tertiary70 0x7f050140
int color m3_ref_palette_tertiary80 0x7f050141
int color m3_ref_palette_tertiary90 0x7f050142
int color m3_ref_palette_tertiary95 0x7f050143
int color m3_ref_palette_tertiary99 0x7f050144
int color m3_ref_palette_white 0x7f050145
int color m3_selection_control_ripple_color_selector 0x7f050146
int color m3_simple_item_ripple_color 0x7f050147
int color m3_slider_active_track_color 0x7f050148
int color m3_slider_halo_color 0x7f050149
int color m3_slider_inactive_track_color 0x7f05014a
int color m3_slider_thumb_color 0x7f05014b
int color m3_switch_thumb_tint 0x7f05014c
int color m3_switch_track_tint 0x7f05014d
int color m3_sys_color_dark_background 0x7f05014e
int color m3_sys_color_dark_error 0x7f05014f
int color m3_sys_color_dark_error_container 0x7f050150
int color m3_sys_color_dark_inverse_on_surface 0x7f050151
int color m3_sys_color_dark_inverse_primary 0x7f050152
int color m3_sys_color_dark_inverse_surface 0x7f050153
int color m3_sys_color_dark_on_background 0x7f050154
int color m3_sys_color_dark_on_error 0x7f050155
int color m3_sys_color_dark_on_error_container 0x7f050156
int color m3_sys_color_dark_on_primary 0x7f050157
int color m3_sys_color_dark_on_primary_container 0x7f050158
int color m3_sys_color_dark_on_secondary 0x7f050159
int color m3_sys_color_dark_on_secondary_container 0x7f05015a
int color m3_sys_color_dark_on_surface 0x7f05015b
int color m3_sys_color_dark_on_surface_variant 0x7f05015c
int color m3_sys_color_dark_on_tertiary 0x7f05015d
int color m3_sys_color_dark_on_tertiary_container 0x7f05015e
int color m3_sys_color_dark_outline 0x7f05015f
int color m3_sys_color_dark_outline_variant 0x7f050160
int color m3_sys_color_dark_primary 0x7f050161
int color m3_sys_color_dark_primary_container 0x7f050162
int color m3_sys_color_dark_secondary 0x7f050163
int color m3_sys_color_dark_secondary_container 0x7f050164
int color m3_sys_color_dark_surface 0x7f050165
int color m3_sys_color_dark_surface_bright 0x7f050166
int color m3_sys_color_dark_surface_container 0x7f050167
int color m3_sys_color_dark_surface_container_high 0x7f050168
int color m3_sys_color_dark_surface_container_highest 0x7f050169
int color m3_sys_color_dark_surface_container_low 0x7f05016a
int color m3_sys_color_dark_surface_container_lowest 0x7f05016b
int color m3_sys_color_dark_surface_dim 0x7f05016c
int color m3_sys_color_dark_surface_variant 0x7f05016d
int color m3_sys_color_dark_tertiary 0x7f05016e
int color m3_sys_color_dark_tertiary_container 0x7f05016f
int color m3_sys_color_dynamic_dark_background 0x7f050170
int color m3_sys_color_dynamic_dark_inverse_on_surface 0x7f050171
int color m3_sys_color_dynamic_dark_inverse_primary 0x7f050172
int color m3_sys_color_dynamic_dark_inverse_surface 0x7f050173
int color m3_sys_color_dynamic_dark_on_background 0x7f050174
int color m3_sys_color_dynamic_dark_on_primary 0x7f050175
int color m3_sys_color_dynamic_dark_on_primary_container 0x7f050176
int color m3_sys_color_dynamic_dark_on_secondary 0x7f050177
int color m3_sys_color_dynamic_dark_on_secondary_container 0x7f050178
int color m3_sys_color_dynamic_dark_on_surface 0x7f050179
int color m3_sys_color_dynamic_dark_on_surface_variant 0x7f05017a
int color m3_sys_color_dynamic_dark_on_tertiary 0x7f05017b
int color m3_sys_color_dynamic_dark_on_tertiary_container 0x7f05017c
int color m3_sys_color_dynamic_dark_outline 0x7f05017d
int color m3_sys_color_dynamic_dark_outline_variant 0x7f05017e
int color m3_sys_color_dynamic_dark_primary 0x7f05017f
int color m3_sys_color_dynamic_dark_primary_container 0x7f050180
int color m3_sys_color_dynamic_dark_secondary 0x7f050181
int color m3_sys_color_dynamic_dark_secondary_container 0x7f050182
int color m3_sys_color_dynamic_dark_surface 0x7f050183
int color m3_sys_color_dynamic_dark_surface_bright 0x7f050184
int color m3_sys_color_dynamic_dark_surface_container 0x7f050185
int color m3_sys_color_dynamic_dark_surface_container_high 0x7f050186
int color m3_sys_color_dynamic_dark_surface_container_highest 0x7f050187
int color m3_sys_color_dynamic_dark_surface_container_low 0x7f050188
int color m3_sys_color_dynamic_dark_surface_container_lowest 0x7f050189
int color m3_sys_color_dynamic_dark_surface_dim 0x7f05018a
int color m3_sys_color_dynamic_dark_surface_variant 0x7f05018b
int color m3_sys_color_dynamic_dark_tertiary 0x7f05018c
int color m3_sys_color_dynamic_dark_tertiary_container 0x7f05018d
int color m3_sys_color_dynamic_light_background 0x7f05018e
int color m3_sys_color_dynamic_light_inverse_on_surface 0x7f05018f
int color m3_sys_color_dynamic_light_inverse_primary 0x7f050190
int color m3_sys_color_dynamic_light_inverse_surface 0x7f050191
int color m3_sys_color_dynamic_light_on_background 0x7f050192
int color m3_sys_color_dynamic_light_on_primary 0x7f050193
int color m3_sys_color_dynamic_light_on_primary_container 0x7f050194
int color m3_sys_color_dynamic_light_on_secondary 0x7f050195
int color m3_sys_color_dynamic_light_on_secondary_container 0x7f050196
int color m3_sys_color_dynamic_light_on_surface 0x7f050197
int color m3_sys_color_dynamic_light_on_surface_variant 0x7f050198
int color m3_sys_color_dynamic_light_on_tertiary 0x7f050199
int color m3_sys_color_dynamic_light_on_tertiary_container 0x7f05019a
int color m3_sys_color_dynamic_light_outline 0x7f05019b
int color m3_sys_color_dynamic_light_outline_variant 0x7f05019c
int color m3_sys_color_dynamic_light_primary 0x7f05019d
int color m3_sys_color_dynamic_light_primary_container 0x7f05019e
int color m3_sys_color_dynamic_light_secondary 0x7f05019f
int color m3_sys_color_dynamic_light_secondary_container 0x7f0501a0
int color m3_sys_color_dynamic_light_surface 0x7f0501a1
int color m3_sys_color_dynamic_light_surface_bright 0x7f0501a2
int color m3_sys_color_dynamic_light_surface_container 0x7f0501a3
int color m3_sys_color_dynamic_light_surface_container_high 0x7f0501a4
int color m3_sys_color_dynamic_light_surface_container_highest 0x7f0501a5
int color m3_sys_color_dynamic_light_surface_container_low 0x7f0501a6
int color m3_sys_color_dynamic_light_surface_container_lowest 0x7f0501a7
int color m3_sys_color_dynamic_light_surface_dim 0x7f0501a8
int color m3_sys_color_dynamic_light_surface_variant 0x7f0501a9
int color m3_sys_color_dynamic_light_tertiary 0x7f0501aa
int color m3_sys_color_dynamic_light_tertiary_container 0x7f0501ab
int color m3_sys_color_dynamic_on_primary_fixed 0x7f0501ac
int color m3_sys_color_dynamic_on_primary_fixed_variant 0x7f0501ad
int color m3_sys_color_dynamic_on_secondary_fixed 0x7f0501ae
int color m3_sys_color_dynamic_on_secondary_fixed_variant 0x7f0501af
int color m3_sys_color_dynamic_on_tertiary_fixed 0x7f0501b0
int color m3_sys_color_dynamic_on_tertiary_fixed_variant 0x7f0501b1
int color m3_sys_color_dynamic_primary_fixed 0x7f0501b2
int color m3_sys_color_dynamic_primary_fixed_dim 0x7f0501b3
int color m3_sys_color_dynamic_secondary_fixed 0x7f0501b4
int color m3_sys_color_dynamic_secondary_fixed_dim 0x7f0501b5
int color m3_sys_color_dynamic_tertiary_fixed 0x7f0501b6
int color m3_sys_color_dynamic_tertiary_fixed_dim 0x7f0501b7
int color m3_sys_color_light_background 0x7f0501b8
int color m3_sys_color_light_error 0x7f0501b9
int color m3_sys_color_light_error_container 0x7f0501ba
int color m3_sys_color_light_inverse_on_surface 0x7f0501bb
int color m3_sys_color_light_inverse_primary 0x7f0501bc
int color m3_sys_color_light_inverse_surface 0x7f0501bd
int color m3_sys_color_light_on_background 0x7f0501be
int color m3_sys_color_light_on_error 0x7f0501bf
int color m3_sys_color_light_on_error_container 0x7f0501c0
int color m3_sys_color_light_on_primary 0x7f0501c1
int color m3_sys_color_light_on_primary_container 0x7f0501c2
int color m3_sys_color_light_on_secondary 0x7f0501c3
int color m3_sys_color_light_on_secondary_container 0x7f0501c4
int color m3_sys_color_light_on_surface 0x7f0501c5
int color m3_sys_color_light_on_surface_variant 0x7f0501c6
int color m3_sys_color_light_on_tertiary 0x7f0501c7
int color m3_sys_color_light_on_tertiary_container 0x7f0501c8
int color m3_sys_color_light_outline 0x7f0501c9
int color m3_sys_color_light_outline_variant 0x7f0501ca
int color m3_sys_color_light_primary 0x7f0501cb
int color m3_sys_color_light_primary_container 0x7f0501cc
int color m3_sys_color_light_secondary 0x7f0501cd
int color m3_sys_color_light_secondary_container 0x7f0501ce
int color m3_sys_color_light_surface 0x7f0501cf
int color m3_sys_color_light_surface_bright 0x7f0501d0
int color m3_sys_color_light_surface_container 0x7f0501d1
int color m3_sys_color_light_surface_container_high 0x7f0501d2
int color m3_sys_color_light_surface_container_highest 0x7f0501d3
int color m3_sys_color_light_surface_container_low 0x7f0501d4
int color m3_sys_color_light_surface_container_lowest 0x7f0501d5
int color m3_sys_color_light_surface_dim 0x7f0501d6
int color m3_sys_color_light_surface_variant 0x7f0501d7
int color m3_sys_color_light_tertiary 0x7f0501d8
int color m3_sys_color_light_tertiary_container 0x7f0501d9
int color m3_sys_color_on_primary_fixed 0x7f0501da
int color m3_sys_color_on_primary_fixed_variant 0x7f0501db
int color m3_sys_color_on_secondary_fixed 0x7f0501dc
int color m3_sys_color_on_secondary_fixed_variant 0x7f0501dd
int color m3_sys_color_on_tertiary_fixed 0x7f0501de
int color m3_sys_color_on_tertiary_fixed_variant 0x7f0501df
int color m3_sys_color_primary_fixed 0x7f0501e0
int color m3_sys_color_primary_fixed_dim 0x7f0501e1
int color m3_sys_color_secondary_fixed 0x7f0501e2
int color m3_sys_color_secondary_fixed_dim 0x7f0501e3
int color m3_sys_color_tertiary_fixed 0x7f0501e4
int color m3_sys_color_tertiary_fixed_dim 0x7f0501e5
int color m3_tabs_icon_color 0x7f0501e6
int color m3_tabs_icon_color_secondary 0x7f0501e7
int color m3_tabs_ripple_color 0x7f0501e8
int color m3_tabs_ripple_color_secondary 0x7f0501e9
int color m3_tabs_text_color 0x7f0501ea
int color m3_tabs_text_color_secondary 0x7f0501eb
int color m3_text_button_background_color_selector 0x7f0501ec
int color m3_text_button_foreground_color_selector 0x7f0501ed
int color m3_text_button_ripple_color_selector 0x7f0501ee
int color m3_textfield_filled_background_color 0x7f0501ef
int color m3_textfield_indicator_text_color 0x7f0501f0
int color m3_textfield_input_text_color 0x7f0501f1
int color m3_textfield_label_color 0x7f0501f2
int color m3_textfield_stroke_color 0x7f0501f3
int color m3_timepicker_button_background_color 0x7f0501f4
int color m3_timepicker_button_ripple_color 0x7f0501f5
int color m3_timepicker_button_text_color 0x7f0501f6
int color m3_timepicker_clock_text_color 0x7f0501f7
int color m3_timepicker_display_background_color 0x7f0501f8
int color m3_timepicker_display_ripple_color 0x7f0501f9
int color m3_timepicker_display_text_color 0x7f0501fa
int color m3_timepicker_secondary_text_button_ripple_color 0x7f0501fb
int color m3_timepicker_secondary_text_button_text_color 0x7f0501fc
int color m3_timepicker_time_input_stroke_color 0x7f0501fd
int color m3_tonal_button_ripple_color_selector 0x7f0501fe
int color material_blue_grey_800 0x7f0501ff
int color material_blue_grey_900 0x7f050200
int color material_blue_grey_950 0x7f050201
int color material_cursor_color 0x7f050202
int color material_deep_teal_200 0x7f050203
int color material_deep_teal_500 0x7f050204
int color material_divider_color 0x7f050205
int color material_dynamic_neutral0 0x7f050206
int color material_dynamic_neutral10 0x7f050207
int color material_dynamic_neutral100 0x7f050208
int color material_dynamic_neutral20 0x7f050209
int color material_dynamic_neutral30 0x7f05020a
int color material_dynamic_neutral40 0x7f05020b
int color material_dynamic_neutral50 0x7f05020c
int color material_dynamic_neutral60 0x7f05020d
int color material_dynamic_neutral70 0x7f05020e
int color material_dynamic_neutral80 0x7f05020f
int color material_dynamic_neutral90 0x7f050210
int color material_dynamic_neutral95 0x7f050211
int color material_dynamic_neutral99 0x7f050212
int color material_dynamic_neutral_variant0 0x7f050213
int color material_dynamic_neutral_variant10 0x7f050214
int color material_dynamic_neutral_variant100 0x7f050215
int color material_dynamic_neutral_variant20 0x7f050216
int color material_dynamic_neutral_variant30 0x7f050217
int color material_dynamic_neutral_variant40 0x7f050218
int color material_dynamic_neutral_variant50 0x7f050219
int color material_dynamic_neutral_variant60 0x7f05021a
int color material_dynamic_neutral_variant70 0x7f05021b
int color material_dynamic_neutral_variant80 0x7f05021c
int color material_dynamic_neutral_variant90 0x7f05021d
int color material_dynamic_neutral_variant95 0x7f05021e
int color material_dynamic_neutral_variant99 0x7f05021f
int color material_dynamic_primary0 0x7f050220
int color material_dynamic_primary10 0x7f050221
int color material_dynamic_primary100 0x7f050222
int color material_dynamic_primary20 0x7f050223
int color material_dynamic_primary30 0x7f050224
int color material_dynamic_primary40 0x7f050225
int color material_dynamic_primary50 0x7f050226
int color material_dynamic_primary60 0x7f050227
int color material_dynamic_primary70 0x7f050228
int color material_dynamic_primary80 0x7f050229
int color material_dynamic_primary90 0x7f05022a
int color material_dynamic_primary95 0x7f05022b
int color material_dynamic_primary99 0x7f05022c
int color material_dynamic_secondary0 0x7f05022d
int color material_dynamic_secondary10 0x7f05022e
int color material_dynamic_secondary100 0x7f05022f
int color material_dynamic_secondary20 0x7f050230
int color material_dynamic_secondary30 0x7f050231
int color material_dynamic_secondary40 0x7f050232
int color material_dynamic_secondary50 0x7f050233
int color material_dynamic_secondary60 0x7f050234
int color material_dynamic_secondary70 0x7f050235
int color material_dynamic_secondary80 0x7f050236
int color material_dynamic_secondary90 0x7f050237
int color material_dynamic_secondary95 0x7f050238
int color material_dynamic_secondary99 0x7f050239
int color material_dynamic_tertiary0 0x7f05023a
int color material_dynamic_tertiary10 0x7f05023b
int color material_dynamic_tertiary100 0x7f05023c
int color material_dynamic_tertiary20 0x7f05023d
int color material_dynamic_tertiary30 0x7f05023e
int color material_dynamic_tertiary40 0x7f05023f
int color material_dynamic_tertiary50 0x7f050240
int color material_dynamic_tertiary60 0x7f050241
int color material_dynamic_tertiary70 0x7f050242
int color material_dynamic_tertiary80 0x7f050243
int color material_dynamic_tertiary90 0x7f050244
int color material_dynamic_tertiary95 0x7f050245
int color material_dynamic_tertiary99 0x7f050246
int color material_grey_100 0x7f050247
int color material_grey_300 0x7f050248
int color material_grey_50 0x7f050249
int color material_grey_600 0x7f05024a
int color material_grey_800 0x7f05024b
int color material_grey_850 0x7f05024c
int color material_grey_900 0x7f05024d
int color material_harmonized_color_error 0x7f05024e
int color material_harmonized_color_error_container 0x7f05024f
int color material_harmonized_color_on_error 0x7f050250
int color material_harmonized_color_on_error_container 0x7f050251
int color material_on_background_disabled 0x7f050252
int color material_on_background_emphasis_high_type 0x7f050253
int color material_on_background_emphasis_medium 0x7f050254
int color material_on_primary_disabled 0x7f050255
int color material_on_primary_emphasis_high_type 0x7f050256
int color material_on_primary_emphasis_medium 0x7f050257
int color material_on_surface_disabled 0x7f050258
int color material_on_surface_emphasis_high_type 0x7f050259
int color material_on_surface_emphasis_medium 0x7f05025a
int color material_on_surface_stroke 0x7f05025b
int color material_personalized__highlighted_text 0x7f05025c
int color material_personalized__highlighted_text_inverse 0x7f05025d
int color material_personalized_color_background 0x7f05025e
int color material_personalized_color_control_activated 0x7f05025f
int color material_personalized_color_control_highlight 0x7f050260
int color material_personalized_color_control_normal 0x7f050261
int color material_personalized_color_error 0x7f050262
int color material_personalized_color_error_container 0x7f050263
int color material_personalized_color_on_background 0x7f050264
int color material_personalized_color_on_error 0x7f050265
int color material_personalized_color_on_error_container 0x7f050266
int color material_personalized_color_on_primary 0x7f050267
int color material_personalized_color_on_primary_container 0x7f050268
int color material_personalized_color_on_secondary 0x7f050269
int color material_personalized_color_on_secondary_container 0x7f05026a
int color material_personalized_color_on_surface 0x7f05026b
int color material_personalized_color_on_surface_inverse 0x7f05026c
int color material_personalized_color_on_surface_variant 0x7f05026d
int color material_personalized_color_on_tertiary 0x7f05026e
int color material_personalized_color_on_tertiary_container 0x7f05026f
int color material_personalized_color_outline 0x7f050270
int color material_personalized_color_outline_variant 0x7f050271
int color material_personalized_color_primary 0x7f050272
int color material_personalized_color_primary_container 0x7f050273
int color material_personalized_color_primary_inverse 0x7f050274
int color material_personalized_color_primary_text 0x7f050275
int color material_personalized_color_primary_text_inverse 0x7f050276
int color material_personalized_color_secondary 0x7f050277
int color material_personalized_color_secondary_container 0x7f050278
int color material_personalized_color_secondary_text 0x7f050279
int color material_personalized_color_secondary_text_inverse 0x7f05027a
int color material_personalized_color_surface 0x7f05027b
int color material_personalized_color_surface_bright 0x7f05027c
int color material_personalized_color_surface_container 0x7f05027d
int color material_personalized_color_surface_container_high 0x7f05027e
int color material_personalized_color_surface_container_highest 0x7f05027f
int color material_personalized_color_surface_container_low 0x7f050280
int color material_personalized_color_surface_container_lowest 0x7f050281
int color material_personalized_color_surface_dim 0x7f050282
int color material_personalized_color_surface_inverse 0x7f050283
int color material_personalized_color_surface_variant 0x7f050284
int color material_personalized_color_tertiary 0x7f050285
int color material_personalized_color_tertiary_container 0x7f050286
int color material_personalized_color_text_hint_foreground_inverse 0x7f050287
int color material_personalized_color_text_primary_inverse 0x7f050288
int color material_personalized_color_text_primary_inverse_disable_only 0x7f050289
int color material_personalized_color_text_secondary_and_tertiary_inverse 0x7f05028a
int color material_personalized_color_text_secondary_and_tertiary_inverse_disabled 0x7f05028b
int color material_personalized_hint_foreground 0x7f05028c
int color material_personalized_hint_foreground_inverse 0x7f05028d
int color material_personalized_primary_inverse_text_disable_only 0x7f05028e
int color material_personalized_primary_text_disable_only 0x7f05028f
int color material_slider_active_tick_marks_color 0x7f050290
int color material_slider_active_track_color 0x7f050291
int color material_slider_halo_color 0x7f050292
int color material_slider_inactive_tick_marks_color 0x7f050293
int color material_slider_inactive_track_color 0x7f050294
int color material_slider_thumb_color 0x7f050295
int color material_timepicker_button_background 0x7f050296
int color material_timepicker_button_stroke 0x7f050297
int color material_timepicker_clock_text_color 0x7f050298
int color material_timepicker_clockface 0x7f050299
int color material_timepicker_modebutton_tint 0x7f05029a
int color mtrl_btn_bg_color_selector 0x7f05029b
int color mtrl_btn_ripple_color 0x7f05029c
int color mtrl_btn_stroke_color_selector 0x7f05029d
int color mtrl_btn_text_btn_bg_color_selector 0x7f05029e
int color mtrl_btn_text_btn_ripple_color 0x7f05029f
int color mtrl_btn_text_color_disabled 0x7f0502a0
int color mtrl_btn_text_color_selector 0x7f0502a1
int color mtrl_btn_transparent_bg_color 0x7f0502a2
int color mtrl_calendar_item_stroke_color 0x7f0502a3
int color mtrl_calendar_selected_range 0x7f0502a4
int color mtrl_card_view_foreground 0x7f0502a5
int color mtrl_card_view_ripple 0x7f0502a6
int color mtrl_chip_background_color 0x7f0502a7
int color mtrl_chip_close_icon_tint 0x7f0502a8
int color mtrl_chip_surface_color 0x7f0502a9
int color mtrl_chip_text_color 0x7f0502aa
int color mtrl_choice_chip_background_color 0x7f0502ab
int color mtrl_choice_chip_ripple_color 0x7f0502ac
int color mtrl_choice_chip_text_color 0x7f0502ad
int color mtrl_error 0x7f0502ae
int color mtrl_fab_bg_color_selector 0x7f0502af
int color mtrl_fab_icon_text_color_selector 0x7f0502b0
int color mtrl_fab_ripple_color 0x7f0502b1
int color mtrl_filled_background_color 0x7f0502b2
int color mtrl_filled_icon_tint 0x7f0502b3
int color mtrl_filled_stroke_color 0x7f0502b4
int color mtrl_indicator_text_color 0x7f0502b5
int color mtrl_navigation_bar_colored_item_tint 0x7f0502b6
int color mtrl_navigation_bar_colored_ripple_color 0x7f0502b7
int color mtrl_navigation_bar_item_tint 0x7f0502b8
int color mtrl_navigation_bar_ripple_color 0x7f0502b9
int color mtrl_navigation_item_background_color 0x7f0502ba
int color mtrl_navigation_item_icon_tint 0x7f0502bb
int color mtrl_navigation_item_text_color 0x7f0502bc
int color mtrl_on_primary_text_btn_text_color_selector 0x7f0502bd
int color mtrl_on_surface_ripple_color 0x7f0502be
int color mtrl_outlined_icon_tint 0x7f0502bf
int color mtrl_outlined_stroke_color 0x7f0502c0
int color mtrl_popupmenu_overlay_color 0x7f0502c1
int color mtrl_scrim_color 0x7f0502c2
int color mtrl_switch_thumb_icon_tint 0x7f0502c3
int color mtrl_switch_thumb_tint 0x7f0502c4
int color mtrl_switch_track_decoration_tint 0x7f0502c5
int color mtrl_switch_track_tint 0x7f0502c6
int color mtrl_tabs_colored_ripple_color 0x7f0502c7
int color mtrl_tabs_icon_color_selector 0x7f0502c8
int color mtrl_tabs_icon_color_selector_colored 0x7f0502c9
int color mtrl_tabs_legacy_text_color_selector 0x7f0502ca
int color mtrl_tabs_ripple_color 0x7f0502cb
int color mtrl_text_btn_text_color_selector 0x7f0502cc
int color mtrl_textinput_default_box_stroke_color 0x7f0502cd
int color mtrl_textinput_disabled_color 0x7f0502ce
int color mtrl_textinput_filled_box_default_background_color 0x7f0502cf
int color mtrl_textinput_focused_box_stroke_color 0x7f0502d0
int color mtrl_textinput_hovered_box_stroke_color 0x7f0502d1
int color notification_action_color_filter 0x7f0502d2
int color notification_icon_bg_color 0x7f0502d3
int color notification_material_background_media_default_color 0x7f0502d4
int color primary_blue 0x7f0502d5
int color primary_blue_dark 0x7f0502d6
int color primary_dark_material_dark 0x7f0502d7
int color primary_dark_material_light 0x7f0502d8
int color primary_material_dark 0x7f0502d9
int color primary_material_light 0x7f0502da
int color primary_text_default_material_dark 0x7f0502db
int color primary_text_default_material_light 0x7f0502dc
int color primary_text_disabled_material_dark 0x7f0502dd
int color primary_text_disabled_material_light 0x7f0502de
int color purple_200 0x7f0502df
int color purple_500 0x7f0502e0
int color purple_700 0x7f0502e1
int color ripple_material_dark 0x7f0502e2
int color ripple_material_light 0x7f0502e3
int color secondary_text_default_material_dark 0x7f0502e4
int color secondary_text_default_material_light 0x7f0502e5
int color secondary_text_disabled_material_dark 0x7f0502e6
int color secondary_text_disabled_material_light 0x7f0502e7
int color stamina_color 0x7f0502e8
int color switch_thumb_disabled_material_dark 0x7f0502e9
int color switch_thumb_disabled_material_light 0x7f0502ea
int color switch_thumb_material_dark 0x7f0502eb
int color switch_thumb_material_light 0x7f0502ec
int color switch_thumb_normal_material_dark 0x7f0502ed
int color switch_thumb_normal_material_light 0x7f0502ee
int color teal_200 0x7f0502ef
int color teal_700 0x7f0502f0
int color text_primary 0x7f0502f1
int color text_secondary 0x7f0502f2
int color tooltip_background_dark 0x7f0502f3
int color tooltip_background_light 0x7f0502f4
int color warmth_color 0x7f0502f5
int color white 0x7f0502f6
int dimen abc_action_bar_content_inset_material 0x7f060000
int dimen abc_action_bar_content_inset_with_nav 0x7f060001
int dimen abc_action_bar_default_height_material 0x7f060002
int dimen abc_action_bar_default_padding_end_material 0x7f060003
int dimen abc_action_bar_default_padding_start_material 0x7f060004
int dimen abc_action_bar_elevation_material 0x7f060005
int dimen abc_action_bar_icon_vertical_padding_material 0x7f060006
int dimen abc_action_bar_overflow_padding_end_material 0x7f060007
int dimen abc_action_bar_overflow_padding_start_material 0x7f060008
int dimen abc_action_bar_stacked_max_height 0x7f060009
int dimen abc_action_bar_stacked_tab_max_width 0x7f06000a
int dimen abc_action_bar_subtitle_bottom_margin_material 0x7f06000b
int dimen abc_action_bar_subtitle_top_margin_material 0x7f06000c
int dimen abc_action_button_min_height_material 0x7f06000d
int dimen abc_action_button_min_width_material 0x7f06000e
int dimen abc_action_button_min_width_overflow_material 0x7f06000f
int dimen abc_alert_dialog_button_bar_height 0x7f060010
int dimen abc_alert_dialog_button_dimen 0x7f060011
int dimen abc_button_inset_horizontal_material 0x7f060012
int dimen abc_button_inset_vertical_material 0x7f060013
int dimen abc_button_padding_horizontal_material 0x7f060014
int dimen abc_button_padding_vertical_material 0x7f060015
int dimen abc_cascading_menus_min_smallest_width 0x7f060016
int dimen abc_config_prefDialogWidth 0x7f060017
int dimen abc_control_corner_material 0x7f060018
int dimen abc_control_inset_material 0x7f060019
int dimen abc_control_padding_material 0x7f06001a
int dimen abc_dialog_corner_radius_material 0x7f06001b
int dimen abc_dialog_fixed_height_major 0x7f06001c
int dimen abc_dialog_fixed_height_minor 0x7f06001d
int dimen abc_dialog_fixed_width_major 0x7f06001e
int dimen abc_dialog_fixed_width_minor 0x7f06001f
int dimen abc_dialog_list_padding_bottom_no_buttons 0x7f060020
int dimen abc_dialog_list_padding_top_no_title 0x7f060021
int dimen abc_dialog_min_width_major 0x7f060022
int dimen abc_dialog_min_width_minor 0x7f060023
int dimen abc_dialog_padding_material 0x7f060024
int dimen abc_dialog_padding_top_material 0x7f060025
int dimen abc_dialog_title_divider_material 0x7f060026
int dimen abc_disabled_alpha_material_dark 0x7f060027
int dimen abc_disabled_alpha_material_light 0x7f060028
int dimen abc_dropdownitem_icon_width 0x7f060029
int dimen abc_dropdownitem_text_padding_left 0x7f06002a
int dimen abc_dropdownitem_text_padding_right 0x7f06002b
int dimen abc_edit_text_inset_bottom_material 0x7f06002c
int dimen abc_edit_text_inset_horizontal_material 0x7f06002d
int dimen abc_edit_text_inset_top_material 0x7f06002e
int dimen abc_floating_window_z 0x7f06002f
int dimen abc_list_item_height_large_material 0x7f060030
int dimen abc_list_item_height_material 0x7f060031
int dimen abc_list_item_height_small_material 0x7f060032
int dimen abc_list_item_padding_horizontal_material 0x7f060033
int dimen abc_panel_menu_list_width 0x7f060034
int dimen abc_progress_bar_height_material 0x7f060035
int dimen abc_search_view_preferred_height 0x7f060036
int dimen abc_search_view_preferred_width 0x7f060037
int dimen abc_seekbar_track_background_height_material 0x7f060038
int dimen abc_seekbar_track_progress_height_material 0x7f060039
int dimen abc_select_dialog_padding_start_material 0x7f06003a
int dimen abc_star_big 0x7f06003b
int dimen abc_star_medium 0x7f06003c
int dimen abc_star_small 0x7f06003d
int dimen abc_switch_padding 0x7f06003e
int dimen abc_text_size_body_1_material 0x7f06003f
int dimen abc_text_size_body_2_material 0x7f060040
int dimen abc_text_size_button_material 0x7f060041
int dimen abc_text_size_caption_material 0x7f060042
int dimen abc_text_size_display_1_material 0x7f060043
int dimen abc_text_size_display_2_material 0x7f060044
int dimen abc_text_size_display_3_material 0x7f060045
int dimen abc_text_size_display_4_material 0x7f060046
int dimen abc_text_size_headline_material 0x7f060047
int dimen abc_text_size_large_material 0x7f060048
int dimen abc_text_size_medium_material 0x7f060049
int dimen abc_text_size_menu_header_material 0x7f06004a
int dimen abc_text_size_menu_material 0x7f06004b
int dimen abc_text_size_small_material 0x7f06004c
int dimen abc_text_size_subhead_material 0x7f06004d
int dimen abc_text_size_subtitle_material_toolbar 0x7f06004e
int dimen abc_text_size_title_material 0x7f06004f
int dimen abc_text_size_title_material_toolbar 0x7f060050
int dimen appcompat_dialog_background_inset 0x7f060051
int dimen cardview_compat_inset_shadow 0x7f060052
int dimen cardview_default_elevation 0x7f060053
int dimen cardview_default_radius 0x7f060054
int dimen clock_face_margin_start 0x7f060055
int dimen compat_button_inset_horizontal_material 0x7f060056
int dimen compat_button_inset_vertical_material 0x7f060057
int dimen compat_button_padding_horizontal_material 0x7f060058
int dimen compat_button_padding_vertical_material 0x7f060059
int dimen compat_control_corner_material 0x7f06005a
int dimen compat_notification_large_icon_max_height 0x7f06005b
int dimen compat_notification_large_icon_max_width 0x7f06005c
int dimen def_drawer_elevation 0x7f06005d
int dimen design_appbar_elevation 0x7f06005e
int dimen design_bottom_navigation_active_item_max_width 0x7f06005f
int dimen design_bottom_navigation_active_item_min_width 0x7f060060
int dimen design_bottom_navigation_active_text_size 0x7f060061
int dimen design_bottom_navigation_elevation 0x7f060062
int dimen design_bottom_navigation_height 0x7f060063
int dimen design_bottom_navigation_icon_size 0x7f060064
int dimen design_bottom_navigation_item_max_width 0x7f060065
int dimen design_bottom_navigation_item_min_width 0x7f060066
int dimen design_bottom_navigation_label_padding 0x7f060067
int dimen design_bottom_navigation_margin 0x7f060068
int dimen design_bottom_navigation_shadow_height 0x7f060069
int dimen design_bottom_navigation_text_size 0x7f06006a
int dimen design_bottom_sheet_elevation 0x7f06006b
int dimen design_bottom_sheet_modal_elevation 0x7f06006c
int dimen design_bottom_sheet_peek_height_min 0x7f06006d
int dimen design_fab_border_width 0x7f06006e
int dimen design_fab_elevation 0x7f06006f
int dimen design_fab_image_size 0x7f060070
int dimen design_fab_size_mini 0x7f060071
int dimen design_fab_size_normal 0x7f060072
int dimen design_fab_translation_z_hovered_focused 0x7f060073
int dimen design_fab_translation_z_pressed 0x7f060074
int dimen design_navigation_elevation 0x7f060075
int dimen design_navigation_icon_padding 0x7f060076
int dimen design_navigation_icon_size 0x7f060077
int dimen design_navigation_item_horizontal_padding 0x7f060078
int dimen design_navigation_item_icon_padding 0x7f060079
int dimen design_navigation_item_vertical_padding 0x7f06007a
int dimen design_navigation_max_width 0x7f06007b
int dimen design_navigation_padding_bottom 0x7f06007c
int dimen design_navigation_separator_vertical_padding 0x7f06007d
int dimen design_snackbar_action_inline_max_width 0x7f06007e
int dimen design_snackbar_action_text_color_alpha 0x7f06007f
int dimen design_snackbar_background_corner_radius 0x7f060080
int dimen design_snackbar_elevation 0x7f060081
int dimen design_snackbar_extra_spacing_horizontal 0x7f060082
int dimen design_snackbar_max_width 0x7f060083
int dimen design_snackbar_min_width 0x7f060084
int dimen design_snackbar_padding_horizontal 0x7f060085
int dimen design_snackbar_padding_vertical 0x7f060086
int dimen design_snackbar_padding_vertical_2lines 0x7f060087
int dimen design_snackbar_text_size 0x7f060088
int dimen design_tab_max_width 0x7f060089
int dimen design_tab_scrollable_min_width 0x7f06008a
int dimen design_tab_text_size 0x7f06008b
int dimen design_tab_text_size_2line 0x7f06008c
int dimen design_textinput_caption_translate_y 0x7f06008d
int dimen disabled_alpha_material_dark 0x7f06008e
int dimen disabled_alpha_material_light 0x7f06008f
int dimen fastscroll_default_thickness 0x7f060090
int dimen fastscroll_margin 0x7f060091
int dimen fastscroll_minimum_range 0x7f060092
int dimen highlight_alpha_material_colored 0x7f060093
int dimen highlight_alpha_material_dark 0x7f060094
int dimen highlight_alpha_material_light 0x7f060095
int dimen hint_alpha_material_dark 0x7f060096
int dimen hint_alpha_material_light 0x7f060097
int dimen hint_pressed_alpha_material_dark 0x7f060098
int dimen hint_pressed_alpha_material_light 0x7f060099
int dimen item_touch_helper_max_drag_scroll_per_frame 0x7f06009a
int dimen item_touch_helper_swipe_escape_max_velocity 0x7f06009b
int dimen item_touch_helper_swipe_escape_velocity 0x7f06009c
int dimen m3_alert_dialog_action_bottom_padding 0x7f06009d
int dimen m3_alert_dialog_action_top_padding 0x7f06009e
int dimen m3_alert_dialog_corner_size 0x7f06009f
int dimen m3_alert_dialog_elevation 0x7f0600a0
int dimen m3_alert_dialog_icon_margin 0x7f0600a1
int dimen m3_alert_dialog_icon_size 0x7f0600a2
int dimen m3_alert_dialog_title_bottom_margin 0x7f0600a3
int dimen m3_appbar_expanded_title_margin_bottom 0x7f0600a4
int dimen m3_appbar_expanded_title_margin_horizontal 0x7f0600a5
int dimen m3_appbar_scrim_height_trigger 0x7f0600a6
int dimen m3_appbar_scrim_height_trigger_large 0x7f0600a7
int dimen m3_appbar_scrim_height_trigger_medium 0x7f0600a8
int dimen m3_appbar_size_compact 0x7f0600a9
int dimen m3_appbar_size_large 0x7f0600aa
int dimen m3_appbar_size_medium 0x7f0600ab
int dimen m3_badge_horizontal_offset 0x7f0600ac
int dimen m3_badge_offset 0x7f0600ad
int dimen m3_badge_size 0x7f0600ae
int dimen m3_badge_vertical_offset 0x7f0600af
int dimen m3_badge_with_text_horizontal_offset 0x7f0600b0
int dimen m3_badge_with_text_offset 0x7f0600b1
int dimen m3_badge_with_text_size 0x7f0600b2
int dimen m3_badge_with_text_vertical_offset 0x7f0600b3
int dimen m3_bottom_nav_item_active_indicator_height 0x7f0600b4
int dimen m3_bottom_nav_item_active_indicator_margin_horizontal 0x7f0600b5
int dimen m3_bottom_nav_item_active_indicator_width 0x7f0600b6
int dimen m3_bottom_nav_item_padding_bottom 0x7f0600b7
int dimen m3_bottom_nav_item_padding_top 0x7f0600b8
int dimen m3_bottom_nav_min_height 0x7f0600b9
int dimen m3_bottom_sheet_drag_handle_bottom_padding 0x7f0600ba
int dimen m3_bottom_sheet_elevation 0x7f0600bb
int dimen m3_bottom_sheet_modal_elevation 0x7f0600bc
int dimen m3_bottomappbar_fab_cradle_margin 0x7f0600bd
int dimen m3_bottomappbar_fab_cradle_rounded_corner_radius 0x7f0600be
int dimen m3_bottomappbar_fab_cradle_vertical_offset 0x7f0600bf
int dimen m3_bottomappbar_fab_end_margin 0x7f0600c0
int dimen m3_bottomappbar_height 0x7f0600c1
int dimen m3_bottomappbar_horizontal_padding 0x7f0600c2
int dimen m3_btn_dialog_btn_min_width 0x7f0600c3
int dimen m3_btn_dialog_btn_spacing 0x7f0600c4
int dimen m3_btn_disabled_elevation 0x7f0600c5
int dimen m3_btn_disabled_translation_z 0x7f0600c6
int dimen m3_btn_elevated_btn_elevation 0x7f0600c7
int dimen m3_btn_elevation 0x7f0600c8
int dimen m3_btn_icon_btn_padding_left 0x7f0600c9
int dimen m3_btn_icon_btn_padding_right 0x7f0600ca
int dimen m3_btn_icon_only_default_padding 0x7f0600cb
int dimen m3_btn_icon_only_default_size 0x7f0600cc
int dimen m3_btn_icon_only_icon_padding 0x7f0600cd
int dimen m3_btn_icon_only_min_width 0x7f0600ce
int dimen m3_btn_inset 0x7f0600cf
int dimen m3_btn_max_width 0x7f0600d0
int dimen m3_btn_padding_bottom 0x7f0600d1
int dimen m3_btn_padding_left 0x7f0600d2
int dimen m3_btn_padding_right 0x7f0600d3
int dimen m3_btn_padding_top 0x7f0600d4
int dimen m3_btn_stroke_size 0x7f0600d5
int dimen m3_btn_text_btn_icon_padding_left 0x7f0600d6
int dimen m3_btn_text_btn_icon_padding_right 0x7f0600d7
int dimen m3_btn_text_btn_padding_left 0x7f0600d8
int dimen m3_btn_text_btn_padding_right 0x7f0600d9
int dimen m3_btn_translation_z_base 0x7f0600da
int dimen m3_btn_translation_z_hovered 0x7f0600db
int dimen m3_card_disabled_z 0x7f0600dc
int dimen m3_card_dragged_z 0x7f0600dd
int dimen m3_card_elevated_disabled_z 0x7f0600de
int dimen m3_card_elevated_dragged_z 0x7f0600df
int dimen m3_card_elevated_elevation 0x7f0600e0
int dimen m3_card_elevated_hovered_z 0x7f0600e1
int dimen m3_card_elevation 0x7f0600e2
int dimen m3_card_hovered_z 0x7f0600e3
int dimen m3_card_stroke_width 0x7f0600e4
int dimen m3_carousel_debug_keyline_width 0x7f0600e5
int dimen m3_carousel_extra_small_item_size 0x7f0600e6
int dimen m3_carousel_gone_size 0x7f0600e7
int dimen m3_carousel_small_item_default_corner_size 0x7f0600e8
int dimen m3_carousel_small_item_size_max 0x7f0600e9
int dimen m3_carousel_small_item_size_min 0x7f0600ea
int dimen m3_chip_checked_hovered_translation_z 0x7f0600eb
int dimen m3_chip_corner_size 0x7f0600ec
int dimen m3_chip_disabled_translation_z 0x7f0600ed
int dimen m3_chip_dragged_translation_z 0x7f0600ee
int dimen m3_chip_elevated_elevation 0x7f0600ef
int dimen m3_chip_hovered_translation_z 0x7f0600f0
int dimen m3_chip_icon_size 0x7f0600f1
int dimen m3_comp_assist_chip_container_height 0x7f0600f2
int dimen m3_comp_assist_chip_elevated_container_elevation 0x7f0600f3
int dimen m3_comp_assist_chip_flat_container_elevation 0x7f0600f4
int dimen m3_comp_assist_chip_flat_outline_width 0x7f0600f5
int dimen m3_comp_assist_chip_with_icon_icon_size 0x7f0600f6
int dimen m3_comp_badge_large_size 0x7f0600f7
int dimen m3_comp_badge_size 0x7f0600f8
int dimen m3_comp_bottom_app_bar_container_elevation 0x7f0600f9
int dimen m3_comp_bottom_app_bar_container_height 0x7f0600fa
int dimen m3_comp_checkbox_selected_disabled_container_opacity 0x7f0600fb
int dimen m3_comp_circular_progress_indicator_active_indicator_width 0x7f0600fc
int dimen m3_comp_divider_thickness 0x7f0600fd
int dimen m3_comp_elevated_button_container_elevation 0x7f0600fe
int dimen m3_comp_elevated_button_disabled_container_elevation 0x7f0600ff
int dimen m3_comp_elevated_card_container_elevation 0x7f060100
int dimen m3_comp_elevated_card_icon_size 0x7f060101
int dimen m3_comp_extended_fab_primary_container_elevation 0x7f060102
int dimen m3_comp_extended_fab_primary_container_height 0x7f060103
int dimen m3_comp_extended_fab_primary_focus_container_elevation 0x7f060104
int dimen m3_comp_extended_fab_primary_focus_state_layer_opacity 0x7f060105
int dimen m3_comp_extended_fab_primary_hover_container_elevation 0x7f060106
int dimen m3_comp_extended_fab_primary_hover_state_layer_opacity 0x7f060107
int dimen m3_comp_extended_fab_primary_icon_size 0x7f060108
int dimen m3_comp_extended_fab_primary_pressed_container_elevation 0x7f060109
int dimen m3_comp_extended_fab_primary_pressed_state_layer_opacity 0x7f06010a
int dimen m3_comp_fab_primary_container_elevation 0x7f06010b
int dimen m3_comp_fab_primary_container_height 0x7f06010c
int dimen m3_comp_fab_primary_focus_state_layer_opacity 0x7f06010d
int dimen m3_comp_fab_primary_hover_container_elevation 0x7f06010e
int dimen m3_comp_fab_primary_hover_state_layer_opacity 0x7f06010f
int dimen m3_comp_fab_primary_icon_size 0x7f060110
int dimen m3_comp_fab_primary_large_container_height 0x7f060111
int dimen m3_comp_fab_primary_large_icon_size 0x7f060112
int dimen m3_comp_fab_primary_pressed_container_elevation 0x7f060113
int dimen m3_comp_fab_primary_pressed_state_layer_opacity 0x7f060114
int dimen m3_comp_fab_primary_small_container_height 0x7f060115
int dimen m3_comp_fab_primary_small_icon_size 0x7f060116
int dimen m3_comp_filled_autocomplete_menu_container_elevation 0x7f060117
int dimen m3_comp_filled_button_container_elevation 0x7f060118
int dimen m3_comp_filled_button_with_icon_icon_size 0x7f060119
int dimen m3_comp_filled_card_container_elevation 0x7f06011a
int dimen m3_comp_filled_card_dragged_state_layer_opacity 0x7f06011b
int dimen m3_comp_filled_card_focus_state_layer_opacity 0x7f06011c
int dimen m3_comp_filled_card_hover_state_layer_opacity 0x7f06011d
int dimen m3_comp_filled_card_icon_size 0x7f06011e
int dimen m3_comp_filled_card_pressed_state_layer_opacity 0x7f06011f
int dimen m3_comp_filled_text_field_disabled_active_indicator_opacity 0x7f060120
int dimen m3_comp_filter_chip_container_height 0x7f060121
int dimen m3_comp_filter_chip_elevated_container_elevation 0x7f060122
int dimen m3_comp_filter_chip_flat_container_elevation 0x7f060123
int dimen m3_comp_filter_chip_flat_unselected_outline_width 0x7f060124
int dimen m3_comp_filter_chip_with_icon_icon_size 0x7f060125
int dimen m3_comp_input_chip_container_elevation 0x7f060126
int dimen m3_comp_input_chip_container_height 0x7f060127
int dimen m3_comp_input_chip_unselected_outline_width 0x7f060128
int dimen m3_comp_input_chip_with_avatar_avatar_size 0x7f060129
int dimen m3_comp_input_chip_with_leading_icon_leading_icon_size 0x7f06012a
int dimen m3_comp_linear_progress_indicator_active_indicator_height 0x7f06012b
int dimen m3_comp_menu_container_elevation 0x7f06012c
int dimen m3_comp_navigation_bar_active_indicator_height 0x7f06012d
int dimen m3_comp_navigation_bar_active_indicator_width 0x7f06012e
int dimen m3_comp_navigation_bar_container_elevation 0x7f06012f
int dimen m3_comp_navigation_bar_container_height 0x7f060130
int dimen m3_comp_navigation_bar_focus_state_layer_opacity 0x7f060131
int dimen m3_comp_navigation_bar_hover_state_layer_opacity 0x7f060132
int dimen m3_comp_navigation_bar_icon_size 0x7f060133
int dimen m3_comp_navigation_bar_pressed_state_layer_opacity 0x7f060134
int dimen m3_comp_navigation_drawer_container_width 0x7f060135
int dimen m3_comp_navigation_drawer_focus_state_layer_opacity 0x7f060136
int dimen m3_comp_navigation_drawer_hover_state_layer_opacity 0x7f060137
int dimen m3_comp_navigation_drawer_icon_size 0x7f060138
int dimen m3_comp_navigation_drawer_modal_container_elevation 0x7f060139
int dimen m3_comp_navigation_drawer_pressed_state_layer_opacity 0x7f06013a
int dimen m3_comp_navigation_drawer_standard_container_elevation 0x7f06013b
int dimen m3_comp_navigation_rail_active_indicator_height 0x7f06013c
int dimen m3_comp_navigation_rail_active_indicator_width 0x7f06013d
int dimen m3_comp_navigation_rail_container_elevation 0x7f06013e
int dimen m3_comp_navigation_rail_container_width 0x7f06013f
int dimen m3_comp_navigation_rail_icon_size 0x7f060140
int dimen m3_comp_outlined_autocomplete_menu_container_elevation 0x7f060141
int dimen m3_comp_outlined_button_disabled_outline_opacity 0x7f060142
int dimen m3_comp_outlined_button_outline_width 0x7f060143
int dimen m3_comp_outlined_card_container_elevation 0x7f060144
int dimen m3_comp_outlined_card_disabled_outline_opacity 0x7f060145
int dimen m3_comp_outlined_card_icon_size 0x7f060146
int dimen m3_comp_outlined_card_outline_width 0x7f060147
int dimen m3_comp_outlined_icon_button_unselected_outline_width 0x7f060148
int dimen m3_comp_outlined_text_field_disabled_input_text_opacity 0x7f060149
int dimen m3_comp_outlined_text_field_disabled_label_text_opacity 0x7f06014a
int dimen m3_comp_outlined_text_field_disabled_supporting_text_opacity 0x7f06014b
int dimen m3_comp_outlined_text_field_focus_outline_width 0x7f06014c
int dimen m3_comp_outlined_text_field_outline_width 0x7f06014d
int dimen m3_comp_primary_navigation_tab_active_focus_state_layer_opacity 0x7f06014e
int dimen m3_comp_primary_navigation_tab_active_hover_state_layer_opacity 0x7f06014f
int dimen m3_comp_primary_navigation_tab_active_indicator_height 0x7f060150
int dimen m3_comp_primary_navigation_tab_active_pressed_state_layer_opacity 0x7f060151
int dimen m3_comp_primary_navigation_tab_divider_height 0x7f060152
int dimen m3_comp_primary_navigation_tab_inactive_focus_state_layer_opacity 0x7f060153
int dimen m3_comp_primary_navigation_tab_inactive_hover_state_layer_opacity 0x7f060154
int dimen m3_comp_primary_navigation_tab_inactive_pressed_state_layer_opacity 0x7f060155
int dimen m3_comp_primary_navigation_tab_with_icon_icon_size 0x7f060156
int dimen m3_comp_radio_button_disabled_selected_icon_opacity 0x7f060157
int dimen m3_comp_radio_button_disabled_unselected_icon_opacity 0x7f060158
int dimen m3_comp_radio_button_selected_focus_state_layer_opacity 0x7f060159
int dimen m3_comp_radio_button_selected_hover_state_layer_opacity 0x7f06015a
int dimen m3_comp_radio_button_selected_pressed_state_layer_opacity 0x7f06015b
int dimen m3_comp_radio_button_unselected_focus_state_layer_opacity 0x7f06015c
int dimen m3_comp_radio_button_unselected_hover_state_layer_opacity 0x7f06015d
int dimen m3_comp_radio_button_unselected_pressed_state_layer_opacity 0x7f06015e
int dimen m3_comp_search_bar_avatar_size 0x7f06015f
int dimen m3_comp_search_bar_container_elevation 0x7f060160
int dimen m3_comp_search_bar_container_height 0x7f060161
int dimen m3_comp_search_bar_hover_state_layer_opacity 0x7f060162
int dimen m3_comp_search_bar_pressed_state_layer_opacity 0x7f060163
int dimen m3_comp_search_view_container_elevation 0x7f060164
int dimen m3_comp_search_view_docked_header_container_height 0x7f060165
int dimen m3_comp_search_view_full_screen_header_container_height 0x7f060166
int dimen m3_comp_secondary_navigation_tab_active_indicator_height 0x7f060167
int dimen m3_comp_secondary_navigation_tab_focus_state_layer_opacity 0x7f060168
int dimen m3_comp_secondary_navigation_tab_hover_state_layer_opacity 0x7f060169
int dimen m3_comp_secondary_navigation_tab_pressed_state_layer_opacity 0x7f06016a
int dimen m3_comp_sheet_bottom_docked_modal_container_elevation 0x7f06016b
int dimen m3_comp_sheet_bottom_docked_standard_container_elevation 0x7f06016c
int dimen m3_comp_sheet_side_docked_container_width 0x7f06016d
int dimen m3_comp_sheet_side_docked_modal_container_elevation 0x7f06016e
int dimen m3_comp_sheet_side_docked_standard_container_elevation 0x7f06016f
int dimen m3_comp_slider_disabled_active_track_opacity 0x7f060170
int dimen m3_comp_slider_disabled_handle_opacity 0x7f060171
int dimen m3_comp_slider_disabled_inactive_track_opacity 0x7f060172
int dimen m3_comp_slider_inactive_track_height 0x7f060173
int dimen m3_comp_snackbar_container_elevation 0x7f060174
int dimen m3_comp_suggestion_chip_container_height 0x7f060175
int dimen m3_comp_suggestion_chip_elevated_container_elevation 0x7f060176
int dimen m3_comp_suggestion_chip_flat_container_elevation 0x7f060177
int dimen m3_comp_suggestion_chip_flat_outline_width 0x7f060178
int dimen m3_comp_suggestion_chip_with_leading_icon_leading_icon_size 0x7f060179
int dimen m3_comp_switch_disabled_selected_handle_opacity 0x7f06017a
int dimen m3_comp_switch_disabled_selected_icon_opacity 0x7f06017b
int dimen m3_comp_switch_disabled_track_opacity 0x7f06017c
int dimen m3_comp_switch_disabled_unselected_handle_opacity 0x7f06017d
int dimen m3_comp_switch_disabled_unselected_icon_opacity 0x7f06017e
int dimen m3_comp_switch_selected_focus_state_layer_opacity 0x7f06017f
int dimen m3_comp_switch_selected_hover_state_layer_opacity 0x7f060180
int dimen m3_comp_switch_selected_pressed_state_layer_opacity 0x7f060181
int dimen m3_comp_switch_track_height 0x7f060182
int dimen m3_comp_switch_track_width 0x7f060183
int dimen m3_comp_switch_unselected_focus_state_layer_opacity 0x7f060184
int dimen m3_comp_switch_unselected_hover_state_layer_opacity 0x7f060185
int dimen m3_comp_switch_unselected_pressed_state_layer_opacity 0x7f060186
int dimen m3_comp_text_button_focus_state_layer_opacity 0x7f060187
int dimen m3_comp_text_button_hover_state_layer_opacity 0x7f060188
int dimen m3_comp_text_button_pressed_state_layer_opacity 0x7f060189
int dimen m3_comp_time_input_time_input_field_focus_outline_width 0x7f06018a
int dimen m3_comp_time_picker_container_elevation 0x7f06018b
int dimen m3_comp_time_picker_period_selector_focus_state_layer_opacity 0x7f06018c
int dimen m3_comp_time_picker_period_selector_hover_state_layer_opacity 0x7f06018d
int dimen m3_comp_time_picker_period_selector_outline_width 0x7f06018e
int dimen m3_comp_time_picker_period_selector_pressed_state_layer_opacity 0x7f06018f
int dimen m3_comp_time_picker_time_selector_focus_state_layer_opacity 0x7f060190
int dimen m3_comp_time_picker_time_selector_hover_state_layer_opacity 0x7f060191
int dimen m3_comp_time_picker_time_selector_pressed_state_layer_opacity 0x7f060192
int dimen m3_comp_top_app_bar_large_container_height 0x7f060193
int dimen m3_comp_top_app_bar_medium_container_height 0x7f060194
int dimen m3_comp_top_app_bar_small_container_elevation 0x7f060195
int dimen m3_comp_top_app_bar_small_container_height 0x7f060196
int dimen m3_comp_top_app_bar_small_on_scroll_container_elevation 0x7f060197
int dimen m3_datepicker_elevation 0x7f060198
int dimen m3_divider_heavy_thickness 0x7f060199
int dimen m3_extended_fab_bottom_padding 0x7f06019a
int dimen m3_extended_fab_end_padding 0x7f06019b
int dimen m3_extended_fab_icon_padding 0x7f06019c
int dimen m3_extended_fab_min_height 0x7f06019d
int dimen m3_extended_fab_start_padding 0x7f06019e
int dimen m3_extended_fab_top_padding 0x7f06019f
int dimen m3_fab_border_width 0x7f0601a0
int dimen m3_fab_corner_size 0x7f0601a1
int dimen m3_fab_translation_z_hovered_focused 0x7f0601a2
int dimen m3_fab_translation_z_pressed 0x7f0601a3
int dimen m3_large_fab_max_image_size 0x7f0601a4
int dimen m3_large_fab_size 0x7f0601a5
int dimen m3_menu_elevation 0x7f0601a6
int dimen m3_navigation_drawer_layout_corner_size 0x7f0601a7
int dimen m3_navigation_item_horizontal_padding 0x7f0601a8
int dimen m3_navigation_item_icon_padding 0x7f0601a9
int dimen m3_navigation_item_shape_inset_bottom 0x7f0601aa
int dimen m3_navigation_item_shape_inset_end 0x7f0601ab
int dimen m3_navigation_item_shape_inset_start 0x7f0601ac
int dimen m3_navigation_item_shape_inset_top 0x7f0601ad
int dimen m3_navigation_item_vertical_padding 0x7f0601ae
int dimen m3_navigation_menu_divider_horizontal_padding 0x7f0601af
int dimen m3_navigation_menu_headline_horizontal_padding 0x7f0601b0
int dimen m3_navigation_rail_default_width 0x7f0601b1
int dimen m3_navigation_rail_elevation 0x7f0601b2
int dimen m3_navigation_rail_icon_size 0x7f0601b3
int dimen m3_navigation_rail_item_active_indicator_height 0x7f0601b4
int dimen m3_navigation_rail_item_active_indicator_margin_horizontal 0x7f0601b5
int dimen m3_navigation_rail_item_active_indicator_width 0x7f0601b6
int dimen m3_navigation_rail_item_min_height 0x7f0601b7
int dimen m3_navigation_rail_item_padding_bottom 0x7f0601b8
int dimen m3_navigation_rail_item_padding_top 0x7f0601b9
int dimen m3_ripple_default_alpha 0x7f0601ba
int dimen m3_ripple_focused_alpha 0x7f0601bb
int dimen m3_ripple_hovered_alpha 0x7f0601bc
int dimen m3_ripple_pressed_alpha 0x7f0601bd
int dimen m3_ripple_selectable_pressed_alpha 0x7f0601be
int dimen m3_searchbar_elevation 0x7f0601bf
int dimen m3_searchbar_height 0x7f0601c0
int dimen m3_searchbar_margin_horizontal 0x7f0601c1
int dimen m3_searchbar_margin_vertical 0x7f0601c2
int dimen m3_searchbar_outlined_stroke_width 0x7f0601c3
int dimen m3_searchbar_padding_start 0x7f0601c4
int dimen m3_searchbar_text_margin_start_no_navigation_icon 0x7f0601c5
int dimen m3_searchbar_text_size 0x7f0601c6
int dimen m3_searchview_divider_size 0x7f0601c7
int dimen m3_searchview_elevation 0x7f0601c8
int dimen m3_searchview_height 0x7f0601c9
int dimen m3_side_sheet_margin_detached 0x7f0601ca
int dimen m3_side_sheet_modal_elevation 0x7f0601cb
int dimen m3_side_sheet_standard_elevation 0x7f0601cc
int dimen m3_side_sheet_width 0x7f0601cd
int dimen m3_simple_item_color_hovered_alpha 0x7f0601ce
int dimen m3_simple_item_color_selected_alpha 0x7f0601cf
int dimen m3_slider_inactive_track_height 0x7f0601d0
int dimen m3_slider_thumb_elevation 0x7f0601d1
int dimen m3_small_fab_max_image_size 0x7f0601d2
int dimen m3_small_fab_size 0x7f0601d3
int dimen m3_snackbar_action_text_color_alpha 0x7f0601d4
int dimen m3_snackbar_margin 0x7f0601d5
int dimen m3_sys_elevation_level0 0x7f0601d6
int dimen m3_sys_elevation_level1 0x7f0601d7
int dimen m3_sys_elevation_level2 0x7f0601d8
int dimen m3_sys_elevation_level3 0x7f0601d9
int dimen m3_sys_elevation_level4 0x7f0601da
int dimen m3_sys_elevation_level5 0x7f0601db
int dimen m3_sys_motion_easing_emphasized_accelerate_control_x1 0x7f0601dc
int dimen m3_sys_motion_easing_emphasized_accelerate_control_x2 0x7f0601dd
int dimen m3_sys_motion_easing_emphasized_accelerate_control_y1 0x7f0601de
int dimen m3_sys_motion_easing_emphasized_accelerate_control_y2 0x7f0601df
int dimen m3_sys_motion_easing_emphasized_decelerate_control_x1 0x7f0601e0
int dimen m3_sys_motion_easing_emphasized_decelerate_control_x2 0x7f0601e1
int dimen m3_sys_motion_easing_emphasized_decelerate_control_y1 0x7f0601e2
int dimen m3_sys_motion_easing_emphasized_decelerate_control_y2 0x7f0601e3
int dimen m3_sys_motion_easing_legacy_accelerate_control_x1 0x7f0601e4
int dimen m3_sys_motion_easing_legacy_accelerate_control_x2 0x7f0601e5
int dimen m3_sys_motion_easing_legacy_accelerate_control_y1 0x7f0601e6
int dimen m3_sys_motion_easing_legacy_accelerate_control_y2 0x7f0601e7
int dimen m3_sys_motion_easing_legacy_control_x1 0x7f0601e8
int dimen m3_sys_motion_easing_legacy_control_x2 0x7f0601e9
int dimen m3_sys_motion_easing_legacy_control_y1 0x7f0601ea
int dimen m3_sys_motion_easing_legacy_control_y2 0x7f0601eb
int dimen m3_sys_motion_easing_legacy_decelerate_control_x1 0x7f0601ec
int dimen m3_sys_motion_easing_legacy_decelerate_control_x2 0x7f0601ed
int dimen m3_sys_motion_easing_legacy_decelerate_control_y1 0x7f0601ee
int dimen m3_sys_motion_easing_legacy_decelerate_control_y2 0x7f0601ef
int dimen m3_sys_motion_easing_linear_control_x1 0x7f0601f0
int dimen m3_sys_motion_easing_linear_control_x2 0x7f0601f1
int dimen m3_sys_motion_easing_linear_control_y1 0x7f0601f2
int dimen m3_sys_motion_easing_linear_control_y2 0x7f0601f3
int dimen m3_sys_motion_easing_standard_accelerate_control_x1 0x7f0601f4
int dimen m3_sys_motion_easing_standard_accelerate_control_x2 0x7f0601f5
int dimen m3_sys_motion_easing_standard_accelerate_control_y1 0x7f0601f6
int dimen m3_sys_motion_easing_standard_accelerate_control_y2 0x7f0601f7
int dimen m3_sys_motion_easing_standard_control_x1 0x7f0601f8
int dimen m3_sys_motion_easing_standard_control_x2 0x7f0601f9
int dimen m3_sys_motion_easing_standard_control_y1 0x7f0601fa
int dimen m3_sys_motion_easing_standard_control_y2 0x7f0601fb
int dimen m3_sys_motion_easing_standard_decelerate_control_x1 0x7f0601fc
int dimen m3_sys_motion_easing_standard_decelerate_control_x2 0x7f0601fd
int dimen m3_sys_motion_easing_standard_decelerate_control_y1 0x7f0601fe
int dimen m3_sys_motion_easing_standard_decelerate_control_y2 0x7f0601ff
int dimen m3_sys_state_dragged_state_layer_opacity 0x7f060200
int dimen m3_sys_state_focus_state_layer_opacity 0x7f060201
int dimen m3_sys_state_hover_state_layer_opacity 0x7f060202
int dimen m3_sys_state_pressed_state_layer_opacity 0x7f060203
int dimen m3_timepicker_display_stroke_width 0x7f060204
int dimen m3_timepicker_window_elevation 0x7f060205
int dimen m3_toolbar_text_size_title 0x7f060206
int dimen material_bottom_sheet_max_width 0x7f060207
int dimen material_clock_display_height 0x7f060208
int dimen material_clock_display_padding 0x7f060209
int dimen material_clock_display_width 0x7f06020a
int dimen material_clock_face_margin_top 0x7f06020b
int dimen material_clock_hand_center_dot_radius 0x7f06020c
int dimen material_clock_hand_padding 0x7f06020d
int dimen material_clock_hand_stroke_width 0x7f06020e
int dimen material_clock_number_text_size 0x7f06020f
int dimen material_clock_period_toggle_height 0x7f060210
int dimen material_clock_period_toggle_horizontal_gap 0x7f060211
int dimen material_clock_period_toggle_vertical_gap 0x7f060212
int dimen material_clock_period_toggle_width 0x7f060213
int dimen material_clock_size 0x7f060214
int dimen material_cursor_inset 0x7f060215
int dimen material_cursor_width 0x7f060216
int dimen material_divider_thickness 0x7f060217
int dimen material_emphasis_disabled 0x7f060218
int dimen material_emphasis_disabled_background 0x7f060219
int dimen material_emphasis_high_type 0x7f06021a
int dimen material_emphasis_medium 0x7f06021b
int dimen material_filled_edittext_font_1_3_padding_bottom 0x7f06021c
int dimen material_filled_edittext_font_1_3_padding_top 0x7f06021d
int dimen material_filled_edittext_font_2_0_padding_bottom 0x7f06021e
int dimen material_filled_edittext_font_2_0_padding_top 0x7f06021f
int dimen material_font_1_3_box_collapsed_padding_top 0x7f060220
int dimen material_font_2_0_box_collapsed_padding_top 0x7f060221
int dimen material_helper_text_default_padding_top 0x7f060222
int dimen material_helper_text_font_1_3_padding_horizontal 0x7f060223
int dimen material_helper_text_font_1_3_padding_top 0x7f060224
int dimen material_input_text_to_prefix_suffix_padding 0x7f060225
int dimen material_textinput_default_width 0x7f060226
int dimen material_textinput_max_width 0x7f060227
int dimen material_textinput_min_width 0x7f060228
int dimen material_time_picker_minimum_screen_height 0x7f060229
int dimen material_time_picker_minimum_screen_width 0x7f06022a
int dimen material_timepicker_dialog_buttons_margin_top 0x7f06022b
int dimen mtrl_alert_dialog_background_inset_bottom 0x7f06022c
int dimen mtrl_alert_dialog_background_inset_end 0x7f06022d
int dimen mtrl_alert_dialog_background_inset_start 0x7f06022e
int dimen mtrl_alert_dialog_background_inset_top 0x7f06022f
int dimen mtrl_alert_dialog_picker_background_inset 0x7f060230
int dimen mtrl_badge_horizontal_edge_offset 0x7f060231
int dimen mtrl_badge_long_text_horizontal_padding 0x7f060232
int dimen mtrl_badge_size 0x7f060233
int dimen mtrl_badge_text_horizontal_edge_offset 0x7f060234
int dimen mtrl_badge_text_size 0x7f060235
int dimen mtrl_badge_toolbar_action_menu_item_horizontal_offset 0x7f060236
int dimen mtrl_badge_toolbar_action_menu_item_vertical_offset 0x7f060237
int dimen mtrl_badge_with_text_size 0x7f060238
int dimen mtrl_bottomappbar_fabOffsetEndMode 0x7f060239
int dimen mtrl_bottomappbar_fab_bottom_margin 0x7f06023a
int dimen mtrl_bottomappbar_fab_cradle_margin 0x7f06023b
int dimen mtrl_bottomappbar_fab_cradle_rounded_corner_radius 0x7f06023c
int dimen mtrl_bottomappbar_fab_cradle_vertical_offset 0x7f06023d
int dimen mtrl_bottomappbar_height 0x7f06023e
int dimen mtrl_btn_corner_radius 0x7f06023f
int dimen mtrl_btn_dialog_btn_min_width 0x7f060240
int dimen mtrl_btn_disabled_elevation 0x7f060241
int dimen mtrl_btn_disabled_z 0x7f060242
int dimen mtrl_btn_elevation 0x7f060243
int dimen mtrl_btn_focused_z 0x7f060244
int dimen mtrl_btn_hovered_z 0x7f060245
int dimen mtrl_btn_icon_btn_padding_left 0x7f060246
int dimen mtrl_btn_icon_padding 0x7f060247
int dimen mtrl_btn_inset 0x7f060248
int dimen mtrl_btn_letter_spacing 0x7f060249
int dimen mtrl_btn_max_width 0x7f06024a
int dimen mtrl_btn_padding_bottom 0x7f06024b
int dimen mtrl_btn_padding_left 0x7f06024c
int dimen mtrl_btn_padding_right 0x7f06024d
int dimen mtrl_btn_padding_top 0x7f06024e
int dimen mtrl_btn_pressed_z 0x7f06024f
int dimen mtrl_btn_snackbar_margin_horizontal 0x7f060250
int dimen mtrl_btn_stroke_size 0x7f060251
int dimen mtrl_btn_text_btn_icon_padding 0x7f060252
int dimen mtrl_btn_text_btn_padding_left 0x7f060253
int dimen mtrl_btn_text_btn_padding_right 0x7f060254
int dimen mtrl_btn_text_size 0x7f060255
int dimen mtrl_btn_z 0x7f060256
int dimen mtrl_calendar_action_confirm_button_min_width 0x7f060257
int dimen mtrl_calendar_action_height 0x7f060258
int dimen mtrl_calendar_action_padding 0x7f060259
int dimen mtrl_calendar_bottom_padding 0x7f06025a
int dimen mtrl_calendar_content_padding 0x7f06025b
int dimen mtrl_calendar_day_corner 0x7f06025c
int dimen mtrl_calendar_day_height 0x7f06025d
int dimen mtrl_calendar_day_horizontal_padding 0x7f06025e
int dimen mtrl_calendar_day_today_stroke 0x7f06025f
int dimen mtrl_calendar_day_vertical_padding 0x7f060260
int dimen mtrl_calendar_day_width 0x7f060261
int dimen mtrl_calendar_days_of_week_height 0x7f060262
int dimen mtrl_calendar_dialog_background_inset 0x7f060263
int dimen mtrl_calendar_header_content_padding 0x7f060264
int dimen mtrl_calendar_header_content_padding_fullscreen 0x7f060265
int dimen mtrl_calendar_header_divider_thickness 0x7f060266
int dimen mtrl_calendar_header_height 0x7f060267
int dimen mtrl_calendar_header_height_fullscreen 0x7f060268
int dimen mtrl_calendar_header_selection_line_height 0x7f060269
int dimen mtrl_calendar_header_text_padding 0x7f06026a
int dimen mtrl_calendar_header_toggle_margin_bottom 0x7f06026b
int dimen mtrl_calendar_header_toggle_margin_top 0x7f06026c
int dimen mtrl_calendar_landscape_header_width 0x7f06026d
int dimen mtrl_calendar_maximum_default_fullscreen_minor_axis 0x7f06026e
int dimen mtrl_calendar_month_horizontal_padding 0x7f06026f
int dimen mtrl_calendar_month_vertical_padding 0x7f060270
int dimen mtrl_calendar_navigation_bottom_padding 0x7f060271
int dimen mtrl_calendar_navigation_height 0x7f060272
int dimen mtrl_calendar_navigation_top_padding 0x7f060273
int dimen mtrl_calendar_pre_l_text_clip_padding 0x7f060274
int dimen mtrl_calendar_selection_baseline_to_top_fullscreen 0x7f060275
int dimen mtrl_calendar_selection_text_baseline_to_bottom 0x7f060276
int dimen mtrl_calendar_selection_text_baseline_to_bottom_fullscreen 0x7f060277
int dimen mtrl_calendar_selection_text_baseline_to_top 0x7f060278
int dimen mtrl_calendar_text_input_padding_top 0x7f060279
int dimen mtrl_calendar_title_baseline_to_top 0x7f06027a
int dimen mtrl_calendar_title_baseline_to_top_fullscreen 0x7f06027b
int dimen mtrl_calendar_year_corner 0x7f06027c
int dimen mtrl_calendar_year_height 0x7f06027d
int dimen mtrl_calendar_year_horizontal_padding 0x7f06027e
int dimen mtrl_calendar_year_vertical_padding 0x7f06027f
int dimen mtrl_calendar_year_width 0x7f060280
int dimen mtrl_card_checked_icon_margin 0x7f060281
int dimen mtrl_card_checked_icon_size 0x7f060282
int dimen mtrl_card_corner_radius 0x7f060283
int dimen mtrl_card_dragged_z 0x7f060284
int dimen mtrl_card_elevation 0x7f060285
int dimen mtrl_card_spacing 0x7f060286
int dimen mtrl_chip_pressed_translation_z 0x7f060287
int dimen mtrl_chip_text_size 0x7f060288
int dimen mtrl_exposed_dropdown_menu_popup_elevation 0x7f060289
int dimen mtrl_exposed_dropdown_menu_popup_vertical_offset 0x7f06028a
int dimen mtrl_exposed_dropdown_menu_popup_vertical_padding 0x7f06028b
int dimen mtrl_extended_fab_bottom_padding 0x7f06028c
int dimen mtrl_extended_fab_disabled_elevation 0x7f06028d
int dimen mtrl_extended_fab_disabled_translation_z 0x7f06028e
int dimen mtrl_extended_fab_elevation 0x7f06028f
int dimen mtrl_extended_fab_end_padding 0x7f060290
int dimen mtrl_extended_fab_end_padding_icon 0x7f060291
int dimen mtrl_extended_fab_icon_size 0x7f060292
int dimen mtrl_extended_fab_icon_text_spacing 0x7f060293
int dimen mtrl_extended_fab_min_height 0x7f060294
int dimen mtrl_extended_fab_min_width 0x7f060295
int dimen mtrl_extended_fab_start_padding 0x7f060296
int dimen mtrl_extended_fab_start_padding_icon 0x7f060297
int dimen mtrl_extended_fab_top_padding 0x7f060298
int dimen mtrl_extended_fab_translation_z_base 0x7f060299
int dimen mtrl_extended_fab_translation_z_hovered_focused 0x7f06029a
int dimen mtrl_extended_fab_translation_z_pressed 0x7f06029b
int dimen mtrl_fab_elevation 0x7f06029c
int dimen mtrl_fab_min_touch_target 0x7f06029d
int dimen mtrl_fab_translation_z_hovered_focused 0x7f06029e
int dimen mtrl_fab_translation_z_pressed 0x7f06029f
int dimen mtrl_high_ripple_default_alpha 0x7f0602a0
int dimen mtrl_high_ripple_focused_alpha 0x7f0602a1
int dimen mtrl_high_ripple_hovered_alpha 0x7f0602a2
int dimen mtrl_high_ripple_pressed_alpha 0x7f0602a3
int dimen mtrl_low_ripple_default_alpha 0x7f0602a4
int dimen mtrl_low_ripple_focused_alpha 0x7f0602a5
int dimen mtrl_low_ripple_hovered_alpha 0x7f0602a6
int dimen mtrl_low_ripple_pressed_alpha 0x7f0602a7
int dimen mtrl_min_touch_target_size 0x7f0602a8
int dimen mtrl_navigation_bar_item_default_icon_size 0x7f0602a9
int dimen mtrl_navigation_bar_item_default_margin 0x7f0602aa
int dimen mtrl_navigation_elevation 0x7f0602ab
int dimen mtrl_navigation_item_horizontal_padding 0x7f0602ac
int dimen mtrl_navigation_item_icon_padding 0x7f0602ad
int dimen mtrl_navigation_item_icon_size 0x7f0602ae
int dimen mtrl_navigation_item_shape_horizontal_margin 0x7f0602af
int dimen mtrl_navigation_item_shape_vertical_margin 0x7f0602b0
int dimen mtrl_navigation_rail_active_text_size 0x7f0602b1
int dimen mtrl_navigation_rail_compact_width 0x7f0602b2
int dimen mtrl_navigation_rail_default_width 0x7f0602b3
int dimen mtrl_navigation_rail_elevation 0x7f0602b4
int dimen mtrl_navigation_rail_icon_margin 0x7f0602b5
int dimen mtrl_navigation_rail_icon_size 0x7f0602b6
int dimen mtrl_navigation_rail_margin 0x7f0602b7
int dimen mtrl_navigation_rail_text_bottom_margin 0x7f0602b8
int dimen mtrl_navigation_rail_text_size 0x7f0602b9
int dimen mtrl_progress_circular_inset 0x7f0602ba
int dimen mtrl_progress_circular_inset_extra_small 0x7f0602bb
int dimen mtrl_progress_circular_inset_medium 0x7f0602bc
int dimen mtrl_progress_circular_inset_small 0x7f0602bd
int dimen mtrl_progress_circular_radius 0x7f0602be
int dimen mtrl_progress_circular_size 0x7f0602bf
int dimen mtrl_progress_circular_size_extra_small 0x7f0602c0
int dimen mtrl_progress_circular_size_medium 0x7f0602c1
int dimen mtrl_progress_circular_size_small 0x7f0602c2
int dimen mtrl_progress_circular_track_thickness_extra_small 0x7f0602c3
int dimen mtrl_progress_circular_track_thickness_medium 0x7f0602c4
int dimen mtrl_progress_circular_track_thickness_small 0x7f0602c5
int dimen mtrl_progress_indicator_full_rounded_corner_radius 0x7f0602c6
int dimen mtrl_progress_track_thickness 0x7f0602c7
int dimen mtrl_shape_corner_size_large_component 0x7f0602c8
int dimen mtrl_shape_corner_size_medium_component 0x7f0602c9
int dimen mtrl_shape_corner_size_small_component 0x7f0602ca
int dimen mtrl_slider_halo_radius 0x7f0602cb
int dimen mtrl_slider_label_padding 0x7f0602cc
int dimen mtrl_slider_label_radius 0x7f0602cd
int dimen mtrl_slider_label_square_side 0x7f0602ce
int dimen mtrl_slider_thumb_elevation 0x7f0602cf
int dimen mtrl_slider_thumb_radius 0x7f0602d0
int dimen mtrl_slider_tick_radius 0x7f0602d1
int dimen mtrl_slider_track_height 0x7f0602d2
int dimen mtrl_slider_track_side_padding 0x7f0602d3
int dimen mtrl_slider_widget_height 0x7f0602d4
int dimen mtrl_snackbar_action_text_color_alpha 0x7f0602d5
int dimen mtrl_snackbar_background_corner_radius 0x7f0602d6
int dimen mtrl_snackbar_background_overlay_color_alpha 0x7f0602d7
int dimen mtrl_snackbar_margin 0x7f0602d8
int dimen mtrl_snackbar_message_margin_horizontal 0x7f0602d9
int dimen mtrl_snackbar_padding_horizontal 0x7f0602da
int dimen mtrl_switch_text_padding 0x7f0602db
int dimen mtrl_switch_thumb_elevation 0x7f0602dc
int dimen mtrl_switch_thumb_size 0x7f0602dd
int dimen mtrl_switch_track_height 0x7f0602de
int dimen mtrl_switch_track_width 0x7f0602df
int dimen mtrl_textinput_box_corner_radius_medium 0x7f0602e0
int dimen mtrl_textinput_box_corner_radius_small 0x7f0602e1
int dimen mtrl_textinput_box_label_cutout_padding 0x7f0602e2
int dimen mtrl_textinput_box_stroke_width_default 0x7f0602e3
int dimen mtrl_textinput_box_stroke_width_focused 0x7f0602e4
int dimen mtrl_textinput_counter_margin_start 0x7f0602e5
int dimen mtrl_textinput_end_icon_margin_start 0x7f0602e6
int dimen mtrl_textinput_outline_box_expanded_padding 0x7f0602e7
int dimen mtrl_textinput_start_icon_margin_end 0x7f0602e8
int dimen mtrl_toolbar_default_height 0x7f0602e9
int dimen mtrl_tooltip_arrowSize 0x7f0602ea
int dimen mtrl_tooltip_cornerSize 0x7f0602eb
int dimen mtrl_tooltip_minHeight 0x7f0602ec
int dimen mtrl_tooltip_minWidth 0x7f0602ed
int dimen mtrl_tooltip_padding 0x7f0602ee
int dimen mtrl_transition_shared_axis_slide_distance 0x7f0602ef
int dimen notification_action_icon_size 0x7f0602f0
int dimen notification_action_text_size 0x7f0602f1
int dimen notification_big_circle_margin 0x7f0602f2
int dimen notification_content_margin_start 0x7f0602f3
int dimen notification_large_icon_height 0x7f0602f4
int dimen notification_large_icon_width 0x7f0602f5
int dimen notification_main_column_padding_top 0x7f0602f6
int dimen notification_media_narrow_margin 0x7f0602f7
int dimen notification_right_icon_size 0x7f0602f8
int dimen notification_right_side_padding_top 0x7f0602f9
int dimen notification_small_icon_background_padding 0x7f0602fa
int dimen notification_small_icon_size_as_large 0x7f0602fb
int dimen notification_subtext_size 0x7f0602fc
int dimen notification_top_pad 0x7f0602fd
int dimen notification_top_pad_large_text 0x7f0602fe
int dimen subtitle_corner_radius 0x7f0602ff
int dimen subtitle_outline_width 0x7f060300
int dimen subtitle_shadow_offset 0x7f060301
int dimen subtitle_shadow_radius 0x7f060302
int dimen tooltip_corner_radius 0x7f060303
int dimen tooltip_horizontal_padding 0x7f060304
int dimen tooltip_margin 0x7f060305
int dimen tooltip_precise_anchor_extra_offset 0x7f060306
int dimen tooltip_precise_anchor_threshold 0x7f060307
int dimen tooltip_vertical_padding 0x7f060308
int dimen tooltip_y_offset_non_touch 0x7f060309
int dimen tooltip_y_offset_touch 0x7f06030a
int drawable abc_ab_share_pack_mtrl_alpha 0x7f070029
int drawable abc_action_bar_item_background_material 0x7f07002a
int drawable abc_btn_borderless_material 0x7f07002b
int drawable abc_btn_check_material 0x7f07002c
int drawable abc_btn_check_material_anim 0x7f07002d
int drawable abc_btn_check_to_on_mtrl_000 0x7f07002e
int drawable abc_btn_check_to_on_mtrl_015 0x7f07002f
int drawable abc_btn_colored_material 0x7f070030
int drawable abc_btn_default_mtrl_shape 0x7f070031
int drawable abc_btn_radio_material 0x7f070032
int drawable abc_btn_radio_material_anim 0x7f070033
int drawable abc_btn_radio_to_on_mtrl_000 0x7f070034
int drawable abc_btn_radio_to_on_mtrl_015 0x7f070035
int drawable abc_btn_switch_to_on_mtrl_00001 0x7f070036
int drawable abc_btn_switch_to_on_mtrl_00012 0x7f070037
int drawable abc_cab_background_internal_bg 0x7f070038
int drawable abc_cab_background_top_material 0x7f070039
int drawable abc_cab_background_top_mtrl_alpha 0x7f07003a
int drawable abc_control_background_material 0x7f07003b
int drawable abc_dialog_material_background 0x7f07003c
int drawable abc_edit_text_material 0x7f07003d
int drawable abc_ic_ab_back_material 0x7f07003e
int drawable abc_ic_arrow_drop_right_black_24dp 0x7f07003f
int drawable abc_ic_clear_material 0x7f070040
int drawable abc_ic_commit_search_api_mtrl_alpha 0x7f070041
int drawable abc_ic_go_search_api_material 0x7f070042
int drawable abc_ic_menu_copy_mtrl_am_alpha 0x7f070043
int drawable abc_ic_menu_cut_mtrl_alpha 0x7f070044
int drawable abc_ic_menu_overflow_material 0x7f070045
int drawable abc_ic_menu_paste_mtrl_am_alpha 0x7f070046
int drawable abc_ic_menu_selectall_mtrl_alpha 0x7f070047
int drawable abc_ic_menu_share_mtrl_alpha 0x7f070048
int drawable abc_ic_search_api_material 0x7f070049
int drawable abc_ic_voice_search_api_material 0x7f07004a
int drawable abc_item_background_holo_dark 0x7f07004b
int drawable abc_item_background_holo_light 0x7f07004c
int drawable abc_list_divider_material 0x7f07004d
int drawable abc_list_divider_mtrl_alpha 0x7f07004e
int drawable abc_list_focused_holo 0x7f07004f
int drawable abc_list_longpressed_holo 0x7f070050
int drawable abc_list_pressed_holo_dark 0x7f070051
int drawable abc_list_pressed_holo_light 0x7f070052
int drawable abc_list_selector_background_transition_holo_dark 0x7f070053
int drawable abc_list_selector_background_transition_holo_light 0x7f070054
int drawable abc_list_selector_disabled_holo_dark 0x7f070055
int drawable abc_list_selector_disabled_holo_light 0x7f070056
int drawable abc_list_selector_holo_dark 0x7f070057
int drawable abc_list_selector_holo_light 0x7f070058
int drawable abc_menu_hardkey_panel_mtrl_mult 0x7f070059
int drawable abc_popup_background_mtrl_mult 0x7f07005a
int drawable abc_ratingbar_indicator_material 0x7f07005b
int drawable abc_ratingbar_material 0x7f07005c
int drawable abc_ratingbar_small_material 0x7f07005d
int drawable abc_scrubber_control_off_mtrl_alpha 0x7f07005e
int drawable abc_scrubber_control_to_pressed_mtrl_000 0x7f07005f
int drawable abc_scrubber_control_to_pressed_mtrl_005 0x7f070060
int drawable abc_scrubber_primary_mtrl_alpha 0x7f070061
int drawable abc_scrubber_track_mtrl_alpha 0x7f070062
int drawable abc_seekbar_thumb_material 0x7f070063
int drawable abc_seekbar_tick_mark_material 0x7f070064
int drawable abc_seekbar_track_material 0x7f070065
int drawable abc_spinner_mtrl_am_alpha 0x7f070066
int drawable abc_spinner_textfield_background_material 0x7f070067
int drawable abc_star_black_48dp 0x7f070068
int drawable abc_star_half_black_48dp 0x7f070069
int drawable abc_switch_thumb_material 0x7f07006a
int drawable abc_switch_track_mtrl_alpha 0x7f07006b
int drawable abc_tab_indicator_material 0x7f07006c
int drawable abc_tab_indicator_mtrl_alpha 0x7f07006d
int drawable abc_text_cursor_material 0x7f07006e
int drawable abc_text_select_handle_left_mtrl 0x7f07006f
int drawable abc_text_select_handle_middle_mtrl 0x7f070070
int drawable abc_text_select_handle_right_mtrl 0x7f070071
int drawable abc_textfield_activated_mtrl_alpha 0x7f070072
int drawable abc_textfield_default_mtrl_alpha 0x7f070073
int drawable abc_textfield_search_activated_mtrl_alpha 0x7f070074
int drawable abc_textfield_search_default_mtrl_alpha 0x7f070075
int drawable abc_textfield_search_material 0x7f070076
int drawable abc_vector_test 0x7f070077
int drawable avd_hide_password 0x7f070078
int drawable avd_show_password 0x7f070079
int drawable background1 0x7f07007a
int drawable background2 0x7f07007b
int drawable btn_checkbox_checked_mtrl 0x7f07007c
int drawable btn_checkbox_checked_to_unchecked_mtrl_animation 0x7f07007d
int drawable btn_checkbox_unchecked_mtrl 0x7f07007e
int drawable btn_checkbox_unchecked_to_checked_mtrl_animation 0x7f07007f
int drawable btn_radio_off_mtrl 0x7f070080
int drawable btn_radio_off_to_on_mtrl_animation 0x7f070081
int drawable btn_radio_on_mtrl 0x7f070082
int drawable btn_radio_on_to_off_mtrl_animation 0x7f070083
int drawable button_background 0x7f070084
int drawable button_background_black 0x7f070085
int drawable button_background_gray 0x7f070086
int drawable button_background_secondary 0x7f070087
int drawable button_black_frame 0x7f070088
int drawable design_fab_background 0x7f070089
int drawable design_ic_visibility 0x7f07008a
int drawable design_ic_visibility_off 0x7f07008b
int drawable design_password_eye 0x7f07008c
int drawable design_snackbar_background 0x7f07008d
int drawable dialog_background 0x7f07008e
int drawable game_overlay 0x7f07008f
int drawable ic_arrow_back_black_24 0x7f070090
int drawable ic_clear_black_24 0x7f070091
int drawable ic_clock_black_24dp 0x7f070092
int drawable ic_keyboard_black_24dp 0x7f070093
int drawable ic_launcher_background 0x7f070094
int drawable ic_launcher_foreground 0x7f070095
int drawable ic_m3_chip_check 0x7f070096
int drawable ic_m3_chip_checked_circle 0x7f070097
int drawable ic_m3_chip_close 0x7f070098
int drawable ic_mtrl_checked_circle 0x7f070099
int drawable ic_mtrl_chip_checked_black 0x7f07009a
int drawable ic_mtrl_chip_checked_circle 0x7f07009b
int drawable ic_mtrl_chip_close_circle 0x7f07009c
int drawable ic_search_black_24 0x7f07009d
int drawable m3_appbar_background 0x7f07009e
int drawable m3_avd_hide_password 0x7f07009f
int drawable m3_avd_show_password 0x7f0700a0
int drawable m3_password_eye 0x7f0700a1
int drawable m3_popupmenu_background_overlay 0x7f0700a2
int drawable m3_radiobutton_ripple 0x7f0700a3
int drawable m3_selection_control_ripple 0x7f0700a4
int drawable m3_tabs_background 0x7f0700a5
int drawable m3_tabs_line_indicator 0x7f0700a6
int drawable m3_tabs_rounded_line_indicator 0x7f0700a7
int drawable m3_tabs_transparent_background 0x7f0700a8
int drawable material_cursor_drawable 0x7f0700a9
int drawable material_ic_calendar_black_24dp 0x7f0700aa
int drawable material_ic_clear_black_24dp 0x7f0700ab
int drawable material_ic_edit_black_24dp 0x7f0700ac
int drawable material_ic_keyboard_arrow_left_black_24dp 0x7f0700ad
int drawable material_ic_keyboard_arrow_next_black_24dp 0x7f0700ae
int drawable material_ic_keyboard_arrow_previous_black_24dp 0x7f0700af
int drawable material_ic_keyboard_arrow_right_black_24dp 0x7f0700b0
int drawable material_ic_menu_arrow_down_black_24dp 0x7f0700b1
int drawable material_ic_menu_arrow_up_black_24dp 0x7f0700b2
int drawable mtrl_bottomsheet_drag_handle 0x7f0700b3
int drawable mtrl_checkbox_button 0x7f0700b4
int drawable mtrl_checkbox_button_checked_unchecked 0x7f0700b5
int drawable mtrl_checkbox_button_icon 0x7f0700b6
int drawable mtrl_checkbox_button_icon_checked_indeterminate 0x7f0700b7
int drawable mtrl_checkbox_button_icon_checked_unchecked 0x7f0700b8
int drawable mtrl_checkbox_button_icon_indeterminate_checked 0x7f0700b9
int drawable mtrl_checkbox_button_icon_indeterminate_unchecked 0x7f0700ba
int drawable mtrl_checkbox_button_icon_unchecked_checked 0x7f0700bb
int drawable mtrl_checkbox_button_icon_unchecked_indeterminate 0x7f0700bc
int drawable mtrl_checkbox_button_unchecked_checked 0x7f0700bd
int drawable mtrl_dialog_background 0x7f0700be
int drawable mtrl_dropdown_arrow 0x7f0700bf
int drawable mtrl_ic_arrow_drop_down 0x7f0700c0
int drawable mtrl_ic_arrow_drop_up 0x7f0700c1
int drawable mtrl_ic_cancel 0x7f0700c2
int drawable mtrl_ic_check_mark 0x7f0700c3
int drawable mtrl_ic_checkbox_checked 0x7f0700c4
int drawable mtrl_ic_checkbox_unchecked 0x7f0700c5
int drawable mtrl_ic_error 0x7f0700c6
int drawable mtrl_ic_indeterminate 0x7f0700c7
int drawable mtrl_navigation_bar_item_background 0x7f0700c8
int drawable mtrl_popupmenu_background 0x7f0700c9
int drawable mtrl_popupmenu_background_overlay 0x7f0700ca
int drawable mtrl_switch_thumb 0x7f0700cb
int drawable mtrl_switch_thumb_checked 0x7f0700cc
int drawable mtrl_switch_thumb_checked_pressed 0x7f0700cd
int drawable mtrl_switch_thumb_checked_unchecked 0x7f0700ce
int drawable mtrl_switch_thumb_pressed 0x7f0700cf
int drawable mtrl_switch_thumb_pressed_checked 0x7f0700d0
int drawable mtrl_switch_thumb_pressed_unchecked 0x7f0700d1
int drawable mtrl_switch_thumb_unchecked 0x7f0700d2
int drawable mtrl_switch_thumb_unchecked_checked 0x7f0700d3
int drawable mtrl_switch_thumb_unchecked_pressed 0x7f0700d4
int drawable mtrl_switch_track 0x7f0700d5
int drawable mtrl_switch_track_decoration 0x7f0700d6
int drawable mtrl_tabs_default_indicator 0x7f0700d7
int drawable navigation_empty_icon 0x7f0700d8
int drawable notification_action_background 0x7f0700d9
int drawable notification_bg 0x7f0700da
int drawable notification_bg_low 0x7f0700db
int drawable notification_bg_low_normal 0x7f0700dc
int drawable notification_bg_low_pressed 0x7f0700dd
int drawable notification_bg_normal 0x7f0700de
int drawable notification_bg_normal_pressed 0x7f0700df
int drawable notification_icon_background 0x7f0700e0
int drawable notification_template_icon_bg 0x7f0700e1
int drawable notification_template_icon_low_bg 0x7f0700e2
int drawable notification_tile_bg 0x7f0700e3
int drawable notify_panel_notification_icon_bg 0x7f0700e4
int drawable oset_rounded_button 0x7f0700e5
int drawable start_overlay 0x7f0700e6
int drawable test_level_drawable 0x7f0700e7
int drawable title 0x7f0700e8
int drawable title_en 0x7f0700e9
int drawable tooltip_frame_dark 0x7f0700ea
int drawable tooltip_frame_light 0x7f0700eb
int id ALT 0x7f080000
int id BOTTOM_END 0x7f080001
int id BOTTOM_START 0x7f080002
int id CTRL 0x7f080003
int id FUNCTION 0x7f080004
int id META 0x7f080005
int id NO_DEBUG 0x7f080006
int id SHIFT 0x7f080007
int id SHOW_ALL 0x7f080008
int id SHOW_PATH 0x7f080009
int id SHOW_PROGRESS 0x7f08000a
int id SYM 0x7f08000b
int id TOP_END 0x7f08000c
int id TOP_START 0x7f08000d
int id accelerate 0x7f08000e
int id accessibility_action_clickable_span 0x7f08000f
int id accessibility_custom_action_0 0x7f080010
int id accessibility_custom_action_1 0x7f080011
int id accessibility_custom_action_10 0x7f080012
int id accessibility_custom_action_11 0x7f080013
int id accessibility_custom_action_12 0x7f080014
int id accessibility_custom_action_13 0x7f080015
int id accessibility_custom_action_14 0x7f080016
int id accessibility_custom_action_15 0x7f080017
int id accessibility_custom_action_16 0x7f080018
int id accessibility_custom_action_17 0x7f080019
int id accessibility_custom_action_18 0x7f08001a
int id accessibility_custom_action_19 0x7f08001b
int id accessibility_custom_action_2 0x7f08001c
int id accessibility_custom_action_20 0x7f08001d
int id accessibility_custom_action_21 0x7f08001e
int id accessibility_custom_action_22 0x7f08001f
int id accessibility_custom_action_23 0x7f080020
int id accessibility_custom_action_24 0x7f080021
int id accessibility_custom_action_25 0x7f080022
int id accessibility_custom_action_26 0x7f080023
int id accessibility_custom_action_27 0x7f080024
int id accessibility_custom_action_28 0x7f080025
int id accessibility_custom_action_29 0x7f080026
int id accessibility_custom_action_3 0x7f080027
int id accessibility_custom_action_30 0x7f080028
int id accessibility_custom_action_31 0x7f080029
int id accessibility_custom_action_4 0x7f08002a
int id accessibility_custom_action_5 0x7f08002b
int id accessibility_custom_action_6 0x7f08002c
int id accessibility_custom_action_7 0x7f08002d
int id accessibility_custom_action_8 0x7f08002e
int id accessibility_custom_action_9 0x7f08002f
int id action0 0x7f080030
int id actionDown 0x7f080031
int id actionDownUp 0x7f080032
int id actionUp 0x7f080033
int id action_bar 0x7f080034
int id action_bar_activity_content 0x7f080035
int id action_bar_container 0x7f080036
int id action_bar_root 0x7f080037
int id action_bar_spinner 0x7f080038
int id action_bar_subtitle 0x7f080039
int id action_bar_title 0x7f08003a
int id action_container 0x7f08003b
int id action_context_bar 0x7f08003c
int id action_divider 0x7f08003d
int id action_image 0x7f08003e
int id action_menu_divider 0x7f08003f
int id action_menu_presenter 0x7f080040
int id action_mode_bar 0x7f080041
int id action_mode_bar_stub 0x7f080042
int id action_mode_close_button 0x7f080043
int id action_text 0x7f080044
int id actions 0x7f080045
int id activity_chooser_view_content 0x7f080046
int id ad_options_view 0x7f080047
int id ad_options_view2 0x7f080048
int id add 0x7f080049
int id advertiser_textView 0x7f08004a
int id agreeButton 0x7f08004b
int id alertTitle 0x7f08004c
int id aligned 0x7f08004d
int id all 0x7f08004e
int id allStates 0x7f08004f
int id always 0x7f080050
int id animateToEnd 0x7f080051
int id animateToStart 0x7f080052
int id antiClockwise 0x7f080053
int id anticipate 0x7f080054
int id arc 0x7f080055
int id asConfigured 0x7f080056
int id async 0x7f080057
int id auto 0x7f080058
int id autoComplete 0x7f080059
int id autoCompleteToEnd 0x7f08005a
int id autoCompleteToStart 0x7f08005b
int id barrier 0x7f08005c
int id baseline 0x7f08005d
int id beginOnFirstDraw 0x7f08005e
int id beginning 0x7f08005f
int id bestChoice 0x7f080060
int id blocking 0x7f080061
int id body_text_view 0x7f080062
int id bottom 0x7f080063
int id bounce 0x7f080064
int id bounceBoth 0x7f080065
int id bounceEnd 0x7f080066
int id bounceStart 0x7f080067
int id buttonPanel 0x7f080068
int id cache_measures 0x7f080069
int id callMeasure 0x7f08006a
int id cancel_action 0x7f08006b
int id cancel_button 0x7f08006c
int id carryVelocity 0x7f08006d
int id center 0x7f08006e
int id centerCrop 0x7f08006f
int id centerInside 0x7f080070
int id center_horizontal 0x7f080071
int id center_vertical 0x7f080072
int id chain 0x7f080073
int id chain2 0x7f080074
int id chains 0x7f080075
int id checkbox 0x7f080076
int id checked 0x7f080077
int id choice1Button 0x7f080078
int id choice2Button 0x7f080079
int id choice3Button 0x7f08007a
int id choice4Button 0x7f08007b
int id choicesLayout 0x7f08007c
int id chronometer 0x7f08007d
int id circle_center 0x7f08007e
int id clear_text 0x7f08007f
int id clip_horizontal 0x7f080080
int id clip_vertical 0x7f080081
int id clockwise 0x7f080082
int id closest 0x7f080083
int id collapseActionView 0x7f080084
int id compress 0x7f080085
int id confirm_button 0x7f080086
int id constraint 0x7f080087
int id container 0x7f080088
int id content 0x7f080089
int id contentPanel 0x7f08008a
int id contiguous 0x7f08008b
int id continuousVelocity 0x7f08008c
int id coordinator 0x7f08008d
int id cos 0x7f08008e
int id counterclockwise 0x7f08008f
int id cradle 0x7f080090
int id cta_button 0x7f080091
int id currentState 0x7f080092
int id custom 0x7f080093
int id customPanel 0x7f080094
int id cut 0x7f080095
int id date_picker_actions 0x7f080096
int id decelerate 0x7f080097
int id decelerateAndComplete 0x7f080098
int id decor_content_parent 0x7f080099
int id default_activity_button 0x7f08009a
int id deltaRelative 0x7f08009b
int id dependency_ordering 0x7f08009c
int id design_bottom_sheet 0x7f08009d
int id design_menu_item_action_area 0x7f08009e
int id design_menu_item_action_area_stub 0x7f08009f
int id design_menu_item_text 0x7f0800a0
int id design_navigation_view 0x7f0800a1
int id dialog_button 0x7f0800a2
int id dimensions 0x7f0800a3
int id direct 0x7f0800a4
int id disableHome 0x7f0800a5
int id disableIntraAutoTransition 0x7f0800a6
int id disablePostScroll 0x7f0800a7
int id disableScroll 0x7f0800a8
int id disagreeButton 0x7f0800a9
int id disjoint 0x7f0800aa
int id dragAnticlockwise 0x7f0800ab
int id dragClockwise 0x7f0800ac
int id dragDown 0x7f0800ad
int id dragEnd 0x7f0800ae
int id dragLeft 0x7f0800af
int id dragRight 0x7f0800b0
int id dragStart 0x7f0800b1
int id dragUp 0x7f0800b2
int id dropdown_menu 0x7f0800b3
int id easeIn 0x7f0800b4
int id easeInOut 0x7f0800b5
int id easeOut 0x7f0800b6
int id east 0x7f0800b7
int id edge 0x7f0800b8
int id edit_query 0x7f0800b9
int id elastic 0x7f0800ba
int id embed 0x7f0800bb
int id end 0x7f0800bc
int id endToStart 0x7f0800bd
int id end_padder 0x7f0800be
int id enterAlways 0x7f0800bf
int id enterAlwaysCollapsed 0x7f0800c0
int id exitGameButton 0x7f0800c1
int id exitUntilCollapsed 0x7f0800c2
int id expand_activities_button 0x7f0800c3
int id expanded_menu 0x7f0800c4
int id fade 0x7f0800c5
int id fill 0x7f0800c6
int id fill_horizontal 0x7f0800c7
int id fill_vertical 0x7f0800c8
int id filled 0x7f0800c9
int id firewoodPlusButton 0x7f0800ca
int id firewoodTextView 0x7f0800cb
int id fitCenter 0x7f0800cc
int id fitEnd 0x7f0800cd
int id fitStart 0x7f0800ce
int id fitToContents 0x7f0800cf
int id fitXY 0x7f0800d0
int id fixed 0x7f0800d1
int id flip 0x7f0800d2
int id floating 0x7f0800d3
int id foodPlusButton 0x7f0800d4
int id foodTextView 0x7f0800d5
int id forever 0x7f0800d6
int id fragment_container_view_tag 0x7f0800d7
int id frost 0x7f0800d8
int id fullscreen_header 0x7f0800d9
int id ghost_view 0x7f0800da
int id ghost_view_holder 0x7f0800db
int id glide_custom_view_target_tag 0x7f0800dc
int id gone 0x7f0800dd
int id graph 0x7f0800de
int id graph_wrap 0x7f0800df
int id group_divider 0x7f0800e0
int id grouping 0x7f0800e1
int id groups 0x7f0800e2
int id header_title 0x7f0800e3
int id hideable 0x7f0800e4
int id home 0x7f0800e5
int id homeAsUp 0x7f0800e6
int id honorRequest 0x7f0800e7
int id horizontal_only 0x7f0800e8
int id icon 0x7f0800e9
int id icon_group 0x7f0800ea
int id icon_image_view 0x7f0800eb
int id ifRoom 0x7f0800ec
int id ignore 0x7f0800ed
int id ignoreRequest 0x7f0800ee
int id image 0x7f0800ef
int id immediateStop 0x7f0800f0
int id included 0x7f0800f1
int id indeterminate 0x7f0800f2
int id info 0x7f0800f3
int id invisible 0x7f0800f4
int id inward 0x7f0800f5
int id is_pooling_container_tag 0x7f0800f6
int id italic 0x7f0800f7
int id item_touch_helper_previous_elevation 0x7f0800f8
int id jumpToEnd 0x7f0800f9
int id jumpToStart 0x7f0800fa
int id labeled 0x7f0800fb
int id languageButton 0x7f0800fc
int id layout 0x7f0800fd
int id left 0x7f0800fe
int id leftToRight 0x7f0800ff
int id legacy 0x7f080100
int id line1 0x7f080101
int id line3 0x7f080102
int id linear 0x7f080103
int id listMode 0x7f080104
int id list_item 0x7f080105
int id m3_side_sheet 0x7f080106
int id marquee 0x7f080107
int id masked 0x7f080108
int id match_constraint 0x7f080109
int id match_parent 0x7f08010a
int id material_clock_display 0x7f08010b
int id material_clock_display_and_toggle 0x7f08010c
int id material_clock_face 0x7f08010d
int id material_clock_hand 0x7f08010e
int id material_clock_level 0x7f08010f
int id material_clock_period_am_button 0x7f080110
int id material_clock_period_pm_button 0x7f080111
int id material_clock_period_toggle 0x7f080112
int id material_hour_text_input 0x7f080113
int id material_hour_tv 0x7f080114
int id material_label 0x7f080115
int id material_minute_text_input 0x7f080116
int id material_minute_tv 0x7f080117
int id material_textinput_timepicker 0x7f080118
int id material_timepicker_cancel_button 0x7f080119
int id material_timepicker_container 0x7f08011a
int id material_timepicker_mode_button 0x7f08011b
int id material_timepicker_ok_button 0x7f08011c
int id material_timepicker_view 0x7f08011d
int id material_value_index 0x7f08011e
int id matrix 0x7f08011f
int id media_actions 0x7f080120
int id media_view_container 0x7f080121
int id message 0x7f080122
int id middle 0x7f080123
int id mini 0x7f080124
int id month_grid 0x7f080125
int id month_navigation_bar 0x7f080126
int id month_navigation_fragment_toggle 0x7f080127
int id month_navigation_next 0x7f080128
int id month_navigation_previous 0x7f080129
int id month_title 0x7f08012a
int id motion_base 0x7f08012b
int id mtrl_anchor_parent 0x7f08012c
int id mtrl_calendar_day_selector_frame 0x7f08012d
int id mtrl_calendar_days_of_week 0x7f08012e
int id mtrl_calendar_frame 0x7f08012f
int id mtrl_calendar_main_pane 0x7f080130
int id mtrl_calendar_months 0x7f080131
int id mtrl_calendar_selection_frame 0x7f080132
int id mtrl_calendar_text_input_frame 0x7f080133
int id mtrl_calendar_year_selector_frame 0x7f080134
int id mtrl_card_checked_layer_id 0x7f080135
int id mtrl_child_content_container 0x7f080136
int id mtrl_internal_children_alpha_tag 0x7f080137
int id mtrl_motion_snapshot_view 0x7f080138
int id mtrl_picker_fullscreen 0x7f080139
int id mtrl_picker_header 0x7f08013a
int id mtrl_picker_header_selection_text 0x7f08013b
int id mtrl_picker_header_title_and_selection 0x7f08013c
int id mtrl_picker_header_toggle 0x7f08013d
int id mtrl_picker_text_input_date 0x7f08013e
int id mtrl_picker_text_input_range_end 0x7f08013f
int id mtrl_picker_text_input_range_start 0x7f080140
int id mtrl_picker_title_text 0x7f080141
int id mtrl_view_tag_bottom_padding 0x7f080142
int id multiply 0x7f080143
int id musicToggleButton 0x7f080144
int id navigation_bar_item_active_indicator_view 0x7f080145
int id navigation_bar_item_icon_container 0x7f080146
int id navigation_bar_item_icon_view 0x7f080147
int id navigation_bar_item_labels_group 0x7f080148
int id navigation_bar_item_large_label_view 0x7f080149
int id navigation_bar_item_small_label_view 0x7f08014a
int id navigation_header_container 0x7f08014b
int id never 0x7f08014c
int id neverCompleteToEnd 0x7f08014d
int id neverCompleteToStart 0x7f08014e
int id noScroll 0x7f08014f
int id noState 0x7f080150
int id none 0x7f080151
int id normal 0x7f080152
int id north 0x7f080153
int id notification_background 0x7f080154
int id notification_main_column 0x7f080155
int id notification_main_column_container 0x7f080156
int id off 0x7f080157
int id on 0x7f080158
int id onInterceptTouchReturnSwipe 0x7f080159
int id outline 0x7f08015a
int id outward 0x7f08015b
int id overshoot 0x7f08015c
int id packed 0x7f08015d
int id parallax 0x7f08015e
int id parent 0x7f08015f
int id parentPanel 0x7f080160
int id parentRelative 0x7f080161
int id parent_matrix 0x7f080162
int id password_toggle 0x7f080163
int id path 0x7f080164
int id pathRelative 0x7f080165
int id peekHeight 0x7f080166
int id percent 0x7f080167
int id pin 0x7f080168
int id pooling_container_listener_holder_tag 0x7f080169
int id position 0x7f08016a
int id postLayout 0x7f08016b
int id pressed 0x7f08016c
int id privacyContentTextView 0x7f08016d
int id progress_circular 0x7f08016e
int id progress_horizontal 0x7f08016f
int id radio 0x7f080170
int id ratio 0x7f080171
int id rectangles 0x7f080172
int id reverseSawtooth 0x7f080173
int id right 0x7f080174
int id rightToLeft 0x7f080175
int id right_icon 0x7f080176
int id right_side 0x7f080177
int id rl_bottom 0x7f080178
int id rounded 0x7f080179
int id row_index_key 0x7f08017a
int id save_non_transition_alpha 0x7f08017b
int id save_overlay_view 0x7f08017c
int id sawtooth 0x7f08017d
int id scale 0x7f08017e
int id screen 0x7f08017f
int id scroll 0x7f080180
int id scrollIndicatorDown 0x7f080181
int id scrollIndicatorUp 0x7f080182
int id scrollView 0x7f080183
int id scrollable 0x7f080184
int id search_badge 0x7f080185
int id search_bar 0x7f080186
int id search_bar_text_view 0x7f080187
int id search_button 0x7f080188
int id search_close_btn 0x7f080189
int id search_edit_frame 0x7f08018a
int id search_go_btn 0x7f08018b
int id search_mag_icon 0x7f08018c
int id search_plate 0x7f08018d
int id search_src_text 0x7f08018e
int id search_view_background 0x7f08018f
int id search_view_clear_button 0x7f080190
int id search_view_content_container 0x7f080191
int id search_view_divider 0x7f080192
int id search_view_dummy_toolbar 0x7f080193
int id search_view_edit_text 0x7f080194
int id search_view_header_container 0x7f080195
int id search_view_root 0x7f080196
int id search_view_scrim 0x7f080197
int id search_view_search_prefix 0x7f080198
int id search_view_status_bar_spacer 0x7f080199
int id search_view_toolbar 0x7f08019a
int id search_view_toolbar_container 0x7f08019b
int id search_voice_btn 0x7f08019c
int id select_dialog_listview 0x7f08019d
int id selected 0x7f08019e
int id selection_type 0x7f08019f
int id sharedValueSet 0x7f0801a0
int id sharedValueUnset 0x7f0801a1
int id shortcut 0x7f0801a2
int id showCustom 0x7f0801a3
int id showHome 0x7f0801a4
int id showTitle 0x7f0801a5
int id sin 0x7f0801a6
int id skipCollapsed 0x7f0801a7
int id skipped 0x7f0801a8
int id slide 0x7f0801a9
int id snackbar_action 0x7f0801aa
int id snackbar_text 0x7f0801ab
int id snap 0x7f0801ac
int id snapMargins 0x7f0801ad
int id south 0x7f0801ae
int id spacer 0x7f0801af
int id special_effects_controller_view_tag 0x7f0801b0
int id spline 0x7f0801b1
int id split_action_bar 0x7f0801b2
int id spread 0x7f0801b3
int id spread_inside 0x7f0801b4
int id spring 0x7f0801b5
int id square 0x7f0801b6
int id src_atop 0x7f0801b7
int id src_in 0x7f0801b8
int id src_over 0x7f0801b9
int id staminaPlusButton 0x7f0801ba
int id staminaTextView 0x7f0801bb
int id standard 0x7f0801bc
int id star_rating_view 0x7f0801bd
int id start 0x7f0801be
int id startGameButton 0x7f0801bf
int id startHorizontal 0x7f0801c0
int id startToEnd 0x7f0801c1
int id startVertical 0x7f0801c2
int id staticLayout 0x7f0801c3
int id staticPostLayout 0x7f0801c4
int id statusBarLayout 0x7f0801c5
int id status_bar_latest_event_content 0x7f0801c6
int id stop 0x7f0801c7
int id storyScrollView 0x7f0801c8
int id storyTextView 0x7f0801c9
int id stretch 0x7f0801ca
int id submenuarrow 0x7f0801cb
int id submit_area 0x7f0801cc
int id supportScrollUp 0x7f0801cd
int id tabMode 0x7f0801ce
int id tag_accessibility_actions 0x7f0801cf
int id tag_accessibility_clickable_spans 0x7f0801d0
int id tag_accessibility_heading 0x7f0801d1
int id tag_accessibility_pane_title 0x7f0801d2
int id tag_on_apply_window_listener 0x7f0801d3
int id tag_on_receive_content_listener 0x7f0801d4
int id tag_on_receive_content_mime_types 0x7f0801d5
int id tag_screen_reader_focusable 0x7f0801d6
int id tag_state_description 0x7f0801d7
int id tag_transition_group 0x7f0801d8
int id tag_unhandled_key_event_manager 0x7f0801d9
int id tag_unhandled_key_listeners 0x7f0801da
int id tag_window_insets_animation_callback 0x7f0801db
int id text 0x7f0801dc
int id text2 0x7f0801dd
int id textEnd 0x7f0801de
int id textSpacerNoButtons 0x7f0801df
int id textSpacerNoTitle 0x7f0801e0
int id textStart 0x7f0801e1
int id textTop 0x7f0801e2
int id text_input_end_icon 0x7f0801e3
int id text_input_error_icon 0x7f0801e4
int id text_input_start_icon 0x7f0801e5
int id textinput_counter 0x7f0801e6
int id textinput_error 0x7f0801e7
int id textinput_helper_text 0x7f0801e8
int id textinput_placeholder 0x7f0801e9
int id textinput_prefix_text 0x7f0801ea
int id textinput_suffix_text 0x7f0801eb
int id time 0x7f0801ec
int id title 0x7f0801ed
int id titleDividerNoCustom 0x7f0801ee
int id titleImageView 0x7f0801ef
int id title_template 0x7f0801f0
int id title_text_view 0x7f0801f1
int id toggle 0x7f0801f2
int id top 0x7f0801f3
int id topPanel 0x7f0801f4
int id touch_outside 0x7f0801f5
int id transitionToEnd 0x7f0801f6
int id transitionToStart 0x7f0801f7
int id transition_current_scene 0x7f0801f8
int id transition_layout_save 0x7f0801f9
int id transition_position 0x7f0801fa
int id transition_scene_layoutid_cache 0x7f0801fb
int id transition_transform 0x7f0801fc
int id triangle 0x7f0801fd
int id unchecked 0x7f0801fe
int id uniform 0x7f0801ff
int id unlabeled 0x7f080200
int id up 0x7f080201
int id useLogo 0x7f080202
int id vertical_only 0x7f080203
int id view_offset_helper 0x7f080204
int id view_transition 0x7f080205
int id view_tree_lifecycle_owner 0x7f080206
int id view_tree_on_back_pressed_dispatcher_owner 0x7f080207
int id view_tree_saved_state_registry_owner 0x7f080208
int id view_tree_view_model_store_owner 0x7f080209
int id visible 0x7f08020a
int id visible_removing_fragment_view_tag 0x7f08020b
int id warmthPlusButton 0x7f08020c
int id warmthTextView 0x7f08020d
int id west 0x7f08020e
int id withText 0x7f08020f
int id with_icon 0x7f080210
int id withinBounds 0x7f080211
int id wrap 0x7f080212
int id wrap_content 0x7f080213
int id wrap_content_constrained 0x7f080214
int id x_left 0x7f080215
int id x_right 0x7f080216
int integer abc_config_activityDefaultDur 0x7f090000
int integer abc_config_activityShortDur 0x7f090001
int integer app_bar_elevation_anim_duration 0x7f090002
int integer bottom_sheet_slide_duration 0x7f090003
int integer cancel_button_image_alpha 0x7f090004
int integer config_tooltipAnimTime 0x7f090005
int integer design_snackbar_text_max_lines 0x7f090006
int integer design_tab_indicator_anim_duration_ms 0x7f090007
int integer hide_password_duration 0x7f090008
int integer m3_btn_anim_delay_ms 0x7f090009
int integer m3_btn_anim_duration_ms 0x7f09000a
int integer m3_card_anim_delay_ms 0x7f09000b
int integer m3_card_anim_duration_ms 0x7f09000c
int integer m3_chip_anim_duration 0x7f09000d
int integer m3_sys_motion_duration_extra_long1 0x7f09000e
int integer m3_sys_motion_duration_extra_long2 0x7f09000f
int integer m3_sys_motion_duration_extra_long3 0x7f090010
int integer m3_sys_motion_duration_extra_long4 0x7f090011
int integer m3_sys_motion_duration_long1 0x7f090012
int integer m3_sys_motion_duration_long2 0x7f090013
int integer m3_sys_motion_duration_long3 0x7f090014
int integer m3_sys_motion_duration_long4 0x7f090015
int integer m3_sys_motion_duration_medium1 0x7f090016
int integer m3_sys_motion_duration_medium2 0x7f090017
int integer m3_sys_motion_duration_medium3 0x7f090018
int integer m3_sys_motion_duration_medium4 0x7f090019
int integer m3_sys_motion_duration_short1 0x7f09001a
int integer m3_sys_motion_duration_short2 0x7f09001b
int integer m3_sys_motion_duration_short3 0x7f09001c
int integer m3_sys_motion_duration_short4 0x7f09001d
int integer m3_sys_motion_path 0x7f09001e
int integer m3_sys_shape_corner_extra_large_corner_family 0x7f09001f
int integer m3_sys_shape_corner_extra_small_corner_family 0x7f090020
int integer m3_sys_shape_corner_full_corner_family 0x7f090021
int integer m3_sys_shape_corner_large_corner_family 0x7f090022
int integer m3_sys_shape_corner_medium_corner_family 0x7f090023
int integer m3_sys_shape_corner_small_corner_family 0x7f090024
int integer material_motion_duration_long_1 0x7f090025
int integer material_motion_duration_long_2 0x7f090026
int integer material_motion_duration_medium_1 0x7f090027
int integer material_motion_duration_medium_2 0x7f090028
int integer material_motion_duration_short_1 0x7f090029
int integer material_motion_duration_short_2 0x7f09002a
int integer material_motion_path 0x7f09002b
int integer mtrl_badge_max_character_count 0x7f09002c
int integer mtrl_btn_anim_delay_ms 0x7f09002d
int integer mtrl_btn_anim_duration_ms 0x7f09002e
int integer mtrl_calendar_header_orientation 0x7f09002f
int integer mtrl_calendar_selection_text_lines 0x7f090030
int integer mtrl_calendar_year_selector_span 0x7f090031
int integer mtrl_card_anim_delay_ms 0x7f090032
int integer mtrl_card_anim_duration_ms 0x7f090033
int integer mtrl_chip_anim_duration 0x7f090034
int integer mtrl_switch_thumb_motion_duration 0x7f090035
int integer mtrl_switch_thumb_post_morphing_duration 0x7f090036
int integer mtrl_switch_thumb_pre_morphing_duration 0x7f090037
int integer mtrl_switch_thumb_pressed_duration 0x7f090038
int integer mtrl_switch_thumb_viewport_center_coordinate 0x7f090039
int integer mtrl_switch_thumb_viewport_size 0x7f09003a
int integer mtrl_switch_track_viewport_height 0x7f09003b
int integer mtrl_switch_track_viewport_width 0x7f09003c
int integer mtrl_tab_indicator_anim_duration_ms 0x7f09003d
int integer mtrl_view_gone 0x7f09003e
int integer mtrl_view_invisible 0x7f09003f
int integer mtrl_view_visible 0x7f090040
int integer show_password_duration 0x7f090041
int integer status_bar_notification_info_maxnum 0x7f090042
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_0 0x7f0a0000
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_1 0x7f0a0001
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0 0x7f0a0002
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1 0x7f0a0003
int interpolator btn_radio_to_off_mtrl_animation_interpolator_0 0x7f0a0004
int interpolator btn_radio_to_on_mtrl_animation_interpolator_0 0x7f0a0005
int interpolator fast_out_slow_in 0x7f0a0006
int interpolator m3_sys_motion_easing_emphasized 0x7f0a0007
int interpolator m3_sys_motion_easing_emphasized_accelerate 0x7f0a0008
int interpolator m3_sys_motion_easing_emphasized_decelerate 0x7f0a0009
int interpolator m3_sys_motion_easing_linear 0x7f0a000a
int interpolator m3_sys_motion_easing_standard 0x7f0a000b
int interpolator m3_sys_motion_easing_standard_accelerate 0x7f0a000c
int interpolator m3_sys_motion_easing_standard_decelerate 0x7f0a000d
int interpolator mtrl_fast_out_linear_in 0x7f0a000e
int interpolator mtrl_fast_out_slow_in 0x7f0a000f
int interpolator mtrl_linear 0x7f0a0010
int interpolator mtrl_linear_out_slow_in 0x7f0a0011
int layout abc_action_bar_title_item 0x7f0b0000
int layout abc_action_bar_up_container 0x7f0b0001
int layout abc_action_menu_item_layout 0x7f0b0002
int layout abc_action_menu_layout 0x7f0b0003
int layout abc_action_mode_bar 0x7f0b0004
int layout abc_action_mode_close_item_material 0x7f0b0005
int layout abc_activity_chooser_view 0x7f0b0006
int layout abc_activity_chooser_view_list_item 0x7f0b0007
int layout abc_alert_dialog_button_bar_material 0x7f0b0008
int layout abc_alert_dialog_material 0x7f0b0009
int layout abc_alert_dialog_title_material 0x7f0b000a
int layout abc_cascading_menu_item_layout 0x7f0b000b
int layout abc_dialog_title_material 0x7f0b000c
int layout abc_expanded_menu_layout 0x7f0b000d
int layout abc_list_menu_item_checkbox 0x7f0b000e
int layout abc_list_menu_item_icon 0x7f0b000f
int layout abc_list_menu_item_layout 0x7f0b0010
int layout abc_list_menu_item_radio 0x7f0b0011
int layout abc_popup_menu_header_item_layout 0x7f0b0012
int layout abc_popup_menu_item_layout 0x7f0b0013
int layout abc_screen_content_include 0x7f0b0014
int layout abc_screen_simple 0x7f0b0015
int layout abc_screen_simple_overlay_action_mode 0x7f0b0016
int layout abc_screen_toolbar 0x7f0b0017
int layout abc_search_dropdown_item_icons_2line 0x7f0b0018
int layout abc_search_view 0x7f0b0019
int layout abc_select_dialog_material 0x7f0b001a
int layout abc_tooltip 0x7f0b001b
int layout activity_main 0x7f0b001c
int layout activity_privacy_policy 0x7f0b001d
int layout activity_start 0x7f0b001e
int layout custom_dialog 0x7f0b001f
int layout design_bottom_navigation_item 0x7f0b0020
int layout design_bottom_sheet_dialog 0x7f0b0021
int layout design_layout_snackbar 0x7f0b0022
int layout design_layout_snackbar_include 0x7f0b0023
int layout design_layout_tab_icon 0x7f0b0024
int layout design_layout_tab_text 0x7f0b0025
int layout design_menu_item_action_area 0x7f0b0026
int layout design_navigation_item 0x7f0b0027
int layout design_navigation_item_header 0x7f0b0028
int layout design_navigation_item_separator 0x7f0b0029
int layout design_navigation_item_subheader 0x7f0b002a
int layout design_navigation_menu 0x7f0b002b
int layout design_navigation_menu_item 0x7f0b002c
int layout design_text_input_end_icon 0x7f0b002d
int layout design_text_input_start_icon 0x7f0b002e
int layout m3_alert_dialog 0x7f0b002f
int layout m3_alert_dialog_actions 0x7f0b0030
int layout m3_alert_dialog_title 0x7f0b0031
int layout m3_auto_complete_simple_item 0x7f0b0032
int layout m3_side_sheet_dialog 0x7f0b0033
int layout material_chip_input_combo 0x7f0b0034
int layout material_clock_display 0x7f0b0035
int layout material_clock_display_divider 0x7f0b0036
int layout material_clock_period_toggle 0x7f0b0037
int layout material_clock_period_toggle_land 0x7f0b0038
int layout material_clockface_textview 0x7f0b0039
int layout material_clockface_view 0x7f0b003a
int layout material_radial_view_group 0x7f0b003b
int layout material_textinput_timepicker 0x7f0b003c
int layout material_time_chip 0x7f0b003d
int layout material_time_input 0x7f0b003e
int layout material_timepicker 0x7f0b003f
int layout material_timepicker_dialog 0x7f0b0040
int layout material_timepicker_textinput_display 0x7f0b0041
int layout mtrl_alert_dialog 0x7f0b0042
int layout mtrl_alert_dialog_actions 0x7f0b0043
int layout mtrl_alert_dialog_title 0x7f0b0044
int layout mtrl_alert_select_dialog_item 0x7f0b0045
int layout mtrl_alert_select_dialog_multichoice 0x7f0b0046
int layout mtrl_alert_select_dialog_singlechoice 0x7f0b0047
int layout mtrl_auto_complete_simple_item 0x7f0b0048
int layout mtrl_calendar_day 0x7f0b0049
int layout mtrl_calendar_day_of_week 0x7f0b004a
int layout mtrl_calendar_days_of_week 0x7f0b004b
int layout mtrl_calendar_horizontal 0x7f0b004c
int layout mtrl_calendar_month 0x7f0b004d
int layout mtrl_calendar_month_labeled 0x7f0b004e
int layout mtrl_calendar_month_navigation 0x7f0b004f
int layout mtrl_calendar_months 0x7f0b0050
int layout mtrl_calendar_vertical 0x7f0b0051
int layout mtrl_calendar_year 0x7f0b0052
int layout mtrl_layout_snackbar 0x7f0b0053
int layout mtrl_layout_snackbar_include 0x7f0b0054
int layout mtrl_navigation_rail_item 0x7f0b0055
int layout mtrl_picker_actions 0x7f0b0056
int layout mtrl_picker_dialog 0x7f0b0057
int layout mtrl_picker_fullscreen 0x7f0b0058
int layout mtrl_picker_header_dialog 0x7f0b0059
int layout mtrl_picker_header_fullscreen 0x7f0b005a
int layout mtrl_picker_header_selection_text 0x7f0b005b
int layout mtrl_picker_header_title_text 0x7f0b005c
int layout mtrl_picker_header_toggle 0x7f0b005d
int layout mtrl_picker_text_input_date 0x7f0b005e
int layout mtrl_picker_text_input_date_range 0x7f0b005f
int layout mtrl_search_bar 0x7f0b0060
int layout mtrl_search_view 0x7f0b0061
int layout notification_action 0x7f0b0062
int layout notification_action_tombstone 0x7f0b0063
int layout notification_media_action 0x7f0b0064
int layout notification_media_cancel_action 0x7f0b0065
int layout notification_template_big_media 0x7f0b0066
int layout notification_template_big_media_custom 0x7f0b0067
int layout notification_template_big_media_narrow 0x7f0b0068
int layout notification_template_big_media_narrow_custom 0x7f0b0069
int layout notification_template_custom_big 0x7f0b006a
int layout notification_template_icon_group 0x7f0b006b
int layout notification_template_lines_media 0x7f0b006c
int layout notification_template_media 0x7f0b006d
int layout notification_template_media_custom 0x7f0b006e
int layout notification_template_part_chronometer 0x7f0b006f
int layout notification_template_part_time 0x7f0b0070
int layout oset_native_custom_ad_view 0x7f0b0071
int layout select_dialog_item_material 0x7f0b0072
int layout select_dialog_multichoice_material 0x7f0b0073
int layout select_dialog_singlechoice_material 0x7f0b0074
int layout support_simple_spinner_dropdown_item 0x7f0b0075
int mipmap ic_launcher 0x7f0d0000
int mipmap ic_launcher_foreground 0x7f0d0001
int mipmap ic_launcher_round 0x7f0d0002
int plurals mtrl_badge_content_description 0x7f0e0000
int raw winter 0x7f0f0000
int string abc_action_bar_home_description 0x7f100000
int string abc_action_bar_up_description 0x7f100001
int string abc_action_menu_overflow_description 0x7f100002
int string abc_action_mode_done 0x7f100003
int string abc_activity_chooser_view_see_all 0x7f100004
int string abc_activitychooserview_choose_application 0x7f100005
int string abc_capital_off 0x7f100006
int string abc_capital_on 0x7f100007
int string abc_menu_alt_shortcut_label 0x7f100008
int string abc_menu_ctrl_shortcut_label 0x7f100009
int string abc_menu_delete_shortcut_label 0x7f10000a
int string abc_menu_enter_shortcut_label 0x7f10000b
int string abc_menu_function_shortcut_label 0x7f10000c
int string abc_menu_meta_shortcut_label 0x7f10000d
int string abc_menu_shift_shortcut_label 0x7f10000e
int string abc_menu_space_shortcut_label 0x7f10000f
int string abc_menu_sym_shortcut_label 0x7f100010
int string abc_prepend_shortcut_label 0x7f100011
int string abc_search_hint 0x7f100012
int string abc_searchview_description_clear 0x7f100013
int string abc_searchview_description_query 0x7f100014
int string abc_searchview_description_search 0x7f100015
int string abc_searchview_description_submit 0x7f100016
int string abc_searchview_description_voice 0x7f100017
int string abc_shareactionprovider_share_with 0x7f100018
int string abc_shareactionprovider_share_with_application 0x7f100019
int string abc_toolbar_collapse_description 0x7f10001a
int string ad_load_failed_message 0x7f10001b
int string ad_load_failed_title 0x7f10001c
int string ad_loading_message 0x7f10001d
int string ad_loading_title 0x7f10001e
int string ad_revive 0x7f10001f
int string ad_show_failed_message 0x7f100020
int string ad_show_failed_title 0x7f100021
int string androidx_startup 0x7f100022
int string app_name 0x7f100023
int string app_name_en 0x7f100024
int string appbar_scrolling_view_behavior 0x7f100025
int string bottom_sheet_behavior 0x7f100026
int string bottomsheet_action_collapse 0x7f100027
int string bottomsheet_action_expand 0x7f100028
int string bottomsheet_action_expand_halfway 0x7f100029
int string bottomsheet_drag_handle_clicked 0x7f10002a
int string bottomsheet_drag_handle_content_description 0x7f10002b
int string cabin_damaged 0x7f10002c
int string cabin_good_condition 0x7f10002d
int string cabin_improved 0x7f10002e
int string cabin_poor_condition 0x7f10002f
int string challenge_again 0x7f100030
int string character_counter_content_description 0x7f100031
int string character_counter_overflowed_content_description 0x7f100032
int string character_counter_pattern 0x7f100033
int string choice_effect_text 0x7f100034
int string choice_made 0x7f100035
int string clear_text_end_icon_content_description 0x7f100036
int string confirm 0x7f100037
int string continue_button 0x7f100038
int string continue_game 0x7f100039
int string defeat_despair_content 0x7f10003a
int string defeat_despair_stats 0x7f10003b
int string defeat_despair_title 0x7f10003c
int string defeat_exhaustion_content 0x7f10003d
int string defeat_exhaustion_title 0x7f10003e
int string defeat_generic_content 0x7f10003f
int string defeat_generic_title 0x7f100040
int string defeat_hypothermia_content 0x7f100041
int string defeat_hypothermia_title 0x7f100042
int string defeat_stats_format 0x7f100043
int string eat_food 0x7f100044
int string eat_food_result 0x7f100045
int string environment_effects 0x7f100046
int string error_a11y_label 0x7f100047
int string error_icon_content_description 0x7f100048
int string event_effect 0x7f100049
int string event_not_found 0x7f10004a
int string exit_game 0x7f10004b
int string exposed_dropdown_menu_content_description 0x7f10004c
int string fab_transformation_scrim_behavior 0x7f10004d
int string fab_transformation_sheet_behavior 0x7f10004e
int string firewood_name 0x7f10004f
int string food_name 0x7f100050
int string food_phase_description 0x7f100051
int string food_phase_title 0x7f100052
int string game_data_load_failed 0x7f100053
int string game_description 0x7f100054
int string hide_bottom_view_on_scroll_behavior 0x7f100055
int string high_morale_effect 0x7f100056
int string hope_greatly_decreased 0x7f100057
int string hope_greatly_increased 0x7f100058
int string hope_slightly_decreased 0x7f100059
int string hope_slightly_increased 0x7f10005a
int string icon_content_description 0x7f10005b
int string item_view_role_description 0x7f10005c
int string language 0x7f10005d
int string language_cancel 0x7f10005e
int string language_english 0x7f10005f
int string language_french 0x7f100060
int string language_german 0x7f100061
int string language_hindi 0x7f100062
int string language_indonesian 0x7f100063
int string language_italian 0x7f100064
int string language_japanese 0x7f100065
int string language_korean 0x7f100066
int string language_malay 0x7f100067
int string language_portuguese 0x7f100068
int string language_russian 0x7f100069
int string language_selection_title 0x7f10006a
int string language_simplified_chinese 0x7f10006b
int string language_spanish 0x7f10006c
int string language_thai 0x7f10006d
int string language_traditional_chinese 0x7f10006e
int string language_vietnamese 0x7f10006f
int string leave_mountain 0x7f100070
int string low_morale_effect 0x7f100071
int string m3_ref_typeface_brand_medium 0x7f100072
int string m3_ref_typeface_brand_regular 0x7f100073
int string m3_ref_typeface_plain_medium 0x7f100074
int string m3_ref_typeface_plain_regular 0x7f100075
int string m3_sys_motion_easing_emphasized 0x7f100076
int string m3_sys_motion_easing_emphasized_accelerate 0x7f100077
int string m3_sys_motion_easing_emphasized_decelerate 0x7f100078
int string m3_sys_motion_easing_emphasized_path_data 0x7f100079
int string m3_sys_motion_easing_legacy 0x7f10007a
int string m3_sys_motion_easing_legacy_accelerate 0x7f10007b
int string m3_sys_motion_easing_legacy_decelerate 0x7f10007c
int string m3_sys_motion_easing_linear 0x7f10007d
int string m3_sys_motion_easing_standard 0x7f10007e
int string m3_sys_motion_easing_standard_accelerate 0x7f10007f
int string m3_sys_motion_easing_standard_decelerate 0x7f100080
int string material_clock_display_divider 0x7f100081
int string material_clock_toggle_content_description 0x7f100082
int string material_hour_24h_suffix 0x7f100083
int string material_hour_selection 0x7f100084
int string material_hour_suffix 0x7f100085
int string material_minute_selection 0x7f100086
int string material_minute_suffix 0x7f100087
int string material_motion_easing_accelerated 0x7f100088
int string material_motion_easing_decelerated 0x7f100089
int string material_motion_easing_emphasized 0x7f10008a
int string material_motion_easing_linear 0x7f10008b
int string material_motion_easing_standard 0x7f10008c
int string material_slider_range_end 0x7f10008d
int string material_slider_range_start 0x7f10008e
int string material_slider_value 0x7f10008f
int string material_timepicker_am 0x7f100090
int string material_timepicker_clock_mode_description 0x7f100091
int string material_timepicker_hour 0x7f100092
int string material_timepicker_minute 0x7f100093
int string material_timepicker_pm 0x7f100094
int string material_timepicker_select_time 0x7f100095
int string material_timepicker_text_input_mode_description 0x7f100096
int string mtrl_badge_numberless_content_description 0x7f100097
int string mtrl_checkbox_button_icon_path_checked 0x7f100098
int string mtrl_checkbox_button_icon_path_group_name 0x7f100099
int string mtrl_checkbox_button_icon_path_indeterminate 0x7f10009a
int string mtrl_checkbox_button_icon_path_name 0x7f10009b
int string mtrl_checkbox_button_path_checked 0x7f10009c
int string mtrl_checkbox_button_path_group_name 0x7f10009d
int string mtrl_checkbox_button_path_name 0x7f10009e
int string mtrl_checkbox_button_path_unchecked 0x7f10009f
int string mtrl_checkbox_state_description_checked 0x7f1000a0
int string mtrl_checkbox_state_description_indeterminate 0x7f1000a1
int string mtrl_checkbox_state_description_unchecked 0x7f1000a2
int string mtrl_chip_close_icon_content_description 0x7f1000a3
int string mtrl_exceed_max_badge_number_content_description 0x7f1000a4
int string mtrl_exceed_max_badge_number_suffix 0x7f1000a5
int string mtrl_picker_a11y_next_month 0x7f1000a6
int string mtrl_picker_a11y_prev_month 0x7f1000a7
int string mtrl_picker_announce_current_range_selection 0x7f1000a8
int string mtrl_picker_announce_current_selection 0x7f1000a9
int string mtrl_picker_announce_current_selection_none 0x7f1000aa
int string mtrl_picker_cancel 0x7f1000ab
int string mtrl_picker_confirm 0x7f1000ac
int string mtrl_picker_date_header_selected 0x7f1000ad
int string mtrl_picker_date_header_title 0x7f1000ae
int string mtrl_picker_date_header_unselected 0x7f1000af
int string mtrl_picker_day_of_week_column_header 0x7f1000b0
int string mtrl_picker_end_date_description 0x7f1000b1
int string mtrl_picker_invalid_format 0x7f1000b2
int string mtrl_picker_invalid_format_example 0x7f1000b3
int string mtrl_picker_invalid_format_use 0x7f1000b4
int string mtrl_picker_invalid_range 0x7f1000b5
int string mtrl_picker_navigate_to_current_year_description 0x7f1000b6
int string mtrl_picker_navigate_to_year_description 0x7f1000b7
int string mtrl_picker_out_of_range 0x7f1000b8
int string mtrl_picker_range_header_only_end_selected 0x7f1000b9
int string mtrl_picker_range_header_only_start_selected 0x7f1000ba
int string mtrl_picker_range_header_selected 0x7f1000bb
int string mtrl_picker_range_header_title 0x7f1000bc
int string mtrl_picker_range_header_unselected 0x7f1000bd
int string mtrl_picker_save 0x7f1000be
int string mtrl_picker_start_date_description 0x7f1000bf
int string mtrl_picker_text_input_date_hint 0x7f1000c0
int string mtrl_picker_text_input_date_range_end_hint 0x7f1000c1
int string mtrl_picker_text_input_date_range_start_hint 0x7f1000c2
int string mtrl_picker_text_input_day_abbr 0x7f1000c3
int string mtrl_picker_text_input_month_abbr 0x7f1000c4
int string mtrl_picker_text_input_year_abbr 0x7f1000c5
int string mtrl_picker_today_description 0x7f1000c6
int string mtrl_picker_toggle_to_calendar_input_mode 0x7f1000c7
int string mtrl_picker_toggle_to_day_selection 0x7f1000c8
int string mtrl_picker_toggle_to_text_input_mode 0x7f1000c9
int string mtrl_picker_toggle_to_year_selection 0x7f1000ca
int string mtrl_switch_thumb_group_name 0x7f1000cb
int string mtrl_switch_thumb_path_checked 0x7f1000cc
int string mtrl_switch_thumb_path_morphing 0x7f1000cd
int string mtrl_switch_thumb_path_name 0x7f1000ce
int string mtrl_switch_thumb_path_pressed 0x7f1000cf
int string mtrl_switch_thumb_path_unchecked 0x7f1000d0
int string mtrl_switch_track_decoration_path 0x7f1000d1
int string mtrl_switch_track_path 0x7f1000d2
int string mtrl_timepicker_cancel 0x7f1000d3
int string mtrl_timepicker_confirm 0x7f1000d4
int string music_off 0x7f1000d5
int string music_on 0x7f1000d6
int string new_day_generic 0x7f1000d7
int string night_firewood_used 0x7f1000d8
int string night_no_firewood 0x7f1000d9
int string night_phase_title 0x7f1000da
int string night_roof_leaking 0x7f1000db
int string night_settlement 0x7f1000dc
int string night_stamina_lost 0x7f1000dd
int string night_warmth_gained 0x7f1000de
int string night_warmth_lost 0x7f1000df
int string night_with_firewood 0x7f1000e0
int string night_without_firewood 0x7f1000e1
int string no_choice_continue 0x7f1000e2
int string ok_button 0x7f1000e3
int string password_toggle_content_description 0x7f1000e4
int string path_password_eye 0x7f1000e5
int string path_password_eye_mask_strike_through 0x7f1000e6
int string path_password_eye_mask_visible 0x7f1000e7
int string path_password_strike_through 0x7f1000e8
int string privacy_policy_agree 0x7f1000e9
int string privacy_policy_content 0x7f1000ea
int string privacy_policy_disagree 0x7f1000eb
int string privacy_policy_link_text 0x7f1000ec
int string privacy_policy_subtitle 0x7f1000ed
int string privacy_policy_title 0x7f1000ee
int string privacy_policy_url 0x7f1000ef
int string random_event_continue 0x7f1000f0
int string random_event_effect 0x7f1000f1
int string rest_indoors 0x7f1000f2
int string restart_challenge 0x7f1000f3
int string revive_ad_load_failed_message 0x7f1000f4
int string revive_ad_loading_message 0x7f1000f5
int string revive_ad_loading_title 0x7f1000f6
int string revive_ad_show_failed_message 0x7f1000f7
int string revive_success_generic 0x7f1000f8
int string revive_success_message 0x7f1000f9
int string revive_success_title 0x7f1000fa
int string reward_message 0x7f1000fb
int string reward_title 0x7f1000fc
int string save_food 0x7f1000fd
int string save_food_result 0x7f1000fe
int string search_menu_title 0x7f1000ff
int string search_resources 0x7f100100
int string searchbar_scrolling_view_behavior 0x7f100101
int string searchview_clear_text_content_description 0x7f100102
int string searchview_navigation_content_description 0x7f100103
int string side_sheet_accessibility_pane_title 0x7f100104
int string side_sheet_behavior 0x7f100105
int string stamina_name 0x7f100106
int string start_game 0x7f100107
int string status_bar_notification_info_overflow 0x7f100108
int string status_change_prefix 0x7f100109
int string title_content_description 0x7f10010a
int string version 0x7f10010b
int string victory_barely_survived_content 0x7f10010c
int string victory_barely_survived_stats 0x7f10010d
int string victory_barely_survived_title 0x7f10010e
int string victory_perfect_rescue_content 0x7f10010f
int string victory_perfect_rescue_stats 0x7f100110
int string victory_perfect_rescue_title 0x7f100111
int string victory_stats_format 0x7f100112
int string victory_strong_will_content 0x7f100113
int string victory_strong_will_stats 0x7f100114
int string victory_strong_will_title 0x7f100115
int string victory_successful_survival_content 0x7f100116
int string victory_successful_survival_stats 0x7f100117
int string victory_successful_survival_title 0x7f100118
int string warmth_name 0x7f100119
int style AlertDialog_AppCompat 0x7f110000
int style AlertDialog_AppCompat_Light 0x7f110001
int style Animation_AppCompat_Dialog 0x7f110002
int style Animation_AppCompat_DropDownUp 0x7f110003
int style Animation_AppCompat_Tooltip 0x7f110004
int style Animation_Design_BottomSheetDialog 0x7f110005
int style Animation_Material3_BottomSheetDialog 0x7f110006
int style Animation_Material3_SideSheetDialog 0x7f110007
int style Animation_MaterialComponents_BottomSheetDialog 0x7f110008
int style Base_AlertDialog_AppCompat 0x7f110009
int style Base_AlertDialog_AppCompat_Light 0x7f11000a
int style Base_Animation_AppCompat_Dialog 0x7f11000b
int style Base_Animation_AppCompat_DropDownUp 0x7f11000c
int style Base_Animation_AppCompat_Tooltip 0x7f11000d
int style Base_CardView 0x7f11000e
int style Base_DialogWindowTitle_AppCompat 0x7f11000f
int style Base_DialogWindowTitleBackground_AppCompat 0x7f110010
int style Base_MaterialAlertDialog_MaterialComponents_Title_Icon 0x7f110011
int style Base_MaterialAlertDialog_MaterialComponents_Title_Panel 0x7f110012
int style Base_MaterialAlertDialog_MaterialComponents_Title_Text 0x7f110013
int style Base_TextAppearance_AppCompat 0x7f110014
int style Base_TextAppearance_AppCompat_Body1 0x7f110015
int style Base_TextAppearance_AppCompat_Body2 0x7f110016
int style Base_TextAppearance_AppCompat_Button 0x7f110017
int style Base_TextAppearance_AppCompat_Caption 0x7f110018
int style Base_TextAppearance_AppCompat_Display1 0x7f110019
int style Base_TextAppearance_AppCompat_Display2 0x7f11001a
int style Base_TextAppearance_AppCompat_Display3 0x7f11001b
int style Base_TextAppearance_AppCompat_Display4 0x7f11001c
int style Base_TextAppearance_AppCompat_Headline 0x7f11001d
int style Base_TextAppearance_AppCompat_Inverse 0x7f11001e
int style Base_TextAppearance_AppCompat_Large 0x7f11001f
int style Base_TextAppearance_AppCompat_Large_Inverse 0x7f110020
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f110021
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f110022
int style Base_TextAppearance_AppCompat_Medium 0x7f110023
int style Base_TextAppearance_AppCompat_Medium_Inverse 0x7f110024
int style Base_TextAppearance_AppCompat_Menu 0x7f110025
int style Base_TextAppearance_AppCompat_SearchResult 0x7f110026
int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x7f110027
int style Base_TextAppearance_AppCompat_SearchResult_Title 0x7f110028
int style Base_TextAppearance_AppCompat_Small 0x7f110029
int style Base_TextAppearance_AppCompat_Small_Inverse 0x7f11002a
int style Base_TextAppearance_AppCompat_Subhead 0x7f11002b
int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x7f11002c
int style Base_TextAppearance_AppCompat_Title 0x7f11002d
int style Base_TextAppearance_AppCompat_Title_Inverse 0x7f11002e
int style Base_TextAppearance_AppCompat_Tooltip 0x7f11002f
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f110030
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f110031
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f110032
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f110033
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f110034
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f110035
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f110036
int style Base_TextAppearance_AppCompat_Widget_Button 0x7f110037
int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f110038
int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x7f110039
int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x7f11003a
int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x7f11003b
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f11003c
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f11003d
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f11003e
int style Base_TextAppearance_AppCompat_Widget_Switch 0x7f11003f
int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f110040
int style Base_TextAppearance_Material3_Search 0x7f110041
int style Base_TextAppearance_MaterialComponents_Badge 0x7f110042
int style Base_TextAppearance_MaterialComponents_Button 0x7f110043
int style Base_TextAppearance_MaterialComponents_Headline6 0x7f110044
int style Base_TextAppearance_MaterialComponents_Subtitle2 0x7f110045
int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f110046
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f110047
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f110048
int style Base_Theme_AppCompat 0x7f110049
int style Base_Theme_AppCompat_CompactMenu 0x7f11004a
int style Base_Theme_AppCompat_Dialog 0x7f11004b
int style Base_Theme_AppCompat_Dialog_Alert 0x7f11004c
int style Base_Theme_AppCompat_Dialog_FixedSize 0x7f11004d
int style Base_Theme_AppCompat_Dialog_MinWidth 0x7f11004e
int style Base_Theme_AppCompat_DialogWhenLarge 0x7f11004f
int style Base_Theme_AppCompat_Light 0x7f110050
int style Base_Theme_AppCompat_Light_DarkActionBar 0x7f110051
int style Base_Theme_AppCompat_Light_Dialog 0x7f110052
int style Base_Theme_AppCompat_Light_Dialog_Alert 0x7f110053
int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x7f110054
int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x7f110055
int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x7f110056
int style Base_Theme_Material3_Dark 0x7f110057
int style Base_Theme_Material3_Dark_BottomSheetDialog 0x7f110058
int style Base_Theme_Material3_Dark_Dialog 0x7f110059
int style Base_Theme_Material3_Dark_SideSheetDialog 0x7f11005a
int style Base_Theme_Material3_Light 0x7f11005b
int style Base_Theme_Material3_Light_BottomSheetDialog 0x7f11005c
int style Base_Theme_Material3_Light_Dialog 0x7f11005d
int style Base_Theme_Material3_Light_SideSheetDialog 0x7f11005e
int style Base_Theme_MaterialComponents 0x7f11005f
int style Base_Theme_MaterialComponents_Bridge 0x7f110060
int style Base_Theme_MaterialComponents_CompactMenu 0x7f110061
int style Base_Theme_MaterialComponents_Dialog 0x7f110062
int style Base_Theme_MaterialComponents_Dialog_Alert 0x7f110063
int style Base_Theme_MaterialComponents_Dialog_Bridge 0x7f110064
int style Base_Theme_MaterialComponents_Dialog_FixedSize 0x7f110065
int style Base_Theme_MaterialComponents_Dialog_MinWidth 0x7f110066
int style Base_Theme_MaterialComponents_DialogWhenLarge 0x7f110067
int style Base_Theme_MaterialComponents_Light 0x7f110068
int style Base_Theme_MaterialComponents_Light_Bridge 0x7f110069
int style Base_Theme_MaterialComponents_Light_DarkActionBar 0x7f11006a
int style Base_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f11006b
int style Base_Theme_MaterialComponents_Light_Dialog 0x7f11006c
int style Base_Theme_MaterialComponents_Light_Dialog_Alert 0x7f11006d
int style Base_Theme_MaterialComponents_Light_Dialog_Bridge 0x7f11006e
int style Base_Theme_MaterialComponents_Light_Dialog_FixedSize 0x7f11006f
int style Base_Theme_MaterialComponents_Light_Dialog_MinWidth 0x7f110070
int style Base_Theme_MaterialComponents_Light_DialogWhenLarge 0x7f110071
int style Base_ThemeOverlay_AppCompat 0x7f110072
int style Base_ThemeOverlay_AppCompat_ActionBar 0x7f110073
int style Base_ThemeOverlay_AppCompat_Dark 0x7f110074
int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x7f110075
int style Base_ThemeOverlay_AppCompat_Dialog 0x7f110076
int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x7f110077
int style Base_ThemeOverlay_AppCompat_Light 0x7f110078
int style Base_ThemeOverlay_Material3_AutoCompleteTextView 0x7f110079
int style Base_ThemeOverlay_Material3_BottomSheetDialog 0x7f11007a
int style Base_ThemeOverlay_Material3_Dialog 0x7f11007b
int style Base_ThemeOverlay_Material3_SideSheetDialog 0x7f11007c
int style Base_ThemeOverlay_Material3_TextInputEditText 0x7f11007d
int style Base_ThemeOverlay_MaterialComponents_Dialog 0x7f11007e
int style Base_ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f11007f
int style Base_ThemeOverlay_MaterialComponents_Dialog_Alert_Framework 0x7f110080
int style Base_ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework 0x7f110081
int style Base_ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x7f110082
int style Base_V14_Theme_Material3_Dark 0x7f110083
int style Base_V14_Theme_Material3_Dark_BottomSheetDialog 0x7f110084
int style Base_V14_Theme_Material3_Dark_Dialog 0x7f110085
int style Base_V14_Theme_Material3_Dark_SideSheetDialog 0x7f110086
int style Base_V14_Theme_Material3_Light 0x7f110087
int style Base_V14_Theme_Material3_Light_BottomSheetDialog 0x7f110088
int style Base_V14_Theme_Material3_Light_Dialog 0x7f110089
int style Base_V14_Theme_Material3_Light_SideSheetDialog 0x7f11008a
int style Base_V14_Theme_MaterialComponents 0x7f11008b
int style Base_V14_Theme_MaterialComponents_Bridge 0x7f11008c
int style Base_V14_Theme_MaterialComponents_Dialog 0x7f11008d
int style Base_V14_Theme_MaterialComponents_Dialog_Bridge 0x7f11008e
int style Base_V14_Theme_MaterialComponents_Light 0x7f11008f
int style Base_V14_Theme_MaterialComponents_Light_Bridge 0x7f110090
int style Base_V14_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f110091
int style Base_V14_Theme_MaterialComponents_Light_Dialog 0x7f110092
int style Base_V14_Theme_MaterialComponents_Light_Dialog_Bridge 0x7f110093
int style Base_V14_ThemeOverlay_Material3_BottomSheetDialog 0x7f110094
int style Base_V14_ThemeOverlay_Material3_SideSheetDialog 0x7f110095
int style Base_V14_ThemeOverlay_MaterialComponents_BottomSheetDialog 0x7f110096
int style Base_V14_ThemeOverlay_MaterialComponents_Dialog 0x7f110097
int style Base_V14_ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f110098
int style Base_V14_ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x7f110099
int style Base_V14_Widget_MaterialComponents_AutoCompleteTextView 0x7f11009a
int style Base_V21_Theme_AppCompat 0x7f11009b
int style Base_V21_Theme_AppCompat_Dialog 0x7f11009c
int style Base_V21_Theme_AppCompat_Light 0x7f11009d
int style Base_V21_Theme_AppCompat_Light_Dialog 0x7f11009e
int style Base_V21_Theme_MaterialComponents 0x7f11009f
int style Base_V21_Theme_MaterialComponents_Dialog 0x7f1100a0
int style Base_V21_Theme_MaterialComponents_Light 0x7f1100a1
int style Base_V21_Theme_MaterialComponents_Light_Dialog 0x7f1100a2
int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x7f1100a3
int style Base_V21_ThemeOverlay_Material3_BottomSheetDialog 0x7f1100a4
int style Base_V21_ThemeOverlay_Material3_SideSheetDialog 0x7f1100a5
int style Base_V21_ThemeOverlay_MaterialComponents_BottomSheetDialog 0x7f1100a6
int style Base_V22_Theme_AppCompat 0x7f1100a7
int style Base_V22_Theme_AppCompat_Light 0x7f1100a8
int style Base_V23_Theme_AppCompat 0x7f1100a9
int style Base_V23_Theme_AppCompat_Light 0x7f1100aa
int style Base_V24_Theme_Material3_Dark 0x7f1100ab
int style Base_V24_Theme_Material3_Dark_Dialog 0x7f1100ac
int style Base_V24_Theme_Material3_Light 0x7f1100ad
int style Base_V24_Theme_Material3_Light_Dialog 0x7f1100ae
int style Base_V26_Theme_AppCompat 0x7f1100af
int style Base_V26_Theme_AppCompat_Light 0x7f1100b0
int style Base_V26_Widget_AppCompat_Toolbar 0x7f1100b1
int style Base_V28_Theme_AppCompat 0x7f1100b2
int style Base_V28_Theme_AppCompat_Light 0x7f1100b3
int style Base_V7_Theme_AppCompat 0x7f1100b4
int style Base_V7_Theme_AppCompat_Dialog 0x7f1100b5
int style Base_V7_Theme_AppCompat_Light 0x7f1100b6
int style Base_V7_Theme_AppCompat_Light_Dialog 0x7f1100b7
int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x7f1100b8
int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x7f1100b9
int style Base_V7_Widget_AppCompat_EditText 0x7f1100ba
int style Base_V7_Widget_AppCompat_Toolbar 0x7f1100bb
int style Base_Widget_AppCompat_ActionBar 0x7f1100bc
int style Base_Widget_AppCompat_ActionBar_Solid 0x7f1100bd
int style Base_Widget_AppCompat_ActionBar_TabBar 0x7f1100be
int style Base_Widget_AppCompat_ActionBar_TabText 0x7f1100bf
int style Base_Widget_AppCompat_ActionBar_TabView 0x7f1100c0
int style Base_Widget_AppCompat_ActionButton 0x7f1100c1
int style Base_Widget_AppCompat_ActionButton_CloseMode 0x7f1100c2
int style Base_Widget_AppCompat_ActionButton_Overflow 0x7f1100c3
int style Base_Widget_AppCompat_ActionMode 0x7f1100c4
int style Base_Widget_AppCompat_ActivityChooserView 0x7f1100c5
int style Base_Widget_AppCompat_AutoCompleteTextView 0x7f1100c6
int style Base_Widget_AppCompat_Button 0x7f1100c7
int style Base_Widget_AppCompat_Button_Borderless 0x7f1100c8
int style Base_Widget_AppCompat_Button_Borderless_Colored 0x7f1100c9
int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f1100ca
int style Base_Widget_AppCompat_Button_Colored 0x7f1100cb
int style Base_Widget_AppCompat_Button_Small 0x7f1100cc
int style Base_Widget_AppCompat_ButtonBar 0x7f1100cd
int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x7f1100ce
int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x7f1100cf
int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x7f1100d0
int style Base_Widget_AppCompat_CompoundButton_Switch 0x7f1100d1
int style Base_Widget_AppCompat_DrawerArrowToggle 0x7f1100d2
int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x7f1100d3
int style Base_Widget_AppCompat_DropDownItem_Spinner 0x7f1100d4
int style Base_Widget_AppCompat_EditText 0x7f1100d5
int style Base_Widget_AppCompat_ImageButton 0x7f1100d6
int style Base_Widget_AppCompat_Light_ActionBar 0x7f1100d7
int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x7f1100d8
int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x7f1100d9
int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x7f1100da
int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f1100db
int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x7f1100dc
int style Base_Widget_AppCompat_Light_PopupMenu 0x7f1100dd
int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x7f1100de
int style Base_Widget_AppCompat_ListMenuView 0x7f1100df
int style Base_Widget_AppCompat_ListPopupWindow 0x7f1100e0
int style Base_Widget_AppCompat_ListView 0x7f1100e1
int style Base_Widget_AppCompat_ListView_DropDown 0x7f1100e2
int style Base_Widget_AppCompat_ListView_Menu 0x7f1100e3
int style Base_Widget_AppCompat_PopupMenu 0x7f1100e4
int style Base_Widget_AppCompat_PopupMenu_Overflow 0x7f1100e5
int style Base_Widget_AppCompat_PopupWindow 0x7f1100e6
int style Base_Widget_AppCompat_ProgressBar 0x7f1100e7
int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x7f1100e8
int style Base_Widget_AppCompat_RatingBar 0x7f1100e9
int style Base_Widget_AppCompat_RatingBar_Indicator 0x7f1100ea
int style Base_Widget_AppCompat_RatingBar_Small 0x7f1100eb
int style Base_Widget_AppCompat_SearchView 0x7f1100ec
int style Base_Widget_AppCompat_SearchView_ActionBar 0x7f1100ed
int style Base_Widget_AppCompat_SeekBar 0x7f1100ee
int style Base_Widget_AppCompat_SeekBar_Discrete 0x7f1100ef
int style Base_Widget_AppCompat_Spinner 0x7f1100f0
int style Base_Widget_AppCompat_Spinner_Underlined 0x7f1100f1
int style Base_Widget_AppCompat_TextView 0x7f1100f2
int style Base_Widget_AppCompat_TextView_SpinnerItem 0x7f1100f3
int style Base_Widget_AppCompat_Toolbar 0x7f1100f4
int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x7f1100f5
int style Base_Widget_Design_TabLayout 0x7f1100f6
int style Base_Widget_Material3_ActionBar_Solid 0x7f1100f7
int style Base_Widget_Material3_ActionMode 0x7f1100f8
int style Base_Widget_Material3_BottomNavigationView 0x7f1100f9
int style Base_Widget_Material3_CardView 0x7f1100fa
int style Base_Widget_Material3_Chip 0x7f1100fb
int style Base_Widget_Material3_CollapsingToolbar 0x7f1100fc
int style Base_Widget_Material3_CompoundButton_CheckBox 0x7f1100fd
int style Base_Widget_Material3_CompoundButton_RadioButton 0x7f1100fe
int style Base_Widget_Material3_CompoundButton_Switch 0x7f1100ff
int style Base_Widget_Material3_ExtendedFloatingActionButton 0x7f110100
int style Base_Widget_Material3_ExtendedFloatingActionButton_Icon 0x7f110101
int style Base_Widget_Material3_FloatingActionButton 0x7f110102
int style Base_Widget_Material3_FloatingActionButton_Large 0x7f110103
int style Base_Widget_Material3_FloatingActionButton_Small 0x7f110104
int style Base_Widget_Material3_Light_ActionBar_Solid 0x7f110105
int style Base_Widget_Material3_MaterialCalendar_NavigationButton 0x7f110106
int style Base_Widget_Material3_Snackbar 0x7f110107
int style Base_Widget_Material3_TabLayout 0x7f110108
int style Base_Widget_Material3_TabLayout_OnSurface 0x7f110109
int style Base_Widget_Material3_TabLayout_Secondary 0x7f11010a
int style Base_Widget_MaterialComponents_AutoCompleteTextView 0x7f11010b
int style Base_Widget_MaterialComponents_CheckedTextView 0x7f11010c
int style Base_Widget_MaterialComponents_Chip 0x7f11010d
int style Base_Widget_MaterialComponents_MaterialCalendar_HeaderToggleButton 0x7f11010e
int style Base_Widget_MaterialComponents_MaterialCalendar_NavigationButton 0x7f11010f
int style Base_Widget_MaterialComponents_PopupMenu 0x7f110110
int style Base_Widget_MaterialComponents_PopupMenu_ContextMenu 0x7f110111
int style Base_Widget_MaterialComponents_PopupMenu_ListPopupWindow 0x7f110112
int style Base_Widget_MaterialComponents_PopupMenu_Overflow 0x7f110113
int style Base_Widget_MaterialComponents_Slider 0x7f110114
int style Base_Widget_MaterialComponents_Snackbar 0x7f110115
int style Base_Widget_MaterialComponents_TextInputEditText 0x7f110116
int style Base_Widget_MaterialComponents_TextInputLayout 0x7f110117
int style Base_Widget_MaterialComponents_TextView 0x7f110118
int style CardView 0x7f110119
int style CardView_Dark 0x7f11011a
int style CardView_Light 0x7f11011b
int style MaterialAlertDialog_Material3 0x7f11011c
int style MaterialAlertDialog_Material3_Animation 0x7f11011d
int style MaterialAlertDialog_Material3_Body_Text 0x7f11011e
int style MaterialAlertDialog_Material3_Body_Text_CenterStacked 0x7f11011f
int style MaterialAlertDialog_Material3_Title_Icon 0x7f110120
int style MaterialAlertDialog_Material3_Title_Icon_CenterStacked 0x7f110121
int style MaterialAlertDialog_Material3_Title_Panel 0x7f110122
int style MaterialAlertDialog_Material3_Title_Panel_CenterStacked 0x7f110123
int style MaterialAlertDialog_Material3_Title_Text 0x7f110124
int style MaterialAlertDialog_Material3_Title_Text_CenterStacked 0x7f110125
int style MaterialAlertDialog_MaterialComponents 0x7f110126
int style MaterialAlertDialog_MaterialComponents_Body_Text 0x7f110127
int style MaterialAlertDialog_MaterialComponents_Picker_Date_Calendar 0x7f110128
int style MaterialAlertDialog_MaterialComponents_Picker_Date_Spinner 0x7f110129
int style MaterialAlertDialog_MaterialComponents_Title_Icon 0x7f11012a
int style MaterialAlertDialog_MaterialComponents_Title_Icon_CenterStacked 0x7f11012b
int style MaterialAlertDialog_MaterialComponents_Title_Panel 0x7f11012c
int style MaterialAlertDialog_MaterialComponents_Title_Panel_CenterStacked 0x7f11012d
int style MaterialAlertDialog_MaterialComponents_Title_Text 0x7f11012e
int style MaterialAlertDialog_MaterialComponents_Title_Text_CenterStacked 0x7f11012f
int style Platform_AppCompat 0x7f110130
int style Platform_AppCompat_Light 0x7f110131
int style Platform_MaterialComponents 0x7f110132
int style Platform_MaterialComponents_Dialog 0x7f110133
int style Platform_MaterialComponents_Light 0x7f110134
int style Platform_MaterialComponents_Light_Dialog 0x7f110135
int style Platform_ThemeOverlay_AppCompat 0x7f110136
int style Platform_ThemeOverlay_AppCompat_Dark 0x7f110137
int style Platform_ThemeOverlay_AppCompat_Light 0x7f110138
int style Platform_V21_AppCompat 0x7f110139
int style Platform_V21_AppCompat_Light 0x7f11013a
int style Platform_V25_AppCompat 0x7f11013b
int style Platform_V25_AppCompat_Light 0x7f11013c
int style Platform_Widget_AppCompat_Spinner 0x7f11013d
int style RtlOverlay_DialogWindowTitle_AppCompat 0x7f11013e
int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x7f11013f
int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x7f110140
int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x7f110141
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x7f110142
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x7f110143
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x7f110144
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x7f110145
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x7f110146
int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x7f110147
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x7f110148
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x7f110149
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x7f11014a
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x7f11014b
int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x7f11014c
int style RtlUnderlay_Widget_AppCompat_ActionButton 0x7f11014d
int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x7f11014e
int style ShapeAppearance_M3_Comp_Badge_Large_Shape 0x7f11014f
int style ShapeAppearance_M3_Comp_Badge_Shape 0x7f110150
int style ShapeAppearance_M3_Comp_BottomAppBar_Container_Shape 0x7f110151
int style ShapeAppearance_M3_Comp_FilledButton_Container_Shape 0x7f110152
int style ShapeAppearance_M3_Comp_NavigationBar_ActiveIndicator_Shape 0x7f110153
int style ShapeAppearance_M3_Comp_NavigationDrawer_ActiveIndicator_Shape 0x7f110154
int style ShapeAppearance_M3_Comp_NavigationRail_ActiveIndicator_Shape 0x7f110155
int style ShapeAppearance_M3_Comp_SearchBar_Avatar_Shape 0x7f110156
int style ShapeAppearance_M3_Comp_SearchBar_Container_Shape 0x7f110157
int style ShapeAppearance_M3_Comp_SearchView_FullScreen_Container_Shape 0x7f110158
int style ShapeAppearance_M3_Comp_Sheet_Side_Docked_Container_Shape 0x7f110159
int style ShapeAppearance_M3_Comp_Switch_Handle_Shape 0x7f11015a
int style ShapeAppearance_M3_Comp_Switch_StateLayer_Shape 0x7f11015b
int style ShapeAppearance_M3_Comp_Switch_Track_Shape 0x7f11015c
int style ShapeAppearance_M3_Comp_TextButton_Container_Shape 0x7f11015d
int style ShapeAppearance_M3_Sys_Shape_Corner_ExtraLarge 0x7f11015e
int style ShapeAppearance_M3_Sys_Shape_Corner_ExtraSmall 0x7f11015f
int style ShapeAppearance_M3_Sys_Shape_Corner_Full 0x7f110160
int style ShapeAppearance_M3_Sys_Shape_Corner_Large 0x7f110161
int style ShapeAppearance_M3_Sys_Shape_Corner_Medium 0x7f110162
int style ShapeAppearance_M3_Sys_Shape_Corner_None 0x7f110163
int style ShapeAppearance_M3_Sys_Shape_Corner_Small 0x7f110164
int style ShapeAppearance_Material3_Corner_ExtraLarge 0x7f110165
int style ShapeAppearance_Material3_Corner_ExtraSmall 0x7f110166
int style ShapeAppearance_Material3_Corner_Full 0x7f110167
int style ShapeAppearance_Material3_Corner_Large 0x7f110168
int style ShapeAppearance_Material3_Corner_Medium 0x7f110169
int style ShapeAppearance_Material3_Corner_None 0x7f11016a
int style ShapeAppearance_Material3_Corner_Small 0x7f11016b
int style ShapeAppearance_Material3_LargeComponent 0x7f11016c
int style ShapeAppearance_Material3_MediumComponent 0x7f11016d
int style ShapeAppearance_Material3_NavigationBarView_ActiveIndicator 0x7f11016e
int style ShapeAppearance_Material3_SmallComponent 0x7f11016f
int style ShapeAppearance_Material3_Tooltip 0x7f110170
int style ShapeAppearance_MaterialComponents 0x7f110171
int style ShapeAppearance_MaterialComponents_Badge 0x7f110172
int style ShapeAppearance_MaterialComponents_LargeComponent 0x7f110173
int style ShapeAppearance_MaterialComponents_MediumComponent 0x7f110174
int style ShapeAppearance_MaterialComponents_SmallComponent 0x7f110175
int style ShapeAppearance_MaterialComponents_Tooltip 0x7f110176
int style ShapeAppearanceOverlay_Material3_Button 0x7f110177
int style ShapeAppearanceOverlay_Material3_Chip 0x7f110178
int style ShapeAppearanceOverlay_Material3_Corner_Bottom 0x7f110179
int style ShapeAppearanceOverlay_Material3_Corner_Left 0x7f11017a
int style ShapeAppearanceOverlay_Material3_Corner_Right 0x7f11017b
int style ShapeAppearanceOverlay_Material3_Corner_Top 0x7f11017c
int style ShapeAppearanceOverlay_Material3_FloatingActionButton 0x7f11017d
int style ShapeAppearanceOverlay_Material3_NavigationView_Item 0x7f11017e
int style ShapeAppearanceOverlay_Material3_SearchBar 0x7f11017f
int style ShapeAppearanceOverlay_Material3_SearchView 0x7f110180
int style ShapeAppearanceOverlay_MaterialAlertDialog_Material3 0x7f110181
int style ShapeAppearanceOverlay_MaterialComponents_BottomSheet 0x7f110182
int style ShapeAppearanceOverlay_MaterialComponents_Chip 0x7f110183
int style ShapeAppearanceOverlay_MaterialComponents_ExtendedFloatingActionButton 0x7f110184
int style ShapeAppearanceOverlay_MaterialComponents_FloatingActionButton 0x7f110185
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day 0x7f110186
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Window_Fullscreen 0x7f110187
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Year 0x7f110188
int style ShapeAppearanceOverlay_MaterialComponents_TextInputLayout_FilledBox 0x7f110189
int style TextAppearance_AppCompat 0x7f11018a
int style TextAppearance_AppCompat_Body1 0x7f11018b
int style TextAppearance_AppCompat_Body2 0x7f11018c
int style TextAppearance_AppCompat_Button 0x7f11018d
int style TextAppearance_AppCompat_Caption 0x7f11018e
int style TextAppearance_AppCompat_Display1 0x7f11018f
int style TextAppearance_AppCompat_Display2 0x7f110190
int style TextAppearance_AppCompat_Display3 0x7f110191
int style TextAppearance_AppCompat_Display4 0x7f110192
int style TextAppearance_AppCompat_Headline 0x7f110193
int style TextAppearance_AppCompat_Inverse 0x7f110194
int style TextAppearance_AppCompat_Large 0x7f110195
int style TextAppearance_AppCompat_Large_Inverse 0x7f110196
int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x7f110197
int style TextAppearance_AppCompat_Light_SearchResult_Title 0x7f110198
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f110199
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f11019a
int style TextAppearance_AppCompat_Medium 0x7f11019b
int style TextAppearance_AppCompat_Medium_Inverse 0x7f11019c
int style TextAppearance_AppCompat_Menu 0x7f11019d
int style TextAppearance_AppCompat_SearchResult_Subtitle 0x7f11019e
int style TextAppearance_AppCompat_SearchResult_Title 0x7f11019f
int style TextAppearance_AppCompat_Small 0x7f1101a0
int style TextAppearance_AppCompat_Small_Inverse 0x7f1101a1
int style TextAppearance_AppCompat_Subhead 0x7f1101a2
int style TextAppearance_AppCompat_Subhead_Inverse 0x7f1101a3
int style TextAppearance_AppCompat_Title 0x7f1101a4
int style TextAppearance_AppCompat_Title_Inverse 0x7f1101a5
int style TextAppearance_AppCompat_Tooltip 0x7f1101a6
int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f1101a7
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f1101a8
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f1101a9
int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f1101aa
int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f1101ab
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f1101ac
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x7f1101ad
int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f1101ae
int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x7f1101af
int style TextAppearance_AppCompat_Widget_Button 0x7f1101b0
int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f1101b1
int style TextAppearance_AppCompat_Widget_Button_Colored 0x7f1101b2
int style TextAppearance_AppCompat_Widget_Button_Inverse 0x7f1101b3
int style TextAppearance_AppCompat_Widget_DropDownItem 0x7f1101b4
int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f1101b5
int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f1101b6
int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f1101b7
int style TextAppearance_AppCompat_Widget_Switch 0x7f1101b8
int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f1101b9
int style TextAppearance_Compat_Notification 0x7f1101ba
int style TextAppearance_Compat_Notification_Info 0x7f1101bb
int style TextAppearance_Compat_Notification_Info_Media 0x7f1101bc
int style TextAppearance_Compat_Notification_Line2 0x7f1101bd
int style TextAppearance_Compat_Notification_Line2_Media 0x7f1101be
int style TextAppearance_Compat_Notification_Media 0x7f1101bf
int style TextAppearance_Compat_Notification_Time 0x7f1101c0
int style TextAppearance_Compat_Notification_Time_Media 0x7f1101c1
int style TextAppearance_Compat_Notification_Title 0x7f1101c2
int style TextAppearance_Compat_Notification_Title_Media 0x7f1101c3
int style TextAppearance_Design_CollapsingToolbar_Expanded 0x7f1101c4
int style TextAppearance_Design_Counter 0x7f1101c5
int style TextAppearance_Design_Counter_Overflow 0x7f1101c6
int style TextAppearance_Design_Error 0x7f1101c7
int style TextAppearance_Design_HelperText 0x7f1101c8
int style TextAppearance_Design_Hint 0x7f1101c9
int style TextAppearance_Design_Placeholder 0x7f1101ca
int style TextAppearance_Design_Prefix 0x7f1101cb
int style TextAppearance_Design_Snackbar_Message 0x7f1101cc
int style TextAppearance_Design_Suffix 0x7f1101cd
int style TextAppearance_Design_Tab 0x7f1101ce
int style TextAppearance_M3_Sys_Typescale_BodyLarge 0x7f1101cf
int style TextAppearance_M3_Sys_Typescale_BodyMedium 0x7f1101d0
int style TextAppearance_M3_Sys_Typescale_BodySmall 0x7f1101d1
int style TextAppearance_M3_Sys_Typescale_DisplayLarge 0x7f1101d2
int style TextAppearance_M3_Sys_Typescale_DisplayMedium 0x7f1101d3
int style TextAppearance_M3_Sys_Typescale_DisplaySmall 0x7f1101d4
int style TextAppearance_M3_Sys_Typescale_HeadlineLarge 0x7f1101d5
int style TextAppearance_M3_Sys_Typescale_HeadlineMedium 0x7f1101d6
int style TextAppearance_M3_Sys_Typescale_HeadlineSmall 0x7f1101d7
int style TextAppearance_M3_Sys_Typescale_LabelLarge 0x7f1101d8
int style TextAppearance_M3_Sys_Typescale_LabelMedium 0x7f1101d9
int style TextAppearance_M3_Sys_Typescale_LabelSmall 0x7f1101da
int style TextAppearance_M3_Sys_Typescale_TitleLarge 0x7f1101db
int style TextAppearance_M3_Sys_Typescale_TitleMedium 0x7f1101dc
int style TextAppearance_M3_Sys_Typescale_TitleSmall 0x7f1101dd
int style TextAppearance_Material3_ActionBar_Subtitle 0x7f1101de
int style TextAppearance_Material3_ActionBar_Title 0x7f1101df
int style TextAppearance_Material3_BodyLarge 0x7f1101e0
int style TextAppearance_Material3_BodyMedium 0x7f1101e1
int style TextAppearance_Material3_BodySmall 0x7f1101e2
int style TextAppearance_Material3_DisplayLarge 0x7f1101e3
int style TextAppearance_Material3_DisplayMedium 0x7f1101e4
int style TextAppearance_Material3_DisplaySmall 0x7f1101e5
int style TextAppearance_Material3_HeadlineLarge 0x7f1101e6
int style TextAppearance_Material3_HeadlineMedium 0x7f1101e7
int style TextAppearance_Material3_HeadlineSmall 0x7f1101e8
int style TextAppearance_Material3_LabelLarge 0x7f1101e9
int style TextAppearance_Material3_LabelMedium 0x7f1101ea
int style TextAppearance_Material3_LabelSmall 0x7f1101eb
int style TextAppearance_Material3_MaterialTimePicker_Title 0x7f1101ec
int style TextAppearance_Material3_SearchBar 0x7f1101ed
int style TextAppearance_Material3_SearchView 0x7f1101ee
int style TextAppearance_Material3_SearchView_Prefix 0x7f1101ef
int style TextAppearance_Material3_TitleLarge 0x7f1101f0
int style TextAppearance_Material3_TitleMedium 0x7f1101f1
int style TextAppearance_Material3_TitleSmall 0x7f1101f2
int style TextAppearance_MaterialComponents_Badge 0x7f1101f3
int style TextAppearance_MaterialComponents_Body1 0x7f1101f4
int style TextAppearance_MaterialComponents_Body2 0x7f1101f5
int style TextAppearance_MaterialComponents_Button 0x7f1101f6
int style TextAppearance_MaterialComponents_Caption 0x7f1101f7
int style TextAppearance_MaterialComponents_Chip 0x7f1101f8
int style TextAppearance_MaterialComponents_Headline1 0x7f1101f9
int style TextAppearance_MaterialComponents_Headline2 0x7f1101fa
int style TextAppearance_MaterialComponents_Headline3 0x7f1101fb
int style TextAppearance_MaterialComponents_Headline4 0x7f1101fc
int style TextAppearance_MaterialComponents_Headline5 0x7f1101fd
int style TextAppearance_MaterialComponents_Headline6 0x7f1101fe
int style TextAppearance_MaterialComponents_Overline 0x7f1101ff
int style TextAppearance_MaterialComponents_Subtitle1 0x7f110200
int style TextAppearance_MaterialComponents_Subtitle2 0x7f110201
int style TextAppearance_MaterialComponents_TimePicker_Title 0x7f110202
int style TextAppearance_MaterialComponents_Tooltip 0x7f110203
int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f110204
int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f110205
int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f110206
int style Theme_AppCompat 0x7f110207
int style Theme_AppCompat_CompactMenu 0x7f110208
int style Theme_AppCompat_DayNight 0x7f110209
int style Theme_AppCompat_DayNight_DarkActionBar 0x7f11020a
int style Theme_AppCompat_DayNight_Dialog 0x7f11020b
int style Theme_AppCompat_DayNight_Dialog_Alert 0x7f11020c
int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x7f11020d
int style Theme_AppCompat_DayNight_DialogWhenLarge 0x7f11020e
int style Theme_AppCompat_DayNight_NoActionBar 0x7f11020f
int style Theme_AppCompat_Dialog 0x7f110210
int style Theme_AppCompat_Dialog_Alert 0x7f110211
int style Theme_AppCompat_Dialog_MinWidth 0x7f110212
int style Theme_AppCompat_DialogWhenLarge 0x7f110213
int style Theme_AppCompat_Empty 0x7f110214
int style Theme_AppCompat_Light 0x7f110215
int style Theme_AppCompat_Light_DarkActionBar 0x7f110216
int style Theme_AppCompat_Light_Dialog 0x7f110217
int style Theme_AppCompat_Light_Dialog_Alert 0x7f110218
int style Theme_AppCompat_Light_Dialog_MinWidth 0x7f110219
int style Theme_AppCompat_Light_DialogWhenLarge 0x7f11021a
int style Theme_AppCompat_Light_NoActionBar 0x7f11021b
int style Theme_AppCompat_NoActionBar 0x7f11021c
int style Theme_Design 0x7f11021d
int style Theme_Design_BottomSheetDialog 0x7f11021e
int style Theme_Design_Light 0x7f11021f
int style Theme_Design_Light_BottomSheetDialog 0x7f110220
int style Theme_Design_Light_NoActionBar 0x7f110221
int style Theme_Design_NoActionBar 0x7f110222
int style Theme_Material3_Dark 0x7f110223
int style Theme_Material3_Dark_BottomSheetDialog 0x7f110224
int style Theme_Material3_Dark_Dialog 0x7f110225
int style Theme_Material3_Dark_Dialog_Alert 0x7f110226
int style Theme_Material3_Dark_Dialog_MinWidth 0x7f110227
int style Theme_Material3_Dark_DialogWhenLarge 0x7f110228
int style Theme_Material3_Dark_NoActionBar 0x7f110229
int style Theme_Material3_Dark_SideSheetDialog 0x7f11022a
int style Theme_Material3_DayNight 0x7f11022b
int style Theme_Material3_DayNight_BottomSheetDialog 0x7f11022c
int style Theme_Material3_DayNight_Dialog 0x7f11022d
int style Theme_Material3_DayNight_Dialog_Alert 0x7f11022e
int style Theme_Material3_DayNight_Dialog_MinWidth 0x7f11022f
int style Theme_Material3_DayNight_DialogWhenLarge 0x7f110230
int style Theme_Material3_DayNight_NoActionBar 0x7f110231
int style Theme_Material3_DayNight_SideSheetDialog 0x7f110232
int style Theme_Material3_DynamicColors_Dark 0x7f110233
int style Theme_Material3_DynamicColors_DayNight 0x7f110234
int style Theme_Material3_DynamicColors_Light 0x7f110235
int style Theme_Material3_Light 0x7f110236
int style Theme_Material3_Light_BottomSheetDialog 0x7f110237
int style Theme_Material3_Light_Dialog 0x7f110238
int style Theme_Material3_Light_Dialog_Alert 0x7f110239
int style Theme_Material3_Light_Dialog_MinWidth 0x7f11023a
int style Theme_Material3_Light_DialogWhenLarge 0x7f11023b
int style Theme_Material3_Light_NoActionBar 0x7f11023c
int style Theme_Material3_Light_SideSheetDialog 0x7f11023d
int style Theme_MaterialComponents 0x7f11023e
int style Theme_MaterialComponents_BottomSheetDialog 0x7f11023f
int style Theme_MaterialComponents_Bridge 0x7f110240
int style Theme_MaterialComponents_CompactMenu 0x7f110241
int style Theme_MaterialComponents_DayNight 0x7f110242
int style Theme_MaterialComponents_DayNight_BottomSheetDialog 0x7f110243
int style Theme_MaterialComponents_DayNight_Bridge 0x7f110244
int style Theme_MaterialComponents_DayNight_DarkActionBar 0x7f110245
int style Theme_MaterialComponents_DayNight_DarkActionBar_Bridge 0x7f110246
int style Theme_MaterialComponents_DayNight_Dialog 0x7f110247
int style Theme_MaterialComponents_DayNight_Dialog_Alert 0x7f110248
int style Theme_MaterialComponents_DayNight_Dialog_Alert_Bridge 0x7f110249
int style Theme_MaterialComponents_DayNight_Dialog_Bridge 0x7f11024a
int style Theme_MaterialComponents_DayNight_Dialog_FixedSize 0x7f11024b
int style Theme_MaterialComponents_DayNight_Dialog_FixedSize_Bridge 0x7f11024c
int style Theme_MaterialComponents_DayNight_Dialog_MinWidth 0x7f11024d
int style Theme_MaterialComponents_DayNight_Dialog_MinWidth_Bridge 0x7f11024e
int style Theme_MaterialComponents_DayNight_DialogWhenLarge 0x7f11024f
int style Theme_MaterialComponents_DayNight_NoActionBar 0x7f110250
int style Theme_MaterialComponents_DayNight_NoActionBar_Bridge 0x7f110251
int style Theme_MaterialComponents_Dialog 0x7f110252
int style Theme_MaterialComponents_Dialog_Alert 0x7f110253
int style Theme_MaterialComponents_Dialog_Alert_Bridge 0x7f110254
int style Theme_MaterialComponents_Dialog_Bridge 0x7f110255
int style Theme_MaterialComponents_Dialog_FixedSize 0x7f110256
int style Theme_MaterialComponents_Dialog_FixedSize_Bridge 0x7f110257
int style Theme_MaterialComponents_Dialog_MinWidth 0x7f110258
int style Theme_MaterialComponents_Dialog_MinWidth_Bridge 0x7f110259
int style Theme_MaterialComponents_DialogWhenLarge 0x7f11025a
int style Theme_MaterialComponents_Light 0x7f11025b
int style Theme_MaterialComponents_Light_BottomSheetDialog 0x7f11025c
int style Theme_MaterialComponents_Light_Bridge 0x7f11025d
int style Theme_MaterialComponents_Light_DarkActionBar 0x7f11025e
int style Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f11025f
int style Theme_MaterialComponents_Light_Dialog 0x7f110260
int style Theme_MaterialComponents_Light_Dialog_Alert 0x7f110261
int style Theme_MaterialComponents_Light_Dialog_Alert_Bridge 0x7f110262
int style Theme_MaterialComponents_Light_Dialog_Bridge 0x7f110263
int style Theme_MaterialComponents_Light_Dialog_FixedSize 0x7f110264
int style Theme_MaterialComponents_Light_Dialog_FixedSize_Bridge 0x7f110265
int style Theme_MaterialComponents_Light_Dialog_MinWidth 0x7f110266
int style Theme_MaterialComponents_Light_Dialog_MinWidth_Bridge 0x7f110267
int style Theme_MaterialComponents_Light_DialogWhenLarge 0x7f110268
int style Theme_MaterialComponents_Light_NoActionBar 0x7f110269
int style Theme_MaterialComponents_Light_NoActionBar_Bridge 0x7f11026a
int style Theme_MaterialComponents_NoActionBar 0x7f11026b
int style Theme_MaterialComponents_NoActionBar_Bridge 0x7f11026c
int style Theme_MountainSurvival 0x7f11026d
int style ThemeOverlay_AppCompat 0x7f11026e
int style ThemeOverlay_AppCompat_ActionBar 0x7f11026f
int style ThemeOverlay_AppCompat_Dark 0x7f110270
int style ThemeOverlay_AppCompat_Dark_ActionBar 0x7f110271
int style ThemeOverlay_AppCompat_DayNight 0x7f110272
int style ThemeOverlay_AppCompat_DayNight_ActionBar 0x7f110273
int style ThemeOverlay_AppCompat_Dialog 0x7f110274
int style ThemeOverlay_AppCompat_Dialog_Alert 0x7f110275
int style ThemeOverlay_AppCompat_Light 0x7f110276
int style ThemeOverlay_Design_TextInputEditText 0x7f110277
int style ThemeOverlay_Material3 0x7f110278
int style ThemeOverlay_Material3_ActionBar 0x7f110279
int style ThemeOverlay_Material3_AutoCompleteTextView 0x7f11027a
int style ThemeOverlay_Material3_AutoCompleteTextView_FilledBox 0x7f11027b
int style ThemeOverlay_Material3_AutoCompleteTextView_FilledBox_Dense 0x7f11027c
int style ThemeOverlay_Material3_AutoCompleteTextView_OutlinedBox 0x7f11027d
int style ThemeOverlay_Material3_AutoCompleteTextView_OutlinedBox_Dense 0x7f11027e
int style ThemeOverlay_Material3_BottomAppBar 0x7f11027f
int style ThemeOverlay_Material3_BottomAppBar_Legacy 0x7f110280
int style ThemeOverlay_Material3_BottomSheetDialog 0x7f110281
int style ThemeOverlay_Material3_Button 0x7f110282
int style ThemeOverlay_Material3_Button_ElevatedButton 0x7f110283
int style ThemeOverlay_Material3_Button_IconButton 0x7f110284
int style ThemeOverlay_Material3_Button_IconButton_Filled 0x7f110285
int style ThemeOverlay_Material3_Button_IconButton_Filled_Tonal 0x7f110286
int style ThemeOverlay_Material3_Button_TextButton 0x7f110287
int style ThemeOverlay_Material3_Button_TextButton_Snackbar 0x7f110288
int style ThemeOverlay_Material3_Button_TonalButton 0x7f110289
int style ThemeOverlay_Material3_Chip 0x7f11028a
int style ThemeOverlay_Material3_Chip_Assist 0x7f11028b
int style ThemeOverlay_Material3_Dark 0x7f11028c
int style ThemeOverlay_Material3_Dark_ActionBar 0x7f11028d
int style ThemeOverlay_Material3_DayNight_BottomSheetDialog 0x7f11028e
int style ThemeOverlay_Material3_DayNight_SideSheetDialog 0x7f11028f
int style ThemeOverlay_Material3_Dialog 0x7f110290
int style ThemeOverlay_Material3_Dialog_Alert 0x7f110291
int style ThemeOverlay_Material3_Dialog_Alert_Framework 0x7f110292
int style ThemeOverlay_Material3_DynamicColors_Dark 0x7f110293
int style ThemeOverlay_Material3_DynamicColors_DayNight 0x7f110294
int style ThemeOverlay_Material3_DynamicColors_Light 0x7f110295
int style ThemeOverlay_Material3_ExtendedFloatingActionButton_Primary 0x7f110296
int style ThemeOverlay_Material3_ExtendedFloatingActionButton_Secondary 0x7f110297
int style ThemeOverlay_Material3_ExtendedFloatingActionButton_Surface 0x7f110298
int style ThemeOverlay_Material3_ExtendedFloatingActionButton_Tertiary 0x7f110299
int style ThemeOverlay_Material3_FloatingActionButton_Primary 0x7f11029a
int style ThemeOverlay_Material3_FloatingActionButton_Secondary 0x7f11029b
int style ThemeOverlay_Material3_FloatingActionButton_Surface 0x7f11029c
int style ThemeOverlay_Material3_FloatingActionButton_Tertiary 0x7f11029d
int style ThemeOverlay_Material3_HarmonizedColors 0x7f11029e
int style ThemeOverlay_Material3_HarmonizedColors_Empty 0x7f11029f
int style ThemeOverlay_Material3_Light 0x7f1102a0
int style ThemeOverlay_Material3_Light_Dialog_Alert_Framework 0x7f1102a1
int style ThemeOverlay_Material3_MaterialAlertDialog 0x7f1102a2
int style ThemeOverlay_Material3_MaterialAlertDialog_Centered 0x7f1102a3
int style ThemeOverlay_Material3_MaterialCalendar 0x7f1102a4
int style ThemeOverlay_Material3_MaterialCalendar_Fullscreen 0x7f1102a5
int style ThemeOverlay_Material3_MaterialCalendar_HeaderCancelButton 0x7f1102a6
int style ThemeOverlay_Material3_MaterialTimePicker 0x7f1102a7
int style ThemeOverlay_Material3_MaterialTimePicker_Display_TextInputEditText 0x7f1102a8
int style ThemeOverlay_Material3_NavigationView 0x7f1102a9
int style ThemeOverlay_Material3_PersonalizedColors 0x7f1102aa
int style ThemeOverlay_Material3_Search 0x7f1102ab
int style ThemeOverlay_Material3_SideSheetDialog 0x7f1102ac
int style ThemeOverlay_Material3_Snackbar 0x7f1102ad
int style ThemeOverlay_Material3_TextInputEditText 0x7f1102ae
int style ThemeOverlay_Material3_TextInputEditText_FilledBox 0x7f1102af
int style ThemeOverlay_Material3_TextInputEditText_FilledBox_Dense 0x7f1102b0
int style ThemeOverlay_Material3_TextInputEditText_OutlinedBox 0x7f1102b1
int style ThemeOverlay_Material3_TextInputEditText_OutlinedBox_Dense 0x7f1102b2
int style ThemeOverlay_Material3_Toolbar_Surface 0x7f1102b3
int style ThemeOverlay_MaterialAlertDialog_Material3_Title_Icon 0x7f1102b4
int style ThemeOverlay_MaterialComponents 0x7f1102b5
int style ThemeOverlay_MaterialComponents_ActionBar 0x7f1102b6
int style ThemeOverlay_MaterialComponents_ActionBar_Primary 0x7f1102b7
int style ThemeOverlay_MaterialComponents_ActionBar_Surface 0x7f1102b8
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView 0x7f1102b9
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox 0x7f1102ba
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox_Dense 0x7f1102bb
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox 0x7f1102bc
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense 0x7f1102bd
int style ThemeOverlay_MaterialComponents_BottomAppBar_Primary 0x7f1102be
int style ThemeOverlay_MaterialComponents_BottomAppBar_Surface 0x7f1102bf
int style ThemeOverlay_MaterialComponents_BottomSheetDialog 0x7f1102c0
int style ThemeOverlay_MaterialComponents_Dark 0x7f1102c1
int style ThemeOverlay_MaterialComponents_Dark_ActionBar 0x7f1102c2
int style ThemeOverlay_MaterialComponents_DayNight_BottomSheetDialog 0x7f1102c3
int style ThemeOverlay_MaterialComponents_Dialog 0x7f1102c4
int style ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f1102c5
int style ThemeOverlay_MaterialComponents_Dialog_Alert_Framework 0x7f1102c6
int style ThemeOverlay_MaterialComponents_Light 0x7f1102c7
int style ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework 0x7f1102c8
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x7f1102c9
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Centered 0x7f1102ca
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date 0x7f1102cb
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Calendar 0x7f1102cc
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text 0x7f1102cd
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text_Day 0x7f1102ce
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Spinner 0x7f1102cf
int style ThemeOverlay_MaterialComponents_MaterialCalendar 0x7f1102d0
int style ThemeOverlay_MaterialComponents_MaterialCalendar_Fullscreen 0x7f1102d1
int style ThemeOverlay_MaterialComponents_TextInputEditText 0x7f1102d2
int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox 0x7f1102d3
int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox_Dense 0x7f1102d4
int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox 0x7f1102d5
int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x7f1102d6
int style ThemeOverlay_MaterialComponents_TimePicker 0x7f1102d7
int style ThemeOverlay_MaterialComponents_TimePicker_Display 0x7f1102d8
int style ThemeOverlay_MaterialComponents_TimePicker_Display_TextInputEditText 0x7f1102d9
int style ThemeOverlay_MaterialComponents_Toolbar_Popup_Primary 0x7f1102da
int style ThemeOverlay_MaterialComponents_Toolbar_Primary 0x7f1102db
int style ThemeOverlay_MaterialComponents_Toolbar_Surface 0x7f1102dc
int style Widget_AppCompat_ActionBar 0x7f1102dd
int style Widget_AppCompat_ActionBar_Solid 0x7f1102de
int style Widget_AppCompat_ActionBar_TabBar 0x7f1102df
int style Widget_AppCompat_ActionBar_TabText 0x7f1102e0
int style Widget_AppCompat_ActionBar_TabView 0x7f1102e1
int style Widget_AppCompat_ActionButton 0x7f1102e2
int style Widget_AppCompat_ActionButton_CloseMode 0x7f1102e3
int style Widget_AppCompat_ActionButton_Overflow 0x7f1102e4
int style Widget_AppCompat_ActionMode 0x7f1102e5
int style Widget_AppCompat_ActivityChooserView 0x7f1102e6
int style Widget_AppCompat_AutoCompleteTextView 0x7f1102e7
int style Widget_AppCompat_Button 0x7f1102e8
int style Widget_AppCompat_Button_Borderless 0x7f1102e9
int style Widget_AppCompat_Button_Borderless_Colored 0x7f1102ea
int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f1102eb
int style Widget_AppCompat_Button_Colored 0x7f1102ec
int style Widget_AppCompat_Button_Small 0x7f1102ed
int style Widget_AppCompat_ButtonBar 0x7f1102ee
int style Widget_AppCompat_ButtonBar_AlertDialog 0x7f1102ef
int style Widget_AppCompat_CompoundButton_CheckBox 0x7f1102f0
int style Widget_AppCompat_CompoundButton_RadioButton 0x7f1102f1
int style Widget_AppCompat_CompoundButton_Switch 0x7f1102f2
int style Widget_AppCompat_DrawerArrowToggle 0x7f1102f3
int style Widget_AppCompat_DropDownItem_Spinner 0x7f1102f4
int style Widget_AppCompat_EditText 0x7f1102f5
int style Widget_AppCompat_ImageButton 0x7f1102f6
int style Widget_AppCompat_Light_ActionBar 0x7f1102f7
int style Widget_AppCompat_Light_ActionBar_Solid 0x7f1102f8
int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x7f1102f9
int style Widget_AppCompat_Light_ActionBar_TabBar 0x7f1102fa
int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x7f1102fb
int style Widget_AppCompat_Light_ActionBar_TabText 0x7f1102fc
int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f1102fd
int style Widget_AppCompat_Light_ActionBar_TabView 0x7f1102fe
int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x7f1102ff
int style Widget_AppCompat_Light_ActionButton 0x7f110300
int style Widget_AppCompat_Light_ActionButton_CloseMode 0x7f110301
int style Widget_AppCompat_Light_ActionButton_Overflow 0x7f110302
int style Widget_AppCompat_Light_ActionMode_Inverse 0x7f110303
int style Widget_AppCompat_Light_ActivityChooserView 0x7f110304
int style Widget_AppCompat_Light_AutoCompleteTextView 0x7f110305
int style Widget_AppCompat_Light_DropDownItem_Spinner 0x7f110306
int style Widget_AppCompat_Light_ListPopupWindow 0x7f110307
int style Widget_AppCompat_Light_ListView_DropDown 0x7f110308
int style Widget_AppCompat_Light_PopupMenu 0x7f110309
int style Widget_AppCompat_Light_PopupMenu_Overflow 0x7f11030a
int style Widget_AppCompat_Light_SearchView 0x7f11030b
int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x7f11030c
int style Widget_AppCompat_ListMenuView 0x7f11030d
int style Widget_AppCompat_ListPopupWindow 0x7f11030e
int style Widget_AppCompat_ListView 0x7f11030f
int style Widget_AppCompat_ListView_DropDown 0x7f110310
int style Widget_AppCompat_ListView_Menu 0x7f110311
int style Widget_AppCompat_PopupMenu 0x7f110312
int style Widget_AppCompat_PopupMenu_Overflow 0x7f110313
int style Widget_AppCompat_PopupWindow 0x7f110314
int style Widget_AppCompat_ProgressBar 0x7f110315
int style Widget_AppCompat_ProgressBar_Horizontal 0x7f110316
int style Widget_AppCompat_RatingBar 0x7f110317
int style Widget_AppCompat_RatingBar_Indicator 0x7f110318
int style Widget_AppCompat_RatingBar_Small 0x7f110319
int style Widget_AppCompat_SearchView 0x7f11031a
int style Widget_AppCompat_SearchView_ActionBar 0x7f11031b
int style Widget_AppCompat_SeekBar 0x7f11031c
int style Widget_AppCompat_SeekBar_Discrete 0x7f11031d
int style Widget_AppCompat_Spinner 0x7f11031e
int style Widget_AppCompat_Spinner_DropDown 0x7f11031f
int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x7f110320
int style Widget_AppCompat_Spinner_Underlined 0x7f110321
int style Widget_AppCompat_TextView 0x7f110322
int style Widget_AppCompat_TextView_SpinnerItem 0x7f110323
int style Widget_AppCompat_Toolbar 0x7f110324
int style Widget_AppCompat_Toolbar_Button_Navigation 0x7f110325
int style Widget_Compat_NotificationActionContainer 0x7f110326
int style Widget_Compat_NotificationActionText 0x7f110327
int style Widget_Design_AppBarLayout 0x7f110328
int style Widget_Design_BottomNavigationView 0x7f110329
int style Widget_Design_BottomSheet_Modal 0x7f11032a
int style Widget_Design_CollapsingToolbar 0x7f11032b
int style Widget_Design_FloatingActionButton 0x7f11032c
int style Widget_Design_NavigationView 0x7f11032d
int style Widget_Design_ScrimInsetsFrameLayout 0x7f11032e
int style Widget_Design_Snackbar 0x7f11032f
int style Widget_Design_TabLayout 0x7f110330
int style Widget_Design_TextInputEditText 0x7f110331
int style Widget_Design_TextInputLayout 0x7f110332
int style Widget_Material3_ActionBar_Solid 0x7f110333
int style Widget_Material3_ActionMode 0x7f110334
int style Widget_Material3_AppBarLayout 0x7f110335
int style Widget_Material3_AutoCompleteTextView_FilledBox 0x7f110336
int style Widget_Material3_AutoCompleteTextView_FilledBox_Dense 0x7f110337
int style Widget_Material3_AutoCompleteTextView_OutlinedBox 0x7f110338
int style Widget_Material3_AutoCompleteTextView_OutlinedBox_Dense 0x7f110339
int style Widget_Material3_Badge 0x7f11033a
int style Widget_Material3_BottomAppBar 0x7f11033b
int style Widget_Material3_BottomAppBar_Button_Navigation 0x7f11033c
int style Widget_Material3_BottomAppBar_Legacy 0x7f11033d
int style Widget_Material3_BottomNavigationView 0x7f11033e
int style Widget_Material3_BottomNavigationView_ActiveIndicator 0x7f11033f
int style Widget_Material3_BottomSheet 0x7f110340
int style Widget_Material3_BottomSheet_DragHandle 0x7f110341
int style Widget_Material3_BottomSheet_Modal 0x7f110342
int style Widget_Material3_Button 0x7f110343
int style Widget_Material3_Button_ElevatedButton 0x7f110344
int style Widget_Material3_Button_ElevatedButton_Icon 0x7f110345
int style Widget_Material3_Button_Icon 0x7f110346
int style Widget_Material3_Button_IconButton 0x7f110347
int style Widget_Material3_Button_IconButton_Filled 0x7f110348
int style Widget_Material3_Button_IconButton_Filled_Tonal 0x7f110349
int style Widget_Material3_Button_IconButton_Outlined 0x7f11034a
int style Widget_Material3_Button_OutlinedButton 0x7f11034b
int style Widget_Material3_Button_OutlinedButton_Icon 0x7f11034c
int style Widget_Material3_Button_TextButton 0x7f11034d
int style Widget_Material3_Button_TextButton_Dialog 0x7f11034e
int style Widget_Material3_Button_TextButton_Dialog_Flush 0x7f11034f
int style Widget_Material3_Button_TextButton_Dialog_Icon 0x7f110350
int style Widget_Material3_Button_TextButton_Icon 0x7f110351
int style Widget_Material3_Button_TextButton_Snackbar 0x7f110352
int style Widget_Material3_Button_TonalButton 0x7f110353
int style Widget_Material3_Button_TonalButton_Icon 0x7f110354
int style Widget_Material3_Button_UnelevatedButton 0x7f110355
int style Widget_Material3_CardView_Elevated 0x7f110356
int style Widget_Material3_CardView_Filled 0x7f110357
int style Widget_Material3_CardView_Outlined 0x7f110358
int style Widget_Material3_CheckedTextView 0x7f110359
int style Widget_Material3_Chip_Assist 0x7f11035a
int style Widget_Material3_Chip_Assist_Elevated 0x7f11035b
int style Widget_Material3_Chip_Filter 0x7f11035c
int style Widget_Material3_Chip_Filter_Elevated 0x7f11035d
int style Widget_Material3_Chip_Input 0x7f11035e
int style Widget_Material3_Chip_Input_Elevated 0x7f11035f
int style Widget_Material3_Chip_Input_Icon 0x7f110360
int style Widget_Material3_Chip_Input_Icon_Elevated 0x7f110361
int style Widget_Material3_Chip_Suggestion 0x7f110362
int style Widget_Material3_Chip_Suggestion_Elevated 0x7f110363
int style Widget_Material3_ChipGroup 0x7f110364
int style Widget_Material3_CircularProgressIndicator 0x7f110365
int style Widget_Material3_CircularProgressIndicator_ExtraSmall 0x7f110366
int style Widget_Material3_CircularProgressIndicator_Medium 0x7f110367
int style Widget_Material3_CircularProgressIndicator_Small 0x7f110368
int style Widget_Material3_CollapsingToolbar 0x7f110369
int style Widget_Material3_CollapsingToolbar_Large 0x7f11036a
int style Widget_Material3_CollapsingToolbar_Medium 0x7f11036b
int style Widget_Material3_CompoundButton_CheckBox 0x7f11036c
int style Widget_Material3_CompoundButton_MaterialSwitch 0x7f11036d
int style Widget_Material3_CompoundButton_RadioButton 0x7f11036e
int style Widget_Material3_CompoundButton_Switch 0x7f11036f
int style Widget_Material3_DrawerLayout 0x7f110370
int style Widget_Material3_ExtendedFloatingActionButton_Icon_Primary 0x7f110371
int style Widget_Material3_ExtendedFloatingActionButton_Icon_Secondary 0x7f110372
int style Widget_Material3_ExtendedFloatingActionButton_Icon_Surface 0x7f110373
int style Widget_Material3_ExtendedFloatingActionButton_Icon_Tertiary 0x7f110374
int style Widget_Material3_ExtendedFloatingActionButton_Primary 0x7f110375
int style Widget_Material3_ExtendedFloatingActionButton_Secondary 0x7f110376
int style Widget_Material3_ExtendedFloatingActionButton_Surface 0x7f110377
int style Widget_Material3_ExtendedFloatingActionButton_Tertiary 0x7f110378
int style Widget_Material3_FloatingActionButton_Large_Primary 0x7f110379
int style Widget_Material3_FloatingActionButton_Large_Secondary 0x7f11037a
int style Widget_Material3_FloatingActionButton_Large_Surface 0x7f11037b
int style Widget_Material3_FloatingActionButton_Large_Tertiary 0x7f11037c
int style Widget_Material3_FloatingActionButton_Primary 0x7f11037d
int style Widget_Material3_FloatingActionButton_Secondary 0x7f11037e
int style Widget_Material3_FloatingActionButton_Small_Primary 0x7f11037f
int style Widget_Material3_FloatingActionButton_Small_Secondary 0x7f110380
int style Widget_Material3_FloatingActionButton_Small_Surface 0x7f110381
int style Widget_Material3_FloatingActionButton_Small_Tertiary 0x7f110382
int style Widget_Material3_FloatingActionButton_Surface 0x7f110383
int style Widget_Material3_FloatingActionButton_Tertiary 0x7f110384
int style Widget_Material3_Light_ActionBar_Solid 0x7f110385
int style Widget_Material3_LinearProgressIndicator 0x7f110386
int style Widget_Material3_MaterialButtonToggleGroup 0x7f110387
int style Widget_Material3_MaterialCalendar 0x7f110388
int style Widget_Material3_MaterialCalendar_Day 0x7f110389
int style Widget_Material3_MaterialCalendar_Day_Invalid 0x7f11038a
int style Widget_Material3_MaterialCalendar_Day_Selected 0x7f11038b
int style Widget_Material3_MaterialCalendar_Day_Today 0x7f11038c
int style Widget_Material3_MaterialCalendar_DayOfWeekLabel 0x7f11038d
int style Widget_Material3_MaterialCalendar_DayTextView 0x7f11038e
int style Widget_Material3_MaterialCalendar_Fullscreen 0x7f11038f
int style Widget_Material3_MaterialCalendar_HeaderCancelButton 0x7f110390
int style Widget_Material3_MaterialCalendar_HeaderDivider 0x7f110391
int style Widget_Material3_MaterialCalendar_HeaderLayout 0x7f110392
int style Widget_Material3_MaterialCalendar_HeaderLayout_Fullscreen 0x7f110393
int style Widget_Material3_MaterialCalendar_HeaderSelection 0x7f110394
int style Widget_Material3_MaterialCalendar_HeaderSelection_Fullscreen 0x7f110395
int style Widget_Material3_MaterialCalendar_HeaderTitle 0x7f110396
int style Widget_Material3_MaterialCalendar_HeaderToggleButton 0x7f110397
int style Widget_Material3_MaterialCalendar_Item 0x7f110398
int style Widget_Material3_MaterialCalendar_MonthNavigationButton 0x7f110399
int style Widget_Material3_MaterialCalendar_MonthTextView 0x7f11039a
int style Widget_Material3_MaterialCalendar_Year 0x7f11039b
int style Widget_Material3_MaterialCalendar_Year_Selected 0x7f11039c
int style Widget_Material3_MaterialCalendar_Year_Today 0x7f11039d
int style Widget_Material3_MaterialCalendar_YearNavigationButton 0x7f11039e
int style Widget_Material3_MaterialDivider 0x7f11039f
int style Widget_Material3_MaterialDivider_Heavy 0x7f1103a0
int style Widget_Material3_MaterialTimePicker 0x7f1103a1
int style Widget_Material3_MaterialTimePicker_Button 0x7f1103a2
int style Widget_Material3_MaterialTimePicker_Clock 0x7f1103a3
int style Widget_Material3_MaterialTimePicker_Display 0x7f1103a4
int style Widget_Material3_MaterialTimePicker_Display_Divider 0x7f1103a5
int style Widget_Material3_MaterialTimePicker_Display_HelperText 0x7f1103a6
int style Widget_Material3_MaterialTimePicker_Display_TextInputEditText 0x7f1103a7
int style Widget_Material3_MaterialTimePicker_Display_TextInputLayout 0x7f1103a8
int style Widget_Material3_MaterialTimePicker_ImageButton 0x7f1103a9
int style Widget_Material3_NavigationRailView 0x7f1103aa
int style Widget_Material3_NavigationRailView_ActiveIndicator 0x7f1103ab
int style Widget_Material3_NavigationView 0x7f1103ac
int style Widget_Material3_PopupMenu 0x7f1103ad
int style Widget_Material3_PopupMenu_ContextMenu 0x7f1103ae
int style Widget_Material3_PopupMenu_ListPopupWindow 0x7f1103af
int style Widget_Material3_PopupMenu_Overflow 0x7f1103b0
int style Widget_Material3_Search_ActionButton_Overflow 0x7f1103b1
int style Widget_Material3_Search_Toolbar_Button_Navigation 0x7f1103b2
int style Widget_Material3_SearchBar 0x7f1103b3
int style Widget_Material3_SearchBar_Outlined 0x7f1103b4
int style Widget_Material3_SearchView 0x7f1103b5
int style Widget_Material3_SearchView_Prefix 0x7f1103b6
int style Widget_Material3_SearchView_Toolbar 0x7f1103b7
int style Widget_Material3_SideSheet 0x7f1103b8
int style Widget_Material3_SideSheet_Detached 0x7f1103b9
int style Widget_Material3_SideSheet_Modal 0x7f1103ba
int style Widget_Material3_SideSheet_Modal_Detached 0x7f1103bb
int style Widget_Material3_Slider 0x7f1103bc
int style Widget_Material3_Slider_Label 0x7f1103bd
int style Widget_Material3_Snackbar 0x7f1103be
int style Widget_Material3_Snackbar_FullWidth 0x7f1103bf
int style Widget_Material3_Snackbar_TextView 0x7f1103c0
int style Widget_Material3_TabLayout 0x7f1103c1
int style Widget_Material3_TabLayout_OnSurface 0x7f1103c2
int style Widget_Material3_TabLayout_Secondary 0x7f1103c3
int style Widget_Material3_TextInputEditText_FilledBox 0x7f1103c4
int style Widget_Material3_TextInputEditText_FilledBox_Dense 0x7f1103c5
int style Widget_Material3_TextInputEditText_OutlinedBox 0x7f1103c6
int style Widget_Material3_TextInputEditText_OutlinedBox_Dense 0x7f1103c7
int style Widget_Material3_TextInputLayout_FilledBox 0x7f1103c8
int style Widget_Material3_TextInputLayout_FilledBox_Dense 0x7f1103c9
int style Widget_Material3_TextInputLayout_FilledBox_Dense_ExposedDropdownMenu 0x7f1103ca
int style Widget_Material3_TextInputLayout_FilledBox_ExposedDropdownMenu 0x7f1103cb
int style Widget_Material3_TextInputLayout_OutlinedBox 0x7f1103cc
int style Widget_Material3_TextInputLayout_OutlinedBox_Dense 0x7f1103cd
int style Widget_Material3_TextInputLayout_OutlinedBox_Dense_ExposedDropdownMenu 0x7f1103ce
int style Widget_Material3_TextInputLayout_OutlinedBox_ExposedDropdownMenu 0x7f1103cf
int style Widget_Material3_Toolbar 0x7f1103d0
int style Widget_Material3_Toolbar_OnSurface 0x7f1103d1
int style Widget_Material3_Toolbar_Surface 0x7f1103d2
int style Widget_Material3_Tooltip 0x7f1103d3
int style Widget_MaterialComponents_ActionBar_Primary 0x7f1103d4
int style Widget_MaterialComponents_ActionBar_PrimarySurface 0x7f1103d5
int style Widget_MaterialComponents_ActionBar_Solid 0x7f1103d6
int style Widget_MaterialComponents_ActionBar_Surface 0x7f1103d7
int style Widget_MaterialComponents_ActionMode 0x7f1103d8
int style Widget_MaterialComponents_AppBarLayout_Primary 0x7f1103d9
int style Widget_MaterialComponents_AppBarLayout_PrimarySurface 0x7f1103da
int style Widget_MaterialComponents_AppBarLayout_Surface 0x7f1103db
int style Widget_MaterialComponents_AutoCompleteTextView_FilledBox 0x7f1103dc
int style Widget_MaterialComponents_AutoCompleteTextView_FilledBox_Dense 0x7f1103dd
int style Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox 0x7f1103de
int style Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense 0x7f1103df
int style Widget_MaterialComponents_Badge 0x7f1103e0
int style Widget_MaterialComponents_BottomAppBar 0x7f1103e1
int style Widget_MaterialComponents_BottomAppBar_Colored 0x7f1103e2
int style Widget_MaterialComponents_BottomAppBar_PrimarySurface 0x7f1103e3
int style Widget_MaterialComponents_BottomNavigationView 0x7f1103e4
int style Widget_MaterialComponents_BottomNavigationView_Colored 0x7f1103e5
int style Widget_MaterialComponents_BottomNavigationView_PrimarySurface 0x7f1103e6
int style Widget_MaterialComponents_BottomSheet 0x7f1103e7
int style Widget_MaterialComponents_BottomSheet_Modal 0x7f1103e8
int style Widget_MaterialComponents_Button 0x7f1103e9
int style Widget_MaterialComponents_Button_Icon 0x7f1103ea
int style Widget_MaterialComponents_Button_OutlinedButton 0x7f1103eb
int style Widget_MaterialComponents_Button_OutlinedButton_Icon 0x7f1103ec
int style Widget_MaterialComponents_Button_TextButton 0x7f1103ed
int style Widget_MaterialComponents_Button_TextButton_Dialog 0x7f1103ee
int style Widget_MaterialComponents_Button_TextButton_Dialog_Flush 0x7f1103ef
int style Widget_MaterialComponents_Button_TextButton_Dialog_Icon 0x7f1103f0
int style Widget_MaterialComponents_Button_TextButton_Icon 0x7f1103f1
int style Widget_MaterialComponents_Button_TextButton_Snackbar 0x7f1103f2
int style Widget_MaterialComponents_Button_UnelevatedButton 0x7f1103f3
int style Widget_MaterialComponents_Button_UnelevatedButton_Icon 0x7f1103f4
int style Widget_MaterialComponents_CardView 0x7f1103f5
int style Widget_MaterialComponents_CheckedTextView 0x7f1103f6
int style Widget_MaterialComponents_Chip_Action 0x7f1103f7
int style Widget_MaterialComponents_Chip_Choice 0x7f1103f8
int style Widget_MaterialComponents_Chip_Entry 0x7f1103f9
int style Widget_MaterialComponents_Chip_Filter 0x7f1103fa
int style Widget_MaterialComponents_ChipGroup 0x7f1103fb
int style Widget_MaterialComponents_CircularProgressIndicator 0x7f1103fc
int style Widget_MaterialComponents_CircularProgressIndicator_ExtraSmall 0x7f1103fd
int style Widget_MaterialComponents_CircularProgressIndicator_Medium 0x7f1103fe
int style Widget_MaterialComponents_CircularProgressIndicator_Small 0x7f1103ff
int style Widget_MaterialComponents_CollapsingToolbar 0x7f110400
int style Widget_MaterialComponents_CompoundButton_CheckBox 0x7f110401
int style Widget_MaterialComponents_CompoundButton_RadioButton 0x7f110402
int style Widget_MaterialComponents_CompoundButton_Switch 0x7f110403
int style Widget_MaterialComponents_ExtendedFloatingActionButton 0x7f110404
int style Widget_MaterialComponents_ExtendedFloatingActionButton_Icon 0x7f110405
int style Widget_MaterialComponents_FloatingActionButton 0x7f110406
int style Widget_MaterialComponents_Light_ActionBar_Solid 0x7f110407
int style Widget_MaterialComponents_LinearProgressIndicator 0x7f110408
int style Widget_MaterialComponents_MaterialButtonToggleGroup 0x7f110409
int style Widget_MaterialComponents_MaterialCalendar 0x7f11040a
int style Widget_MaterialComponents_MaterialCalendar_Day 0x7f11040b
int style Widget_MaterialComponents_MaterialCalendar_Day_Invalid 0x7f11040c
int style Widget_MaterialComponents_MaterialCalendar_Day_Selected 0x7f11040d
int style Widget_MaterialComponents_MaterialCalendar_Day_Today 0x7f11040e
int style Widget_MaterialComponents_MaterialCalendar_DayOfWeekLabel 0x7f11040f
int style Widget_MaterialComponents_MaterialCalendar_DayTextView 0x7f110410
int style Widget_MaterialComponents_MaterialCalendar_Fullscreen 0x7f110411
int style Widget_MaterialComponents_MaterialCalendar_HeaderCancelButton 0x7f110412
int style Widget_MaterialComponents_MaterialCalendar_HeaderConfirmButton 0x7f110413
int style Widget_MaterialComponents_MaterialCalendar_HeaderDivider 0x7f110414
int style Widget_MaterialComponents_MaterialCalendar_HeaderLayout 0x7f110415
int style Widget_MaterialComponents_MaterialCalendar_HeaderLayout_Fullscreen 0x7f110416
int style Widget_MaterialComponents_MaterialCalendar_HeaderSelection 0x7f110417
int style Widget_MaterialComponents_MaterialCalendar_HeaderSelection_Fullscreen 0x7f110418
int style Widget_MaterialComponents_MaterialCalendar_HeaderTitle 0x7f110419
int style Widget_MaterialComponents_MaterialCalendar_HeaderToggleButton 0x7f11041a
int style Widget_MaterialComponents_MaterialCalendar_Item 0x7f11041b
int style Widget_MaterialComponents_MaterialCalendar_MonthNavigationButton 0x7f11041c
int style Widget_MaterialComponents_MaterialCalendar_MonthTextView 0x7f11041d
int style Widget_MaterialComponents_MaterialCalendar_Year 0x7f11041e
int style Widget_MaterialComponents_MaterialCalendar_Year_Selected 0x7f11041f
int style Widget_MaterialComponents_MaterialCalendar_Year_Today 0x7f110420
int style Widget_MaterialComponents_MaterialCalendar_YearNavigationButton 0x7f110421
int style Widget_MaterialComponents_MaterialDivider 0x7f110422
int style Widget_MaterialComponents_NavigationRailView 0x7f110423
int style Widget_MaterialComponents_NavigationRailView_Colored 0x7f110424
int style Widget_MaterialComponents_NavigationRailView_Colored_Compact 0x7f110425
int style Widget_MaterialComponents_NavigationRailView_Compact 0x7f110426
int style Widget_MaterialComponents_NavigationRailView_PrimarySurface 0x7f110427
int style Widget_MaterialComponents_NavigationView 0x7f110428
int style Widget_MaterialComponents_PopupMenu 0x7f110429
int style Widget_MaterialComponents_PopupMenu_ContextMenu 0x7f11042a
int style Widget_MaterialComponents_PopupMenu_ListPopupWindow 0x7f11042b
int style Widget_MaterialComponents_PopupMenu_Overflow 0x7f11042c
int style Widget_MaterialComponents_ProgressIndicator 0x7f11042d
int style Widget_MaterialComponents_ShapeableImageView 0x7f11042e
int style Widget_MaterialComponents_Slider 0x7f11042f
int style Widget_MaterialComponents_Snackbar 0x7f110430
int style Widget_MaterialComponents_Snackbar_FullWidth 0x7f110431
int style Widget_MaterialComponents_Snackbar_TextView 0x7f110432
int style Widget_MaterialComponents_TabLayout 0x7f110433
int style Widget_MaterialComponents_TabLayout_Colored 0x7f110434
int style Widget_MaterialComponents_TabLayout_PrimarySurface 0x7f110435
int style Widget_MaterialComponents_TextInputEditText_FilledBox 0x7f110436
int style Widget_MaterialComponents_TextInputEditText_FilledBox_Dense 0x7f110437
int style Widget_MaterialComponents_TextInputEditText_OutlinedBox 0x7f110438
int style Widget_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x7f110439
int style Widget_MaterialComponents_TextInputLayout_FilledBox 0x7f11043a
int style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense 0x7f11043b
int style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense_ExposedDropdownMenu 0x7f11043c
int style Widget_MaterialComponents_TextInputLayout_FilledBox_ExposedDropdownMenu 0x7f11043d
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox 0x7f11043e
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense 0x7f11043f
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense_ExposedDropdownMenu 0x7f110440
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_ExposedDropdownMenu 0x7f110441
int style Widget_MaterialComponents_TextView 0x7f110442
int style Widget_MaterialComponents_TimePicker 0x7f110443
int style Widget_MaterialComponents_TimePicker_Button 0x7f110444
int style Widget_MaterialComponents_TimePicker_Clock 0x7f110445
int style Widget_MaterialComponents_TimePicker_Display 0x7f110446
int style Widget_MaterialComponents_TimePicker_Display_Divider 0x7f110447
int style Widget_MaterialComponents_TimePicker_Display_HelperText 0x7f110448
int style Widget_MaterialComponents_TimePicker_Display_TextInputEditText 0x7f110449
int style Widget_MaterialComponents_TimePicker_Display_TextInputLayout 0x7f11044a
int style Widget_MaterialComponents_TimePicker_ImageButton 0x7f11044b
int style Widget_MaterialComponents_TimePicker_ImageButton_ShapeAppearance 0x7f11044c
int style Widget_MaterialComponents_Toolbar 0x7f11044d
int style Widget_MaterialComponents_Toolbar_Primary 0x7f11044e
int style Widget_MaterialComponents_Toolbar_PrimarySurface 0x7f11044f
int style Widget_MaterialComponents_Toolbar_Surface 0x7f110450
int style Widget_MaterialComponents_Tooltip 0x7f110451
int style Widget_Support_CoordinatorLayout 0x7f110452
int[] styleable ActionBar { 0x7f030044, 0x7f03004b, 0x7f03004c, 0x7f030134, 0x7f030135, 0x7f030136, 0x7f030137, 0x7f030138, 0x7f030139, 0x7f030160, 0x7f030173, 0x7f030174, 0x7f030193, 0x7f030209, 0x7f030211, 0x7f030217, 0x7f030218, 0x7f03021c, 0x7f03022d, 0x7f030243, 0x7f0302be, 0x7f03033e, 0x7f03036f, 0x7f030376, 0x7f030377, 0x7f0303ed, 0x7f0303f1, 0x7f030475, 0x7f030483 }
int styleable ActionBar_background 0
int styleable ActionBar_backgroundSplit 1
int styleable ActionBar_backgroundStacked 2
int styleable ActionBar_contentInsetEnd 3
int styleable ActionBar_contentInsetEndWithActions 4
int styleable ActionBar_contentInsetLeft 5
int styleable ActionBar_contentInsetRight 6
int styleable ActionBar_contentInsetStart 7
int styleable ActionBar_contentInsetStartWithNavigation 8
int styleable ActionBar_customNavigationLayout 9
int styleable ActionBar_displayOptions 10
int styleable ActionBar_divider 11
int styleable ActionBar_elevation 12
int styleable ActionBar_height 13
int styleable ActionBar_hideOnContentScroll 14
int styleable ActionBar_homeAsUpIndicator 15
int styleable ActionBar_homeLayout 16
int styleable ActionBar_icon 17
int styleable ActionBar_indeterminateProgressStyle 18
int styleable ActionBar_itemPadding 19
int styleable ActionBar_logo 20
int styleable ActionBar_navigationMode 21
int styleable ActionBar_popupTheme 22
int styleable ActionBar_progressBarPadding 23
int styleable ActionBar_progressBarStyle 24
int styleable ActionBar_subtitle 25
int styleable ActionBar_subtitleTextStyle 26
int styleable ActionBar_title 27
int styleable ActionBar_titleTextStyle 28
int[] styleable ActionBarLayout { 0x010100b3 }
int styleable ActionBarLayout_android_layout_gravity 0
int[] styleable ActionMenuItemView { 0x0101013f }
int styleable ActionMenuItemView_android_minWidth 0
int[] styleable ActionMenuView { }
int[] styleable ActionMode { 0x7f030044, 0x7f03004b, 0x7f0300e0, 0x7f030209, 0x7f0303f1, 0x7f030483 }
int styleable ActionMode_background 0
int styleable ActionMode_backgroundSplit 1
int styleable ActionMode_closeItemLayout 2
int styleable ActionMode_height 3
int styleable ActionMode_subtitleTextStyle 4
int styleable ActionMode_titleTextStyle 5
int[] styleable ActivityChooserView { 0x7f0301ae, 0x7f030233 }
int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
int styleable ActivityChooserView_initialActivityCount 1
int[] styleable AlertDialog { 0x010100f2, 0x7f03008d, 0x7f030090, 0x7f0302b3, 0x7f0302b4, 0x7f03033a, 0x7f0303b5, 0x7f0303bd }
int styleable AlertDialog_android_layout 0
int styleable AlertDialog_buttonIconDimen 1
int styleable AlertDialog_buttonPanelSideLayout 2
int styleable AlertDialog_listItemLayout 3
int styleable AlertDialog_listLayout 4
int styleable AlertDialog_multiChoiceItemLayout 5
int styleable AlertDialog_showTitle 6
int styleable AlertDialog_singleChoiceItemLayout 7
int[] styleable AnimatedStateListDrawableCompat { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable AnimatedStateListDrawableCompat_android_dither 0
int styleable AnimatedStateListDrawableCompat_android_visible 1
int styleable AnimatedStateListDrawableCompat_android_variablePadding 2
int styleable AnimatedStateListDrawableCompat_android_constantSize 3
int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 4
int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 5
int[] styleable AnimatedStateListDrawableItem { 0x010100d0, 0x01010199 }
int styleable AnimatedStateListDrawableItem_android_id 0
int styleable AnimatedStateListDrawableItem_android_drawable 1
int[] styleable AnimatedStateListDrawableTransition { 0x01010199, 0x01010449, 0x0101044a, 0x0101044b }
int styleable AnimatedStateListDrawableTransition_android_drawable 0
int styleable AnimatedStateListDrawableTransition_android_toId 1
int styleable AnimatedStateListDrawableTransition_android_fromId 2
int styleable AnimatedStateListDrawableTransition_android_reversible 3
int[] styleable AppBarLayout { 0x010100d4, 0x0101048f, 0x01010540, 0x7f030193, 0x7f0301af, 0x7f0302a8, 0x7f0302a9, 0x7f0302aa, 0x7f0303e3 }
int styleable AppBarLayout_android_background 0
int styleable AppBarLayout_android_touchscreenBlocksFocus 1
int styleable AppBarLayout_android_keyboardNavigationCluster 2
int styleable AppBarLayout_elevation 3
int styleable AppBarLayout_expanded 4
int styleable AppBarLayout_liftOnScroll 5
int styleable AppBarLayout_liftOnScrollColor 6
int styleable AppBarLayout_liftOnScrollTargetViewId 7
int styleable AppBarLayout_statusBarForeground 8
int[] styleable AppBarLayoutStates { 0x7f0303da, 0x7f0303db, 0x7f0303df, 0x7f0303e0 }
int styleable AppBarLayoutStates_state_collapsed 0
int styleable AppBarLayoutStates_state_collapsible 1
int styleable AppBarLayoutStates_state_liftable 2
int styleable AppBarLayoutStates_state_lifted 3
int[] styleable AppBarLayout_Layout { 0x7f0302a4, 0x7f0302a5, 0x7f0302a6 }
int styleable AppBarLayout_Layout_layout_scrollEffect 0
int styleable AppBarLayout_Layout_layout_scrollFlags 1
int styleable AppBarLayout_Layout_layout_scrollInterpolator 2
int[] styleable AppCompatEmojiHelper { }
int[] styleable AppCompatImageView { 0x01010119, 0x7f0303cf, 0x7f030472, 0x7f030473 }
int styleable AppCompatImageView_android_src 0
int styleable AppCompatImageView_srcCompat 1
int styleable AppCompatImageView_tint 2
int styleable AppCompatImageView_tintMode 3
int[] styleable AppCompatSeekBar { 0x01010142, 0x7f03046c, 0x7f03046d, 0x7f03046e }
int styleable AppCompatSeekBar_android_thumb 0
int styleable AppCompatSeekBar_tickMark 1
int styleable AppCompatSeekBar_tickMarkTint 2
int styleable AppCompatSeekBar_tickMarkTintMode 3
int[] styleable AppCompatTextHelper { 0x01010034, 0x0101016d, 0x0101016e, 0x0101016f, 0x01010170, 0x01010392, 0x01010393 }
int styleable AppCompatTextHelper_android_textAppearance 0
int styleable AppCompatTextHelper_android_drawableTop 1
int styleable AppCompatTextHelper_android_drawableBottom 2
int styleable AppCompatTextHelper_android_drawableLeft 3
int styleable AppCompatTextHelper_android_drawableRight 4
int styleable AppCompatTextHelper_android_drawableStart 5
int styleable AppCompatTextHelper_android_drawableEnd 6
int[] styleable AppCompatTextView { 0x01010034, 0x7f03003e, 0x7f03003f, 0x7f030040, 0x7f030041, 0x7f030042, 0x7f030180, 0x7f030181, 0x7f030182, 0x7f030183, 0x7f030185, 0x7f030186, 0x7f030187, 0x7f030188, 0x7f030197, 0x7f0301cf, 0x7f0301f3, 0x7f0301fc, 0x7f03025d, 0x7f0302ac, 0x7f03041b, 0x7f030452 }
int styleable AppCompatTextView_android_textAppearance 0
int styleable AppCompatTextView_autoSizeMaxTextSize 1
int styleable AppCompatTextView_autoSizeMinTextSize 2
int styleable AppCompatTextView_autoSizePresetSizes 3
int styleable AppCompatTextView_autoSizeStepGranularity 4
int styleable AppCompatTextView_autoSizeTextType 5
int styleable AppCompatTextView_drawableBottomCompat 6
int styleable AppCompatTextView_drawableEndCompat 7
int styleable AppCompatTextView_drawableLeftCompat 8
int styleable AppCompatTextView_drawableRightCompat 9
int styleable AppCompatTextView_drawableStartCompat 10
int styleable AppCompatTextView_drawableTint 11
int styleable AppCompatTextView_drawableTintMode 12
int styleable AppCompatTextView_drawableTopCompat 13
int styleable AppCompatTextView_emojiCompatEnabled 14
int styleable AppCompatTextView_firstBaselineToTopHeight 15
int styleable AppCompatTextView_fontFamily 16
int styleable AppCompatTextView_fontVariationSettings 17
int styleable AppCompatTextView_lastBaselineToBottomHeight 18
int styleable AppCompatTextView_lineHeight 19
int styleable AppCompatTextView_textAllCaps 20
int styleable AppCompatTextView_textLocale 21
int[] styleable AppCompatTheme { 0x01010057, 0x010100ae, 0x7f030002, 0x7f030003, 0x7f030004, 0x7f030005, 0x7f030006, 0x7f030007, 0x7f030008, 0x7f030009, 0x7f03000a, 0x7f03000b, 0x7f03000c, 0x7f03000d, 0x7f03000e, 0x7f030010, 0x7f030011, 0x7f030012, 0x7f030013, 0x7f030014, 0x7f030015, 0x7f030016, 0x7f030017, 0x7f030018, 0x7f030019, 0x7f03001a, 0x7f03001b, 0x7f03001c, 0x7f03001d, 0x7f03001e, 0x7f03001f, 0x7f030020, 0x7f030021, 0x7f030022, 0x7f030026, 0x7f030028, 0x7f030029, 0x7f03002a, 0x7f03002b, 0x7f03003c, 0x7f030072, 0x7f030085, 0x7f030086, 0x7f030087, 0x7f030088, 0x7f030089, 0x7f030091, 0x7f030092, 0x7f0300ac, 0x7f0300b7, 0x7f0300ed, 0x7f0300ee, 0x7f0300ef, 0x7f0300f1, 0x7f0300f2, 0x7f0300f3, 0x7f0300f4, 0x7f03010d, 0x7f03010f, 0x7f030124, 0x7f030143, 0x7f030170, 0x7f030171, 0x7f030172, 0x7f030176, 0x7f03017b, 0x7f03018c, 0x7f03018d, 0x7f030190, 0x7f030191, 0x7f030192, 0x7f030217, 0x7f030227, 0x7f0302af, 0x7f0302b0, 0x7f0302b1, 0x7f0302b2, 0x7f0302b5, 0x7f0302b6, 0x7f0302b7, 0x7f0302b8, 0x7f0302b9, 0x7f0302ba, 0x7f0302bb, 0x7f0302bc, 0x7f0302bd, 0x7f030358, 0x7f030359, 0x7f03035a, 0x7f03036e, 0x7f030370, 0x7f03037e, 0x7f030380, 0x7f030381, 0x7f030382, 0x7f03039a, 0x7f03039b, 0x7f03039c, 0x7f03039d, 0x7f0303c7, 0x7f0303c8, 0x7f0303f8, 0x7f030432, 0x7f030434, 0x7f030435, 0x7f030436, 0x7f030438, 0x7f030439, 0x7f03043a, 0x7f03043b, 0x7f030446, 0x7f030447, 0x7f030486, 0x7f030487, 0x7f030489, 0x7f03048a, 0x7f0304ae, 0x7f0304bc, 0x7f0304bd, 0x7f0304be, 0x7f0304bf, 0x7f0304c0, 0x7f0304c1, 0x7f0304c2, 0x7f0304c3, 0x7f0304c4, 0x7f0304c5 }
int styleable AppCompatTheme_android_windowIsFloating 0
int styleable AppCompatTheme_android_windowAnimationStyle 1
int styleable AppCompatTheme_actionBarDivider 2
int styleable AppCompatTheme_actionBarItemBackground 3
int styleable AppCompatTheme_actionBarPopupTheme 4
int styleable AppCompatTheme_actionBarSize 5
int styleable AppCompatTheme_actionBarSplitStyle 6
int styleable AppCompatTheme_actionBarStyle 7
int styleable AppCompatTheme_actionBarTabBarStyle 8
int styleable AppCompatTheme_actionBarTabStyle 9
int styleable AppCompatTheme_actionBarTabTextStyle 10
int styleable AppCompatTheme_actionBarTheme 11
int styleable AppCompatTheme_actionBarWidgetTheme 12
int styleable AppCompatTheme_actionButtonStyle 13
int styleable AppCompatTheme_actionDropDownStyle 14
int styleable AppCompatTheme_actionMenuTextAppearance 15
int styleable AppCompatTheme_actionMenuTextColor 16
int styleable AppCompatTheme_actionModeBackground 17
int styleable AppCompatTheme_actionModeCloseButtonStyle 18
int styleable AppCompatTheme_actionModeCloseContentDescription 19
int styleable AppCompatTheme_actionModeCloseDrawable 20
int styleable AppCompatTheme_actionModeCopyDrawable 21
int styleable AppCompatTheme_actionModeCutDrawable 22
int styleable AppCompatTheme_actionModeFindDrawable 23
int styleable AppCompatTheme_actionModePasteDrawable 24
int styleable AppCompatTheme_actionModePopupWindowStyle 25
int styleable AppCompatTheme_actionModeSelectAllDrawable 26
int styleable AppCompatTheme_actionModeShareDrawable 27
int styleable AppCompatTheme_actionModeSplitBackground 28
int styleable AppCompatTheme_actionModeStyle 29
int styleable AppCompatTheme_actionModeTheme 30
int styleable AppCompatTheme_actionModeWebSearchDrawable 31
int styleable AppCompatTheme_actionOverflowButtonStyle 32
int styleable AppCompatTheme_actionOverflowMenuStyle 33
int styleable AppCompatTheme_activityChooserViewStyle 34
int styleable AppCompatTheme_alertDialogButtonGroupStyle 35
int styleable AppCompatTheme_alertDialogCenterButtons 36
int styleable AppCompatTheme_alertDialogStyle 37
int styleable AppCompatTheme_alertDialogTheme 38
int styleable AppCompatTheme_autoCompleteTextViewStyle 39
int styleable AppCompatTheme_borderlessButtonStyle 40
int styleable AppCompatTheme_buttonBarButtonStyle 41
int styleable AppCompatTheme_buttonBarNegativeButtonStyle 42
int styleable AppCompatTheme_buttonBarNeutralButtonStyle 43
int styleable AppCompatTheme_buttonBarPositiveButtonStyle 44
int styleable AppCompatTheme_buttonBarStyle 45
int styleable AppCompatTheme_buttonStyle 46
int styleable AppCompatTheme_buttonStyleSmall 47
int styleable AppCompatTheme_checkboxStyle 48
int styleable AppCompatTheme_checkedTextViewStyle 49
int styleable AppCompatTheme_colorAccent 50
int styleable AppCompatTheme_colorBackgroundFloating 51
int styleable AppCompatTheme_colorButtonNormal 52
int styleable AppCompatTheme_colorControlActivated 53
int styleable AppCompatTheme_colorControlHighlight 54
int styleable AppCompatTheme_colorControlNormal 55
int styleable AppCompatTheme_colorError 56
int styleable AppCompatTheme_colorPrimary 57
int styleable AppCompatTheme_colorPrimaryDark 58
int styleable AppCompatTheme_colorSwitchThumbNormal 59
int styleable AppCompatTheme_controlBackground 60
int styleable AppCompatTheme_dialogCornerRadius 61
int styleable AppCompatTheme_dialogPreferredPadding 62
int styleable AppCompatTheme_dialogTheme 63
int styleable AppCompatTheme_dividerHorizontal 64
int styleable AppCompatTheme_dividerVertical 65
int styleable AppCompatTheme_dropDownListViewStyle 66
int styleable AppCompatTheme_dropdownListPreferredItemHeight 67
int styleable AppCompatTheme_editTextBackground 68
int styleable AppCompatTheme_editTextColor 69
int styleable AppCompatTheme_editTextStyle 70
int styleable AppCompatTheme_homeAsUpIndicator 71
int styleable AppCompatTheme_imageButtonStyle 72
int styleable AppCompatTheme_listChoiceBackgroundIndicator 73
int styleable AppCompatTheme_listChoiceIndicatorMultipleAnimated 74
int styleable AppCompatTheme_listChoiceIndicatorSingleAnimated 75
int styleable AppCompatTheme_listDividerAlertDialog 76
int styleable AppCompatTheme_listMenuViewStyle 77
int styleable AppCompatTheme_listPopupWindowStyle 78
int styleable AppCompatTheme_listPreferredItemHeight 79
int styleable AppCompatTheme_listPreferredItemHeightLarge 80
int styleable AppCompatTheme_listPreferredItemHeightSmall 81
int styleable AppCompatTheme_listPreferredItemPaddingEnd 82
int styleable AppCompatTheme_listPreferredItemPaddingLeft 83
int styleable AppCompatTheme_listPreferredItemPaddingRight 84
int styleable AppCompatTheme_listPreferredItemPaddingStart 85
int styleable AppCompatTheme_panelBackground 86
int styleable AppCompatTheme_panelMenuListTheme 87
int styleable AppCompatTheme_panelMenuListWidth 88
int styleable AppCompatTheme_popupMenuStyle 89
int styleable AppCompatTheme_popupWindowStyle 90
int styleable AppCompatTheme_radioButtonStyle 91
int styleable AppCompatTheme_ratingBarStyle 92
int styleable AppCompatTheme_ratingBarStyleIndicator 93
int styleable AppCompatTheme_ratingBarStyleSmall 94
int styleable AppCompatTheme_searchViewStyle 95
int styleable AppCompatTheme_seekBarStyle 96
int styleable AppCompatTheme_selectableItemBackground 97
int styleable AppCompatTheme_selectableItemBackgroundBorderless 98
int styleable AppCompatTheme_spinnerDropDownItemStyle 99
int styleable AppCompatTheme_spinnerStyle 100
int styleable AppCompatTheme_switchStyle 101
int styleable AppCompatTheme_textAppearanceLargePopupMenu 102
int styleable AppCompatTheme_textAppearanceListItem 103
int styleable AppCompatTheme_textAppearanceListItemSecondary 104
int styleable AppCompatTheme_textAppearanceListItemSmall 105
int styleable AppCompatTheme_textAppearancePopupMenuHeader 106
int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 107
int styleable AppCompatTheme_textAppearanceSearchResultTitle 108
int styleable AppCompatTheme_textAppearanceSmallPopupMenu 109
int styleable AppCompatTheme_textColorAlertDialogListItem 110
int styleable AppCompatTheme_textColorSearchUrl 111
int styleable AppCompatTheme_toolbarNavigationButtonStyle 112
int styleable AppCompatTheme_toolbarStyle 113
int styleable AppCompatTheme_tooltipForegroundColor 114
int styleable AppCompatTheme_tooltipFrameBackground 115
int styleable AppCompatTheme_viewInflaterClass 116
int styleable AppCompatTheme_windowActionBar 117
int styleable AppCompatTheme_windowActionBarOverlay 118
int styleable AppCompatTheme_windowActionModeOverlay 119
int styleable AppCompatTheme_windowFixedHeightMajor 120
int styleable AppCompatTheme_windowFixedHeightMinor 121
int styleable AppCompatTheme_windowFixedWidthMajor 122
int styleable AppCompatTheme_windowFixedWidthMinor 123
int styleable AppCompatTheme_windowMinWidthMajor 124
int styleable AppCompatTheme_windowMinWidthMinor 125
int styleable AppCompatTheme_windowNoTitle 126
int[] styleable Badge { 0x7f030045, 0x7f03004f, 0x7f030050, 0x7f030051, 0x7f030052, 0x7f030053, 0x7f030055, 0x7f030056, 0x7f030057, 0x7f030058, 0x7f030059, 0x7f03005a, 0x7f03005b, 0x7f03005c, 0x7f03005d, 0x7f030219, 0x7f03021a, 0x7f0302f6, 0x7f030344, 0x7f030346, 0x7f0304ac, 0x7f0304ad }
int styleable Badge_backgroundColor 0
int styleable Badge_badgeGravity 1
int styleable Badge_badgeHeight 2
int styleable Badge_badgeRadius 3
int styleable Badge_badgeShapeAppearance 4
int styleable Badge_badgeShapeAppearanceOverlay 5
int styleable Badge_badgeTextAppearance 6
int styleable Badge_badgeTextColor 7
int styleable Badge_badgeWidePadding 8
int styleable Badge_badgeWidth 9
int styleable Badge_badgeWithTextHeight 10
int styleable Badge_badgeWithTextRadius 11
int styleable Badge_badgeWithTextShapeAppearance 12
int styleable Badge_badgeWithTextShapeAppearanceOverlay 13
int styleable Badge_badgeWithTextWidth 14
int styleable Badge_horizontalOffset 15
int styleable Badge_horizontalOffsetWithText 16
int styleable Badge_maxCharacterCount 17
int styleable Badge_number 18
int styleable Badge_offsetAlignmentMode 19
int styleable Badge_verticalOffset 20
int styleable Badge_verticalOffsetWithText 21
int[] styleable BaseProgressIndicator { 0x01010139, 0x7f03020e, 0x7f03022e, 0x7f030302, 0x7f0303ae, 0x7f0303b0, 0x7f030492, 0x7f030495, 0x7f03049a }
int styleable BaseProgressIndicator_android_indeterminate 0
int styleable BaseProgressIndicator_hideAnimationBehavior 1
int styleable BaseProgressIndicator_indicatorColor 2
int styleable BaseProgressIndicator_minHideDelay 3
int styleable BaseProgressIndicator_showAnimationBehavior 4
int styleable BaseProgressIndicator_showDelay 5
int styleable BaseProgressIndicator_trackColor 6
int styleable BaseProgressIndicator_trackCornerRadius 7
int styleable BaseProgressIndicator_trackThickness 8
int[] styleable BottomAppBar { 0x7f030027, 0x7f03004d, 0x7f030193, 0x7f0301c1, 0x7f0301c2, 0x7f0301c3, 0x7f0301c4, 0x7f0301c5, 0x7f0301c6, 0x7f0301c7, 0x7f030212, 0x7f0302fe, 0x7f03033d, 0x7f030351, 0x7f030353, 0x7f030354, 0x7f03038c }
int styleable BottomAppBar_addElevationShadow 0
int styleable BottomAppBar_backgroundTint 1
int styleable BottomAppBar_elevation 2
int styleable BottomAppBar_fabAlignmentMode 3
int styleable BottomAppBar_fabAlignmentModeEndMargin 4
int styleable BottomAppBar_fabAnchorMode 5
int styleable BottomAppBar_fabAnimationMode 6
int styleable BottomAppBar_fabCradleMargin 7
int styleable BottomAppBar_fabCradleRoundedCornerRadius 8
int styleable BottomAppBar_fabCradleVerticalOffset 9
int styleable BottomAppBar_hideOnScroll 10
int styleable BottomAppBar_menuAlignmentMode 11
int styleable BottomAppBar_navigationIconTint 12
int styleable BottomAppBar_paddingBottomSystemWindowInsets 13
int styleable BottomAppBar_paddingLeftSystemWindowInsets 14
int styleable BottomAppBar_paddingRightSystemWindowInsets 15
int styleable BottomAppBar_removeEmbeddedFabElevation 16
int[] styleable BottomNavigationView { 0x01010140, 0x7f03012a, 0x7f03023d }
int styleable BottomNavigationView_android_minHeight 0
int styleable BottomNavigationView_compatShadowEnabled 1
int styleable BottomNavigationView_itemHorizontalTranslationEnabled 2
int[] styleable BottomSheetBehavior_Layout { 0x0101011f, 0x01010120, 0x01010440, 0x7f03004d, 0x7f030064, 0x7f030065, 0x7f030066, 0x7f030067, 0x7f030068, 0x7f03006a, 0x7f03006b, 0x7f03006c, 0x7f03006d, 0x7f030203, 0x7f0302c3, 0x7f0302c4, 0x7f0302c5, 0x7f030351, 0x7f030353, 0x7f030354, 0x7f030357, 0x7f0303a1, 0x7f0303a9, 0x7f0303ad }
int styleable BottomSheetBehavior_Layout_android_maxWidth 0
int styleable BottomSheetBehavior_Layout_android_maxHeight 1
int styleable BottomSheetBehavior_Layout_android_elevation 2
int styleable BottomSheetBehavior_Layout_backgroundTint 3
int styleable BottomSheetBehavior_Layout_behavior_draggable 4
int styleable BottomSheetBehavior_Layout_behavior_expandedOffset 5
int styleable BottomSheetBehavior_Layout_behavior_fitToContents 6
int styleable BottomSheetBehavior_Layout_behavior_halfExpandedRatio 7
int styleable BottomSheetBehavior_Layout_behavior_hideable 8
int styleable BottomSheetBehavior_Layout_behavior_peekHeight 9
int styleable BottomSheetBehavior_Layout_behavior_saveFlags 10
int styleable BottomSheetBehavior_Layout_behavior_significantVelocityThreshold 11
int styleable BottomSheetBehavior_Layout_behavior_skipCollapsed 12
int styleable BottomSheetBehavior_Layout_gestureInsetBottomIgnored 13
int styleable BottomSheetBehavior_Layout_marginLeftSystemWindowInsets 14
int styleable BottomSheetBehavior_Layout_marginRightSystemWindowInsets 15
int styleable BottomSheetBehavior_Layout_marginTopSystemWindowInsets 16
int styleable BottomSheetBehavior_Layout_paddingBottomSystemWindowInsets 17
int styleable BottomSheetBehavior_Layout_paddingLeftSystemWindowInsets 18
int styleable BottomSheetBehavior_Layout_paddingRightSystemWindowInsets 19
int styleable BottomSheetBehavior_Layout_paddingTopSystemWindowInsets 20
int styleable BottomSheetBehavior_Layout_shapeAppearance 21
int styleable BottomSheetBehavior_Layout_shapeAppearanceOverlay 22
int styleable BottomSheetBehavior_Layout_shouldRemoveExpandedCorners 23
int[] styleable ButtonBarLayout { 0x7f03002c }
int styleable ButtonBarLayout_allowStacking 0
int[] styleable Capability { 0x7f03037d, 0x7f0303ac }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable CardView { 0x0101013f, 0x01010140, 0x7f030095, 0x7f030096, 0x7f030097, 0x7f030099, 0x7f03009a, 0x7f03009b, 0x7f03013a, 0x7f03013b, 0x7f03013d, 0x7f03013e, 0x7f030140 }
int styleable CardView_android_minWidth 0
int styleable CardView_android_minHeight 1
int styleable CardView_cardBackgroundColor 2
int styleable CardView_cardCornerRadius 3
int styleable CardView_cardElevation 4
int styleable CardView_cardMaxElevation 5
int styleable CardView_cardPreventCornerOverlap 6
int styleable CardView_cardUseCompatPadding 7
int styleable CardView_contentPadding 8
int styleable CardView_contentPaddingBottom 9
int styleable CardView_contentPaddingLeft 10
int styleable CardView_contentPaddingRight 11
int styleable CardView_contentPaddingTop 12
int[] styleable Carousel { 0x7f03009d, 0x7f03009e, 0x7f03009f, 0x7f0300a0, 0x7f0300a1, 0x7f0300a2, 0x7f0300a3, 0x7f0300a4, 0x7f0300a5, 0x7f0300a6 }
int styleable Carousel_carousel_backwardTransition 0
int styleable Carousel_carousel_emptyViewsBehavior 1
int styleable Carousel_carousel_firstView 2
int styleable Carousel_carousel_forwardTransition 3
int styleable Carousel_carousel_infinite 4
int styleable Carousel_carousel_nextState 5
int styleable Carousel_carousel_previousState 6
int styleable Carousel_carousel_touchUpMode 7
int styleable Carousel_carousel_touchUp_dampeningFactor 8
int styleable Carousel_carousel_touchUp_velocityThreshold 9
int[] styleable CheckedTextView { 0x01010108, 0x7f0300a9, 0x7f0300aa, 0x7f0300ab }
int styleable CheckedTextView_android_checkMark 0
int styleable CheckedTextView_checkMarkCompat 1
int styleable CheckedTextView_checkMarkTint 2
int styleable CheckedTextView_checkMarkTintMode 3
int[] styleable Chip { 0x01010034, 0x01010095, 0x01010098, 0x010100ab, 0x0101011f, 0x0101014f, 0x010101e5, 0x7f0300af, 0x7f0300b0, 0x7f0300b4, 0x7f0300b5, 0x7f0300b8, 0x7f0300b9, 0x7f0300ba, 0x7f0300bc, 0x7f0300bd, 0x7f0300be, 0x7f0300bf, 0x7f0300c0, 0x7f0300c1, 0x7f0300c2, 0x7f0300c7, 0x7f0300c8, 0x7f0300c9, 0x7f0300cb, 0x7f0300d9, 0x7f0300da, 0x7f0300db, 0x7f0300dc, 0x7f0300dd, 0x7f0300de, 0x7f0300df, 0x7f0301a3, 0x7f03020f, 0x7f03021d, 0x7f030221, 0x7f03038e, 0x7f0303a1, 0x7f0303a9, 0x7f0303b2, 0x7f030448, 0x7f030457 }
int styleable Chip_android_textAppearance 0
int styleable Chip_android_textSize 1
int styleable Chip_android_textColor 2
int styleable Chip_android_ellipsize 3
int styleable Chip_android_maxWidth 4
int styleable Chip_android_text 5
int styleable Chip_android_checkable 6
int styleable Chip_checkedIcon 7
int styleable Chip_checkedIconEnabled 8
int styleable Chip_checkedIconTint 9
int styleable Chip_checkedIconVisible 10
int styleable Chip_chipBackgroundColor 11
int styleable Chip_chipCornerRadius 12
int styleable Chip_chipEndPadding 13
int styleable Chip_chipIcon 14
int styleable Chip_chipIconEnabled 15
int styleable Chip_chipIconSize 16
int styleable Chip_chipIconTint 17
int styleable Chip_chipIconVisible 18
int styleable Chip_chipMinHeight 19
int styleable Chip_chipMinTouchTargetSize 20
int styleable Chip_chipStartPadding 21
int styleable Chip_chipStrokeColor 22
int styleable Chip_chipStrokeWidth 23
int styleable Chip_chipSurfaceColor 24
int styleable Chip_closeIcon 25
int styleable Chip_closeIconEnabled 26
int styleable Chip_closeIconEndPadding 27
int styleable Chip_closeIconSize 28
int styleable Chip_closeIconStartPadding 29
int styleable Chip_closeIconTint 30
int styleable Chip_closeIconVisible 31
int styleable Chip_ensureMinTouchTargetSize 32
int styleable Chip_hideMotionSpec 33
int styleable Chip_iconEndPadding 34
int styleable Chip_iconStartPadding 35
int styleable Chip_rippleColor 36
int styleable Chip_shapeAppearance 37
int styleable Chip_shapeAppearanceOverlay 38
int styleable Chip_showMotionSpec 39
int styleable Chip_textEndPadding 40
int styleable Chip_textStartPadding 41
int[] styleable ChipGroup { 0x7f0300ae, 0x7f0300c3, 0x7f0300c4, 0x7f0300c5, 0x7f03039e, 0x7f0303be, 0x7f0303bf }
int styleable ChipGroup_checkedChip 0
int styleable ChipGroup_chipSpacing 1
int styleable ChipGroup_chipSpacingHorizontal 2
int styleable ChipGroup_chipSpacingVertical 3
int styleable ChipGroup_selectionRequired 4
int styleable ChipGroup_singleLine 5
int styleable ChipGroup_singleSelection 6
int[] styleable CircularProgressIndicator { 0x7f03022f, 0x7f030231, 0x7f030232 }
int styleable CircularProgressIndicator_indicatorDirectionCircular 0
int styleable CircularProgressIndicator_indicatorInset 1
int styleable CircularProgressIndicator_indicatorSize 2
int[] styleable ClockFaceView { 0x7f0300d5, 0x7f0300d8 }
int styleable ClockFaceView_clockFaceBackgroundColor 0
int styleable ClockFaceView_clockNumberTextColor 1
int[] styleable ClockHandView { 0x7f0300d6, 0x7f0302e2, 0x7f03039f }
int styleable ClockHandView_clockHandColor 0
int styleable ClockHandView_materialCircleRadius 1
int styleable ClockHandView_selectorSize 2
int[] styleable CollapsingToolbarLayout { 0x7f0300e4, 0x7f0300e5, 0x7f0300e6, 0x7f030141, 0x7f0301b1, 0x7f0301b2, 0x7f0301b3, 0x7f0301b4, 0x7f0301b5, 0x7f0301b6, 0x7f0301b7, 0x7f0301b8, 0x7f0301c0, 0x7f0301fe, 0x7f0302f9, 0x7f030394, 0x7f030396, 0x7f0303e4, 0x7f030475, 0x7f030477, 0x7f030478, 0x7f03047f, 0x7f030482, 0x7f030485 }
int styleable CollapsingToolbarLayout_collapsedTitleGravity 0
int styleable CollapsingToolbarLayout_collapsedTitleTextAppearance 1
int styleable CollapsingToolbarLayout_collapsedTitleTextColor 2
int styleable CollapsingToolbarLayout_contentScrim 3
int styleable CollapsingToolbarLayout_expandedTitleGravity 4
int styleable CollapsingToolbarLayout_expandedTitleMargin 5
int styleable CollapsingToolbarLayout_expandedTitleMarginBottom 6
int styleable CollapsingToolbarLayout_expandedTitleMarginEnd 7
int styleable CollapsingToolbarLayout_expandedTitleMarginStart 8
int styleable CollapsingToolbarLayout_expandedTitleMarginTop 9
int styleable CollapsingToolbarLayout_expandedTitleTextAppearance 10
int styleable CollapsingToolbarLayout_expandedTitleTextColor 11
int styleable CollapsingToolbarLayout_extraMultilineHeightEnabled 12
int styleable CollapsingToolbarLayout_forceApplySystemWindowInsetTop 13
int styleable CollapsingToolbarLayout_maxLines 14
int styleable CollapsingToolbarLayout_scrimAnimationDuration 15
int styleable CollapsingToolbarLayout_scrimVisibleHeightTrigger 16
int styleable CollapsingToolbarLayout_statusBarScrim 17
int styleable CollapsingToolbarLayout_title 18
int styleable CollapsingToolbarLayout_titleCollapseMode 19
int styleable CollapsingToolbarLayout_titleEnabled 20
int styleable CollapsingToolbarLayout_titlePositionInterpolator 21
int styleable CollapsingToolbarLayout_titleTextEllipsize 22
int styleable CollapsingToolbarLayout_toolbarId 23
int[] styleable CollapsingToolbarLayout_Layout { 0x7f030266, 0x7f030267 }
int styleable CollapsingToolbarLayout_Layout_layout_collapseMode 0
int styleable CollapsingToolbarLayout_Layout_layout_collapseParallaxMultiplier 1
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f03002d, 0x7f030259 }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable CompoundButton { 0x01010107, 0x7f03008a, 0x7f030093, 0x7f030094 }
int styleable CompoundButton_android_button 0
int styleable CompoundButton_buttonCompat 1
int styleable CompoundButton_buttonTint 2
int styleable CompoundButton_buttonTintMode 3
int[] styleable Constraint { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f030030, 0x7f030033, 0x7f03005f, 0x7f030060, 0x7f030061, 0x7f0300a8, 0x7f03012f, 0x7f030130, 0x7f03017f, 0x7f0301df, 0x7f0301e0, 0x7f0301e1, 0x7f0301e2, 0x7f0301e3, 0x7f0301e4, 0x7f0301e5, 0x7f0301e6, 0x7f0301e7, 0x7f0301e8, 0x7f0301e9, 0x7f0301ea, 0x7f0301eb, 0x7f0301ed, 0x7f0301ee, 0x7f0301ef, 0x7f0301f0, 0x7f0301f1, 0x7f030205, 0x7f030268, 0x7f030269, 0x7f03026a, 0x7f03026b, 0x7f03026c, 0x7f03026d, 0x7f03026e, 0x7f03026f, 0x7f030270, 0x7f030271, 0x7f030272, 0x7f030273, 0x7f030274, 0x7f030275, 0x7f030276, 0x7f030277, 0x7f030278, 0x7f030279, 0x7f03027a, 0x7f03027b, 0x7f03027c, 0x7f03027d, 0x7f03027e, 0x7f03027f, 0x7f030280, 0x7f030281, 0x7f030282, 0x7f030283, 0x7f030284, 0x7f030285, 0x7f030286, 0x7f030287, 0x7f030288, 0x7f030289, 0x7f03028a, 0x7f03028b, 0x7f03028c, 0x7f03028d, 0x7f03028e, 0x7f03028f, 0x7f030290, 0x7f030291, 0x7f030292, 0x7f030293, 0x7f030294, 0x7f030295, 0x7f030297, 0x7f030298, 0x7f030299, 0x7f03029a, 0x7f03029b, 0x7f03029c, 0x7f03029d, 0x7f03029e, 0x7f03029f, 0x7f0302a2, 0x7f0302a7, 0x7f030334, 0x7f030335, 0x7f030360, 0x7f030367, 0x7f03036c, 0x7f030378, 0x7f030379, 0x7f03037a, 0x7f03049d, 0x7f03049f, 0x7f0304a1, 0x7f0304b3 }
int styleable Constraint_android_orientation 0
int styleable Constraint_android_id 1
int styleable Constraint_android_visibility 2
int styleable Constraint_android_layout_width 3
int styleable Constraint_android_layout_height 4
int styleable Constraint_android_layout_marginLeft 5
int styleable Constraint_android_layout_marginTop 6
int styleable Constraint_android_layout_marginRight 7
int styleable Constraint_android_layout_marginBottom 8
int styleable Constraint_android_maxWidth 9
int styleable Constraint_android_maxHeight 10
int styleable Constraint_android_minWidth 11
int styleable Constraint_android_minHeight 12
int styleable Constraint_android_alpha 13
int styleable Constraint_android_transformPivotX 14
int styleable Constraint_android_transformPivotY 15
int styleable Constraint_android_translationX 16
int styleable Constraint_android_translationY 17
int styleable Constraint_android_scaleX 18
int styleable Constraint_android_scaleY 19
int styleable Constraint_android_rotation 20
int styleable Constraint_android_rotationX 21
int styleable Constraint_android_rotationY 22
int styleable Constraint_android_layout_marginStart 23
int styleable Constraint_android_layout_marginEnd 24
int styleable Constraint_android_translationZ 25
int styleable Constraint_android_elevation 26
int styleable Constraint_animateCircleAngleTo 27
int styleable Constraint_animateRelativeTo 28
int styleable Constraint_barrierAllowsGoneWidgets 29
int styleable Constraint_barrierDirection 30
int styleable Constraint_barrierMargin 31
int styleable Constraint_chainUseRtl 32
int styleable Constraint_constraint_referenced_ids 33
int styleable Constraint_constraint_referenced_tags 34
int styleable Constraint_drawPath 35
int styleable Constraint_flow_firstHorizontalBias 36
int styleable Constraint_flow_firstHorizontalStyle 37
int styleable Constraint_flow_firstVerticalBias 38
int styleable Constraint_flow_firstVerticalStyle 39
int styleable Constraint_flow_horizontalAlign 40
int styleable Constraint_flow_horizontalBias 41
int styleable Constraint_flow_horizontalGap 42
int styleable Constraint_flow_horizontalStyle 43
int styleable Constraint_flow_lastHorizontalBias 44
int styleable Constraint_flow_lastHorizontalStyle 45
int styleable Constraint_flow_lastVerticalBias 46
int styleable Constraint_flow_lastVerticalStyle 47
int styleable Constraint_flow_maxElementsWrap 48
int styleable Constraint_flow_verticalAlign 49
int styleable Constraint_flow_verticalBias 50
int styleable Constraint_flow_verticalGap 51
int styleable Constraint_flow_verticalStyle 52
int styleable Constraint_flow_wrapMode 53
int styleable Constraint_guidelineUseRtl 54
int styleable Constraint_layout_constrainedHeight 55
int styleable Constraint_layout_constrainedWidth 56
int styleable Constraint_layout_constraintBaseline_creator 57
int styleable Constraint_layout_constraintBaseline_toBaselineOf 58
int styleable Constraint_layout_constraintBaseline_toBottomOf 59
int styleable Constraint_layout_constraintBaseline_toTopOf 60
int styleable Constraint_layout_constraintBottom_creator 61
int styleable Constraint_layout_constraintBottom_toBottomOf 62
int styleable Constraint_layout_constraintBottom_toTopOf 63
int styleable Constraint_layout_constraintCircle 64
int styleable Constraint_layout_constraintCircleAngle 65
int styleable Constraint_layout_constraintCircleRadius 66
int styleable Constraint_layout_constraintDimensionRatio 67
int styleable Constraint_layout_constraintEnd_toEndOf 68
int styleable Constraint_layout_constraintEnd_toStartOf 69
int styleable Constraint_layout_constraintGuide_begin 70
int styleable Constraint_layout_constraintGuide_end 71
int styleable Constraint_layout_constraintGuide_percent 72
int styleable Constraint_layout_constraintHeight 73
int styleable Constraint_layout_constraintHeight_default 74
int styleable Constraint_layout_constraintHeight_max 75
int styleable Constraint_layout_constraintHeight_min 76
int styleable Constraint_layout_constraintHeight_percent 77
int styleable Constraint_layout_constraintHorizontal_bias 78
int styleable Constraint_layout_constraintHorizontal_chainStyle 79
int styleable Constraint_layout_constraintHorizontal_weight 80
int styleable Constraint_layout_constraintLeft_creator 81
int styleable Constraint_layout_constraintLeft_toLeftOf 82
int styleable Constraint_layout_constraintLeft_toRightOf 83
int styleable Constraint_layout_constraintRight_creator 84
int styleable Constraint_layout_constraintRight_toLeftOf 85
int styleable Constraint_layout_constraintRight_toRightOf 86
int styleable Constraint_layout_constraintStart_toEndOf 87
int styleable Constraint_layout_constraintStart_toStartOf 88
int styleable Constraint_layout_constraintTag 89
int styleable Constraint_layout_constraintTop_creator 90
int styleable Constraint_layout_constraintTop_toBottomOf 91
int styleable Constraint_layout_constraintTop_toTopOf 92
int styleable Constraint_layout_constraintVertical_bias 93
int styleable Constraint_layout_constraintVertical_chainStyle 94
int styleable Constraint_layout_constraintVertical_weight 95
int styleable Constraint_layout_constraintWidth 96
int styleable Constraint_layout_constraintWidth_default 97
int styleable Constraint_layout_constraintWidth_max 98
int styleable Constraint_layout_constraintWidth_min 99
int styleable Constraint_layout_constraintWidth_percent 100
int styleable Constraint_layout_editor_absoluteX 101
int styleable Constraint_layout_editor_absoluteY 102
int styleable Constraint_layout_goneMarginBaseline 103
int styleable Constraint_layout_goneMarginBottom 104
int styleable Constraint_layout_goneMarginEnd 105
int styleable Constraint_layout_goneMarginLeft 106
int styleable Constraint_layout_goneMarginRight 107
int styleable Constraint_layout_goneMarginStart 108
int styleable Constraint_layout_goneMarginTop 109
int styleable Constraint_layout_marginBaseline 110
int styleable Constraint_layout_wrapBehaviorInParent 111
int styleable Constraint_motionProgress 112
int styleable Constraint_motionStagger 113
int styleable Constraint_pathMotionArc 114
int styleable Constraint_pivotAnchor 115
int styleable Constraint_polarRelativeTo 116
int styleable Constraint_quantizeMotionInterpolator 117
int styleable Constraint_quantizeMotionPhase 118
int styleable Constraint_quantizeMotionSteps 119
int styleable Constraint_transformPivotTarget 120
int styleable Constraint_transitionEasing 121
int styleable Constraint_transitionPathRotate 122
int styleable Constraint_visibilityMode 123
int[] styleable ConstraintLayout_Layout { 0x010100c4, 0x010100d5, 0x010100d6, 0x010100d7, 0x010100d8, 0x010100d9, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f6, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x010103b3, 0x010103b4, 0x010103b5, 0x010103b6, 0x01010440, 0x0101053b, 0x0101053c, 0x7f03005f, 0x7f030060, 0x7f030061, 0x7f0300a8, 0x7f0300ce, 0x7f0300cf, 0x7f0300d0, 0x7f0300d1, 0x7f0300d2, 0x7f03012c, 0x7f03012f, 0x7f030130, 0x7f0301df, 0x7f0301e0, 0x7f0301e1, 0x7f0301e2, 0x7f0301e3, 0x7f0301e4, 0x7f0301e5, 0x7f0301e6, 0x7f0301e7, 0x7f0301e8, 0x7f0301e9, 0x7f0301ea, 0x7f0301eb, 0x7f0301ed, 0x7f0301ee, 0x7f0301ef, 0x7f0301f0, 0x7f0301f1, 0x7f030205, 0x7f030260, 0x7f030268, 0x7f030269, 0x7f03026a, 0x7f03026b, 0x7f03026c, 0x7f03026d, 0x7f03026e, 0x7f03026f, 0x7f030270, 0x7f030271, 0x7f030272, 0x7f030273, 0x7f030274, 0x7f030275, 0x7f030276, 0x7f030277, 0x7f030278, 0x7f030279, 0x7f03027a, 0x7f03027b, 0x7f03027c, 0x7f03027d, 0x7f03027e, 0x7f03027f, 0x7f030280, 0x7f030281, 0x7f030282, 0x7f030283, 0x7f030284, 0x7f030285, 0x7f030286, 0x7f030287, 0x7f030288, 0x7f030289, 0x7f03028a, 0x7f03028b, 0x7f03028c, 0x7f03028d, 0x7f03028e, 0x7f03028f, 0x7f030290, 0x7f030291, 0x7f030292, 0x7f030293, 0x7f030294, 0x7f030295, 0x7f030297, 0x7f030298, 0x7f030299, 0x7f03029a, 0x7f03029b, 0x7f03029c, 0x7f03029d, 0x7f03029e, 0x7f03029f, 0x7f0302a2, 0x7f0302a3, 0x7f0302a7 }
int styleable ConstraintLayout_Layout_android_orientation 0
int styleable ConstraintLayout_Layout_android_padding 1
int styleable ConstraintLayout_Layout_android_paddingLeft 2
int styleable ConstraintLayout_Layout_android_paddingTop 3
int styleable ConstraintLayout_Layout_android_paddingRight 4
int styleable ConstraintLayout_Layout_android_paddingBottom 5
int styleable ConstraintLayout_Layout_android_visibility 6
int styleable ConstraintLayout_Layout_android_layout_width 7
int styleable ConstraintLayout_Layout_android_layout_height 8
int styleable ConstraintLayout_Layout_android_layout_margin 9
int styleable ConstraintLayout_Layout_android_layout_marginLeft 10
int styleable ConstraintLayout_Layout_android_layout_marginTop 11
int styleable ConstraintLayout_Layout_android_layout_marginRight 12
int styleable ConstraintLayout_Layout_android_layout_marginBottom 13
int styleable ConstraintLayout_Layout_android_maxWidth 14
int styleable ConstraintLayout_Layout_android_maxHeight 15
int styleable ConstraintLayout_Layout_android_minWidth 16
int styleable ConstraintLayout_Layout_android_minHeight 17
int styleable ConstraintLayout_Layout_android_paddingStart 18
int styleable ConstraintLayout_Layout_android_paddingEnd 19
int styleable ConstraintLayout_Layout_android_layout_marginStart 20
int styleable ConstraintLayout_Layout_android_layout_marginEnd 21
int styleable ConstraintLayout_Layout_android_elevation 22
int styleable ConstraintLayout_Layout_android_layout_marginHorizontal 23
int styleable ConstraintLayout_Layout_android_layout_marginVertical 24
int styleable ConstraintLayout_Layout_barrierAllowsGoneWidgets 25
int styleable ConstraintLayout_Layout_barrierDirection 26
int styleable ConstraintLayout_Layout_barrierMargin 27
int styleable ConstraintLayout_Layout_chainUseRtl 28
int styleable ConstraintLayout_Layout_circularflow_angles 29
int styleable ConstraintLayout_Layout_circularflow_defaultAngle 30
int styleable ConstraintLayout_Layout_circularflow_defaultRadius 31
int styleable ConstraintLayout_Layout_circularflow_radiusInDP 32
int styleable ConstraintLayout_Layout_circularflow_viewCenter 33
int styleable ConstraintLayout_Layout_constraintSet 34
int styleable ConstraintLayout_Layout_constraint_referenced_ids 35
int styleable ConstraintLayout_Layout_constraint_referenced_tags 36
int styleable ConstraintLayout_Layout_flow_firstHorizontalBias 37
int styleable ConstraintLayout_Layout_flow_firstHorizontalStyle 38
int styleable ConstraintLayout_Layout_flow_firstVerticalBias 39
int styleable ConstraintLayout_Layout_flow_firstVerticalStyle 40
int styleable ConstraintLayout_Layout_flow_horizontalAlign 41
int styleable ConstraintLayout_Layout_flow_horizontalBias 42
int styleable ConstraintLayout_Layout_flow_horizontalGap 43
int styleable ConstraintLayout_Layout_flow_horizontalStyle 44
int styleable ConstraintLayout_Layout_flow_lastHorizontalBias 45
int styleable ConstraintLayout_Layout_flow_lastHorizontalStyle 46
int styleable ConstraintLayout_Layout_flow_lastVerticalBias 47
int styleable ConstraintLayout_Layout_flow_lastVerticalStyle 48
int styleable ConstraintLayout_Layout_flow_maxElementsWrap 49
int styleable ConstraintLayout_Layout_flow_verticalAlign 50
int styleable ConstraintLayout_Layout_flow_verticalBias 51
int styleable ConstraintLayout_Layout_flow_verticalGap 52
int styleable ConstraintLayout_Layout_flow_verticalStyle 53
int styleable ConstraintLayout_Layout_flow_wrapMode 54
int styleable ConstraintLayout_Layout_guidelineUseRtl 55
int styleable ConstraintLayout_Layout_layoutDescription 56
int styleable ConstraintLayout_Layout_layout_constrainedHeight 57
int styleable ConstraintLayout_Layout_layout_constrainedWidth 58
int styleable ConstraintLayout_Layout_layout_constraintBaseline_creator 59
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toBaselineOf 60
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toBottomOf 61
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toTopOf 62
int styleable ConstraintLayout_Layout_layout_constraintBottom_creator 63
int styleable ConstraintLayout_Layout_layout_constraintBottom_toBottomOf 64
int styleable ConstraintLayout_Layout_layout_constraintBottom_toTopOf 65
int styleable ConstraintLayout_Layout_layout_constraintCircle 66
int styleable ConstraintLayout_Layout_layout_constraintCircleAngle 67
int styleable ConstraintLayout_Layout_layout_constraintCircleRadius 68
int styleable ConstraintLayout_Layout_layout_constraintDimensionRatio 69
int styleable ConstraintLayout_Layout_layout_constraintEnd_toEndOf 70
int styleable ConstraintLayout_Layout_layout_constraintEnd_toStartOf 71
int styleable ConstraintLayout_Layout_layout_constraintGuide_begin 72
int styleable ConstraintLayout_Layout_layout_constraintGuide_end 73
int styleable ConstraintLayout_Layout_layout_constraintGuide_percent 74
int styleable ConstraintLayout_Layout_layout_constraintHeight 75
int styleable ConstraintLayout_Layout_layout_constraintHeight_default 76
int styleable ConstraintLayout_Layout_layout_constraintHeight_max 77
int styleable ConstraintLayout_Layout_layout_constraintHeight_min 78
int styleable ConstraintLayout_Layout_layout_constraintHeight_percent 79
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_bias 80
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_chainStyle 81
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_weight 82
int styleable ConstraintLayout_Layout_layout_constraintLeft_creator 83
int styleable ConstraintLayout_Layout_layout_constraintLeft_toLeftOf 84
int styleable ConstraintLayout_Layout_layout_constraintLeft_toRightOf 85
int styleable ConstraintLayout_Layout_layout_constraintRight_creator 86
int styleable ConstraintLayout_Layout_layout_constraintRight_toLeftOf 87
int styleable ConstraintLayout_Layout_layout_constraintRight_toRightOf 88
int styleable ConstraintLayout_Layout_layout_constraintStart_toEndOf 89
int styleable ConstraintLayout_Layout_layout_constraintStart_toStartOf 90
int styleable ConstraintLayout_Layout_layout_constraintTag 91
int styleable ConstraintLayout_Layout_layout_constraintTop_creator 92
int styleable ConstraintLayout_Layout_layout_constraintTop_toBottomOf 93
int styleable ConstraintLayout_Layout_layout_constraintTop_toTopOf 94
int styleable ConstraintLayout_Layout_layout_constraintVertical_bias 95
int styleable ConstraintLayout_Layout_layout_constraintVertical_chainStyle 96
int styleable ConstraintLayout_Layout_layout_constraintVertical_weight 97
int styleable ConstraintLayout_Layout_layout_constraintWidth 98
int styleable ConstraintLayout_Layout_layout_constraintWidth_default 99
int styleable ConstraintLayout_Layout_layout_constraintWidth_max 100
int styleable ConstraintLayout_Layout_layout_constraintWidth_min 101
int styleable ConstraintLayout_Layout_layout_constraintWidth_percent 102
int styleable ConstraintLayout_Layout_layout_editor_absoluteX 103
int styleable ConstraintLayout_Layout_layout_editor_absoluteY 104
int styleable ConstraintLayout_Layout_layout_goneMarginBaseline 105
int styleable ConstraintLayout_Layout_layout_goneMarginBottom 106
int styleable ConstraintLayout_Layout_layout_goneMarginEnd 107
int styleable ConstraintLayout_Layout_layout_goneMarginLeft 108
int styleable ConstraintLayout_Layout_layout_goneMarginRight 109
int styleable ConstraintLayout_Layout_layout_goneMarginStart 110
int styleable ConstraintLayout_Layout_layout_goneMarginTop 111
int styleable ConstraintLayout_Layout_layout_marginBaseline 112
int styleable ConstraintLayout_Layout_layout_optimizationLevel 113
int styleable ConstraintLayout_Layout_layout_wrapBehaviorInParent 114
int[] styleable ConstraintLayout_ReactiveGuide { 0x7f030383, 0x7f030384, 0x7f030385, 0x7f030386 }
int styleable ConstraintLayout_ReactiveGuide_reactiveGuide_animateChange 0
int styleable ConstraintLayout_ReactiveGuide_reactiveGuide_applyToAllConstraintSets 1
int styleable ConstraintLayout_ReactiveGuide_reactiveGuide_applyToConstraintSet 2
int styleable ConstraintLayout_ReactiveGuide_reactiveGuide_valueId 3
int[] styleable ConstraintLayout_placeholder { 0x7f030132, 0x7f03036b }
int styleable ConstraintLayout_placeholder_content 0
int styleable ConstraintLayout_placeholder_placeholder_emptyVisibility 1
int[] styleable ConstraintOverride { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f030030, 0x7f030033, 0x7f03005f, 0x7f030060, 0x7f030061, 0x7f0300a8, 0x7f03012f, 0x7f03017f, 0x7f0301df, 0x7f0301e0, 0x7f0301e1, 0x7f0301e2, 0x7f0301e3, 0x7f0301e4, 0x7f0301e5, 0x7f0301e6, 0x7f0301e7, 0x7f0301e8, 0x7f0301e9, 0x7f0301ea, 0x7f0301eb, 0x7f0301ed, 0x7f0301ee, 0x7f0301ef, 0x7f0301f0, 0x7f0301f1, 0x7f030205, 0x7f030268, 0x7f030269, 0x7f03026a, 0x7f03026e, 0x7f030272, 0x7f030273, 0x7f030274, 0x7f030277, 0x7f030278, 0x7f030279, 0x7f03027a, 0x7f03027b, 0x7f03027c, 0x7f03027d, 0x7f03027e, 0x7f03027f, 0x7f030280, 0x7f030281, 0x7f030282, 0x7f030285, 0x7f03028a, 0x7f03028b, 0x7f03028e, 0x7f03028f, 0x7f030290, 0x7f030291, 0x7f030292, 0x7f030293, 0x7f030294, 0x7f030295, 0x7f030297, 0x7f030298, 0x7f030299, 0x7f03029a, 0x7f03029b, 0x7f03029c, 0x7f03029d, 0x7f03029e, 0x7f03029f, 0x7f0302a2, 0x7f0302a7, 0x7f030334, 0x7f030335, 0x7f030336, 0x7f030360, 0x7f030367, 0x7f03036c, 0x7f030378, 0x7f030379, 0x7f03037a, 0x7f03049d, 0x7f03049f, 0x7f0304a1, 0x7f0304b3 }
int styleable ConstraintOverride_android_orientation 0
int styleable ConstraintOverride_android_id 1
int styleable ConstraintOverride_android_visibility 2
int styleable ConstraintOverride_android_layout_width 3
int styleable ConstraintOverride_android_layout_height 4
int styleable ConstraintOverride_android_layout_marginLeft 5
int styleable ConstraintOverride_android_layout_marginTop 6
int styleable ConstraintOverride_android_layout_marginRight 7
int styleable ConstraintOverride_android_layout_marginBottom 8
int styleable ConstraintOverride_android_maxWidth 9
int styleable ConstraintOverride_android_maxHeight 10
int styleable ConstraintOverride_android_minWidth 11
int styleable ConstraintOverride_android_minHeight 12
int styleable ConstraintOverride_android_alpha 13
int styleable ConstraintOverride_android_transformPivotX 14
int styleable ConstraintOverride_android_transformPivotY 15
int styleable ConstraintOverride_android_translationX 16
int styleable ConstraintOverride_android_translationY 17
int styleable ConstraintOverride_android_scaleX 18
int styleable ConstraintOverride_android_scaleY 19
int styleable ConstraintOverride_android_rotation 20
int styleable ConstraintOverride_android_rotationX 21
int styleable ConstraintOverride_android_rotationY 22
int styleable ConstraintOverride_android_layout_marginStart 23
int styleable ConstraintOverride_android_layout_marginEnd 24
int styleable ConstraintOverride_android_translationZ 25
int styleable ConstraintOverride_android_elevation 26
int styleable ConstraintOverride_animateCircleAngleTo 27
int styleable ConstraintOverride_animateRelativeTo 28
int styleable ConstraintOverride_barrierAllowsGoneWidgets 29
int styleable ConstraintOverride_barrierDirection 30
int styleable ConstraintOverride_barrierMargin 31
int styleable ConstraintOverride_chainUseRtl 32
int styleable ConstraintOverride_constraint_referenced_ids 33
int styleable ConstraintOverride_drawPath 34
int styleable ConstraintOverride_flow_firstHorizontalBias 35
int styleable ConstraintOverride_flow_firstHorizontalStyle 36
int styleable ConstraintOverride_flow_firstVerticalBias 37
int styleable ConstraintOverride_flow_firstVerticalStyle 38
int styleable ConstraintOverride_flow_horizontalAlign 39
int styleable ConstraintOverride_flow_horizontalBias 40
int styleable ConstraintOverride_flow_horizontalGap 41
int styleable ConstraintOverride_flow_horizontalStyle 42
int styleable ConstraintOverride_flow_lastHorizontalBias 43
int styleable ConstraintOverride_flow_lastHorizontalStyle 44
int styleable ConstraintOverride_flow_lastVerticalBias 45
int styleable ConstraintOverride_flow_lastVerticalStyle 46
int styleable ConstraintOverride_flow_maxElementsWrap 47
int styleable ConstraintOverride_flow_verticalAlign 48
int styleable ConstraintOverride_flow_verticalBias 49
int styleable ConstraintOverride_flow_verticalGap 50
int styleable ConstraintOverride_flow_verticalStyle 51
int styleable ConstraintOverride_flow_wrapMode 52
int styleable ConstraintOverride_guidelineUseRtl 53
int styleable ConstraintOverride_layout_constrainedHeight 54
int styleable ConstraintOverride_layout_constrainedWidth 55
int styleable ConstraintOverride_layout_constraintBaseline_creator 56
int styleable ConstraintOverride_layout_constraintBottom_creator 57
int styleable ConstraintOverride_layout_constraintCircleAngle 58
int styleable ConstraintOverride_layout_constraintCircleRadius 59
int styleable ConstraintOverride_layout_constraintDimensionRatio 60
int styleable ConstraintOverride_layout_constraintGuide_begin 61
int styleable ConstraintOverride_layout_constraintGuide_end 62
int styleable ConstraintOverride_layout_constraintGuide_percent 63
int styleable ConstraintOverride_layout_constraintHeight 64
int styleable ConstraintOverride_layout_constraintHeight_default 65
int styleable ConstraintOverride_layout_constraintHeight_max 66
int styleable ConstraintOverride_layout_constraintHeight_min 67
int styleable ConstraintOverride_layout_constraintHeight_percent 68
int styleable ConstraintOverride_layout_constraintHorizontal_bias 69
int styleable ConstraintOverride_layout_constraintHorizontal_chainStyle 70
int styleable ConstraintOverride_layout_constraintHorizontal_weight 71
int styleable ConstraintOverride_layout_constraintLeft_creator 72
int styleable ConstraintOverride_layout_constraintRight_creator 73
int styleable ConstraintOverride_layout_constraintTag 74
int styleable ConstraintOverride_layout_constraintTop_creator 75
int styleable ConstraintOverride_layout_constraintVertical_bias 76
int styleable ConstraintOverride_layout_constraintVertical_chainStyle 77
int styleable ConstraintOverride_layout_constraintVertical_weight 78
int styleable ConstraintOverride_layout_constraintWidth 79
int styleable ConstraintOverride_layout_constraintWidth_default 80
int styleable ConstraintOverride_layout_constraintWidth_max 81
int styleable ConstraintOverride_layout_constraintWidth_min 82
int styleable ConstraintOverride_layout_constraintWidth_percent 83
int styleable ConstraintOverride_layout_editor_absoluteX 84
int styleable ConstraintOverride_layout_editor_absoluteY 85
int styleable ConstraintOverride_layout_goneMarginBaseline 86
int styleable ConstraintOverride_layout_goneMarginBottom 87
int styleable ConstraintOverride_layout_goneMarginEnd 88
int styleable ConstraintOverride_layout_goneMarginLeft 89
int styleable ConstraintOverride_layout_goneMarginRight 90
int styleable ConstraintOverride_layout_goneMarginStart 91
int styleable ConstraintOverride_layout_goneMarginTop 92
int styleable ConstraintOverride_layout_marginBaseline 93
int styleable ConstraintOverride_layout_wrapBehaviorInParent 94
int styleable ConstraintOverride_motionProgress 95
int styleable ConstraintOverride_motionStagger 96
int styleable ConstraintOverride_motionTarget 97
int styleable ConstraintOverride_pathMotionArc 98
int styleable ConstraintOverride_pivotAnchor 99
int styleable ConstraintOverride_polarRelativeTo 100
int styleable ConstraintOverride_quantizeMotionInterpolator 101
int styleable ConstraintOverride_quantizeMotionPhase 102
int styleable ConstraintOverride_quantizeMotionSteps 103
int styleable ConstraintOverride_transformPivotTarget 104
int styleable ConstraintOverride_transitionEasing 105
int styleable ConstraintOverride_transitionPathRotate 106
int styleable ConstraintOverride_visibilityMode 107
int[] styleable ConstraintSet { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x010101b5, 0x010101b6, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f030030, 0x7f030033, 0x7f03005f, 0x7f030060, 0x7f030061, 0x7f0300a8, 0x7f03012b, 0x7f03012f, 0x7f030130, 0x7f03016f, 0x7f03017f, 0x7f0301df, 0x7f0301e0, 0x7f0301e1, 0x7f0301e2, 0x7f0301e3, 0x7f0301e4, 0x7f0301e5, 0x7f0301e6, 0x7f0301e7, 0x7f0301e8, 0x7f0301e9, 0x7f0301ea, 0x7f0301eb, 0x7f0301ed, 0x7f0301ee, 0x7f0301ef, 0x7f0301f0, 0x7f0301f1, 0x7f030205, 0x7f030268, 0x7f030269, 0x7f03026a, 0x7f03026b, 0x7f03026c, 0x7f03026d, 0x7f03026e, 0x7f03026f, 0x7f030270, 0x7f030271, 0x7f030272, 0x7f030273, 0x7f030274, 0x7f030275, 0x7f030276, 0x7f030277, 0x7f030278, 0x7f030279, 0x7f03027b, 0x7f03027c, 0x7f03027d, 0x7f03027e, 0x7f03027f, 0x7f030280, 0x7f030281, 0x7f030282, 0x7f030283, 0x7f030284, 0x7f030285, 0x7f030286, 0x7f030287, 0x7f030288, 0x7f030289, 0x7f03028a, 0x7f03028b, 0x7f03028c, 0x7f03028d, 0x7f03028e, 0x7f03028f, 0x7f030290, 0x7f030292, 0x7f030293, 0x7f030294, 0x7f030295, 0x7f030297, 0x7f030298, 0x7f030299, 0x7f03029a, 0x7f03029b, 0x7f03029c, 0x7f03029d, 0x7f03029e, 0x7f03029f, 0x7f0302a2, 0x7f0302a7, 0x7f030334, 0x7f030335, 0x7f030360, 0x7f030367, 0x7f03036c, 0x7f03037a, 0x7f03049f, 0x7f0304a1 }
int styleable ConstraintSet_android_orientation 0
int styleable ConstraintSet_android_id 1
int styleable ConstraintSet_android_visibility 2
int styleable ConstraintSet_android_layout_width 3
int styleable ConstraintSet_android_layout_height 4
int styleable ConstraintSet_android_layout_marginLeft 5
int styleable ConstraintSet_android_layout_marginTop 6
int styleable ConstraintSet_android_layout_marginRight 7
int styleable ConstraintSet_android_layout_marginBottom 8
int styleable ConstraintSet_android_maxWidth 9
int styleable ConstraintSet_android_maxHeight 10
int styleable ConstraintSet_android_minWidth 11
int styleable ConstraintSet_android_minHeight 12
int styleable ConstraintSet_android_pivotX 13
int styleable ConstraintSet_android_pivotY 14
int styleable ConstraintSet_android_alpha 15
int styleable ConstraintSet_android_transformPivotX 16
int styleable ConstraintSet_android_transformPivotY 17
int styleable ConstraintSet_android_translationX 18
int styleable ConstraintSet_android_translationY 19
int styleable ConstraintSet_android_scaleX 20
int styleable ConstraintSet_android_scaleY 21
int styleable ConstraintSet_android_rotation 22
int styleable ConstraintSet_android_rotationX 23
int styleable ConstraintSet_android_rotationY 24
int styleable ConstraintSet_android_layout_marginStart 25
int styleable ConstraintSet_android_layout_marginEnd 26
int styleable ConstraintSet_android_translationZ 27
int styleable ConstraintSet_android_elevation 28
int styleable ConstraintSet_animateCircleAngleTo 29
int styleable ConstraintSet_animateRelativeTo 30
int styleable ConstraintSet_barrierAllowsGoneWidgets 31
int styleable ConstraintSet_barrierDirection 32
int styleable ConstraintSet_barrierMargin 33
int styleable ConstraintSet_chainUseRtl 34
int styleable ConstraintSet_constraintRotate 35
int styleable ConstraintSet_constraint_referenced_ids 36
int styleable ConstraintSet_constraint_referenced_tags 37
int styleable ConstraintSet_deriveConstraintsFrom 38
int styleable ConstraintSet_drawPath 39
int styleable ConstraintSet_flow_firstHorizontalBias 40
int styleable ConstraintSet_flow_firstHorizontalStyle 41
int styleable ConstraintSet_flow_firstVerticalBias 42
int styleable ConstraintSet_flow_firstVerticalStyle 43
int styleable ConstraintSet_flow_horizontalAlign 44
int styleable ConstraintSet_flow_horizontalBias 45
int styleable ConstraintSet_flow_horizontalGap 46
int styleable ConstraintSet_flow_horizontalStyle 47
int styleable ConstraintSet_flow_lastHorizontalBias 48
int styleable ConstraintSet_flow_lastHorizontalStyle 49
int styleable ConstraintSet_flow_lastVerticalBias 50
int styleable ConstraintSet_flow_lastVerticalStyle 51
int styleable ConstraintSet_flow_maxElementsWrap 52
int styleable ConstraintSet_flow_verticalAlign 53
int styleable ConstraintSet_flow_verticalBias 54
int styleable ConstraintSet_flow_verticalGap 55
int styleable ConstraintSet_flow_verticalStyle 56
int styleable ConstraintSet_flow_wrapMode 57
int styleable ConstraintSet_guidelineUseRtl 58
int styleable ConstraintSet_layout_constrainedHeight 59
int styleable ConstraintSet_layout_constrainedWidth 60
int styleable ConstraintSet_layout_constraintBaseline_creator 61
int styleable ConstraintSet_layout_constraintBaseline_toBaselineOf 62
int styleable ConstraintSet_layout_constraintBaseline_toBottomOf 63
int styleable ConstraintSet_layout_constraintBaseline_toTopOf 64
int styleable ConstraintSet_layout_constraintBottom_creator 65
int styleable ConstraintSet_layout_constraintBottom_toBottomOf 66
int styleable ConstraintSet_layout_constraintBottom_toTopOf 67
int styleable ConstraintSet_layout_constraintCircle 68
int styleable ConstraintSet_layout_constraintCircleAngle 69
int styleable ConstraintSet_layout_constraintCircleRadius 70
int styleable ConstraintSet_layout_constraintDimensionRatio 71
int styleable ConstraintSet_layout_constraintEnd_toEndOf 72
int styleable ConstraintSet_layout_constraintEnd_toStartOf 73
int styleable ConstraintSet_layout_constraintGuide_begin 74
int styleable ConstraintSet_layout_constraintGuide_end 75
int styleable ConstraintSet_layout_constraintGuide_percent 76
int styleable ConstraintSet_layout_constraintHeight_default 77
int styleable ConstraintSet_layout_constraintHeight_max 78
int styleable ConstraintSet_layout_constraintHeight_min 79
int styleable ConstraintSet_layout_constraintHeight_percent 80
int styleable ConstraintSet_layout_constraintHorizontal_bias 81
int styleable ConstraintSet_layout_constraintHorizontal_chainStyle 82
int styleable ConstraintSet_layout_constraintHorizontal_weight 83
int styleable ConstraintSet_layout_constraintLeft_creator 84
int styleable ConstraintSet_layout_constraintLeft_toLeftOf 85
int styleable ConstraintSet_layout_constraintLeft_toRightOf 86
int styleable ConstraintSet_layout_constraintRight_creator 87
int styleable ConstraintSet_layout_constraintRight_toLeftOf 88
int styleable ConstraintSet_layout_constraintRight_toRightOf 89
int styleable ConstraintSet_layout_constraintStart_toEndOf 90
int styleable ConstraintSet_layout_constraintStart_toStartOf 91
int styleable ConstraintSet_layout_constraintTag 92
int styleable ConstraintSet_layout_constraintTop_creator 93
int styleable ConstraintSet_layout_constraintTop_toBottomOf 94
int styleable ConstraintSet_layout_constraintTop_toTopOf 95
int styleable ConstraintSet_layout_constraintVertical_bias 96
int styleable ConstraintSet_layout_constraintVertical_chainStyle 97
int styleable ConstraintSet_layout_constraintVertical_weight 98
int styleable ConstraintSet_layout_constraintWidth_default 99
int styleable ConstraintSet_layout_constraintWidth_max 100
int styleable ConstraintSet_layout_constraintWidth_min 101
int styleable ConstraintSet_layout_constraintWidth_percent 102
int styleable ConstraintSet_layout_editor_absoluteX 103
int styleable ConstraintSet_layout_editor_absoluteY 104
int styleable ConstraintSet_layout_goneMarginBaseline 105
int styleable ConstraintSet_layout_goneMarginBottom 106
int styleable ConstraintSet_layout_goneMarginEnd 107
int styleable ConstraintSet_layout_goneMarginLeft 108
int styleable ConstraintSet_layout_goneMarginRight 109
int styleable ConstraintSet_layout_goneMarginStart 110
int styleable ConstraintSet_layout_goneMarginTop 111
int styleable ConstraintSet_layout_marginBaseline 112
int styleable ConstraintSet_layout_wrapBehaviorInParent 113
int styleable ConstraintSet_motionProgress 114
int styleable ConstraintSet_motionStagger 115
int styleable ConstraintSet_pathMotionArc 116
int styleable ConstraintSet_pivotAnchor 117
int styleable ConstraintSet_polarRelativeTo 118
int styleable ConstraintSet_quantizeMotionSteps 119
int styleable ConstraintSet_transitionEasing 120
int styleable ConstraintSet_transitionPathRotate 121
int[] styleable CoordinatorLayout { 0x7f030258, 0x7f0303e2 }
int styleable CoordinatorLayout_keylines 0
int styleable CoordinatorLayout_statusBarBackground 1
int[] styleable CoordinatorLayout_Layout { 0x010100b3, 0x7f030263, 0x7f030264, 0x7f030265, 0x7f030296, 0x7f0302a0, 0x7f0302a1 }
int styleable CoordinatorLayout_Layout_android_layout_gravity 0
int styleable CoordinatorLayout_Layout_layout_anchor 1
int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
int styleable CoordinatorLayout_Layout_layout_behavior 3
int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
int styleable CoordinatorLayout_Layout_layout_insetEdge 5
int styleable CoordinatorLayout_Layout_layout_keyline 6
int[] styleable CustomAttribute { 0x7f03003a, 0x7f03015a, 0x7f03015b, 0x7f03015c, 0x7f03015d, 0x7f03015e, 0x7f03015f, 0x7f030161, 0x7f030162, 0x7f030163, 0x7f030300 }
int styleable CustomAttribute_attributeName 0
int styleable CustomAttribute_customBoolean 1
int styleable CustomAttribute_customColorDrawableValue 2
int styleable CustomAttribute_customColorValue 3
int styleable CustomAttribute_customDimension 4
int styleable CustomAttribute_customFloatValue 5
int styleable CustomAttribute_customIntegerValue 6
int styleable CustomAttribute_customPixelDimension 7
int styleable CustomAttribute_customReference 8
int styleable CustomAttribute_customStringValue 9
int styleable CustomAttribute_methodName 10
int[] styleable DrawerArrowToggle { 0x7f030038, 0x7f030039, 0x7f03005e, 0x7f0300ec, 0x7f030184, 0x7f030202, 0x7f0303c6, 0x7f03045d }
int styleable DrawerArrowToggle_arrowHeadLength 0
int styleable DrawerArrowToggle_arrowShaftLength 1
int styleable DrawerArrowToggle_barLength 2
int styleable DrawerArrowToggle_color 3
int styleable DrawerArrowToggle_drawableSize 4
int styleable DrawerArrowToggle_gapBetweenBars 5
int styleable DrawerArrowToggle_spinBars 6
int styleable DrawerArrowToggle_thickness 7
int[] styleable DrawerLayout { 0x7f030193 }
int styleable DrawerLayout_elevation 0
int[] styleable ExtendedFloatingActionButton { 0x7f0300e3, 0x7f030193, 0x7f0301b9, 0x7f0301ba, 0x7f03020f, 0x7f0303b2, 0x7f0303b6 }
int styleable ExtendedFloatingActionButton_collapsedSize 0
int styleable ExtendedFloatingActionButton_elevation 1
int styleable ExtendedFloatingActionButton_extendMotionSpec 2
int styleable ExtendedFloatingActionButton_extendStrategy 3
int styleable ExtendedFloatingActionButton_hideMotionSpec 4
int styleable ExtendedFloatingActionButton_showMotionSpec 5
int styleable ExtendedFloatingActionButton_shrinkMotionSpec 6
int[] styleable ExtendedFloatingActionButton_Behavior_Layout { 0x7f030062, 0x7f030063 }
int styleable ExtendedFloatingActionButton_Behavior_Layout_behavior_autoHide 0
int styleable ExtendedFloatingActionButton_Behavior_Layout_behavior_autoShrink 1
int[] styleable FloatingActionButton { 0x0101000e, 0x7f03004d, 0x7f03004e, 0x7f030071, 0x7f030193, 0x7f0301a3, 0x7f0301c8, 0x7f0301c9, 0x7f03020f, 0x7f03021b, 0x7f0302f8, 0x7f030375, 0x7f03038e, 0x7f0303a1, 0x7f0303a9, 0x7f0303b2, 0x7f0304a8 }
int styleable FloatingActionButton_android_enabled 0
int styleable FloatingActionButton_backgroundTint 1
int styleable FloatingActionButton_backgroundTintMode 2
int styleable FloatingActionButton_borderWidth 3
int styleable FloatingActionButton_elevation 4
int styleable FloatingActionButton_ensureMinTouchTargetSize 5
int styleable FloatingActionButton_fabCustomSize 6
int styleable FloatingActionButton_fabSize 7
int styleable FloatingActionButton_hideMotionSpec 8
int styleable FloatingActionButton_hoveredFocusedTranslationZ 9
int styleable FloatingActionButton_maxImageSize 10
int styleable FloatingActionButton_pressedTranslationZ 11
int styleable FloatingActionButton_rippleColor 12
int styleable FloatingActionButton_shapeAppearance 13
int styleable FloatingActionButton_shapeAppearanceOverlay 14
int styleable FloatingActionButton_showMotionSpec 15
int styleable FloatingActionButton_useCompatPadding 16
int[] styleable FloatingActionButton_Behavior_Layout { 0x7f030062 }
int styleable FloatingActionButton_Behavior_Layout_behavior_autoHide 0
int[] styleable FlowLayout { 0x7f03024e, 0x7f0302ad }
int styleable FlowLayout_itemSpacing 0
int styleable FlowLayout_lineSpacing 1
int[] styleable FontFamily { 0x7f0301f4, 0x7f0301f5, 0x7f0301f6, 0x7f0301f7, 0x7f0301f8, 0x7f0301f9, 0x7f0301fa }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int styleable FontFamily_fontProviderSystemFontFamily 6
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f0301f2, 0x7f0301fb, 0x7f0301fc, 0x7f0301fd, 0x7f0304a6 }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable ForegroundLinearLayout { 0x01010109, 0x01010200, 0x7f030200 }
int styleable ForegroundLinearLayout_android_foreground 0
int styleable ForegroundLinearLayout_android_foregroundGravity 1
int styleable ForegroundLinearLayout_foregroundInsidePadding 2
int[] styleable Fragment { 0x01010003, 0x010100d0, 0x010100d1 }
int styleable Fragment_android_name 0
int styleable Fragment_android_id 1
int styleable Fragment_android_tag 2
int[] styleable FragmentContainerView { 0x01010003, 0x010100d1 }
int styleable FragmentContainerView_android_name 0
int styleable FragmentContainerView_android_tag 1
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable ImageFilterView { 0x7f03002f, 0x7f03006e, 0x7f030084, 0x7f030142, 0x7f030157, 0x7f030228, 0x7f030229, 0x7f03022a, 0x7f03022b, 0x7f03034f, 0x7f030390, 0x7f030391, 0x7f030392, 0x7f0304b5 }
int styleable ImageFilterView_altSrc 0
int styleable ImageFilterView_blendSrc 1
int styleable ImageFilterView_brightness 2
int styleable ImageFilterView_contrast 3
int styleable ImageFilterView_crossfade 4
int styleable ImageFilterView_imagePanX 5
int styleable ImageFilterView_imagePanY 6
int styleable ImageFilterView_imageRotate 7
int styleable ImageFilterView_imageZoom 8
int styleable ImageFilterView_overlay 9
int styleable ImageFilterView_round 10
int styleable ImageFilterView_roundPercent 11
int styleable ImageFilterView_saturation 12
int styleable ImageFilterView_warmth 13
int[] styleable Insets { 0x7f0302c3, 0x7f0302c4, 0x7f0302c5, 0x7f030351, 0x7f030353, 0x7f030354, 0x7f030357 }
int styleable Insets_marginLeftSystemWindowInsets 0
int styleable Insets_marginRightSystemWindowInsets 1
int styleable Insets_marginTopSystemWindowInsets 2
int styleable Insets_paddingBottomSystemWindowInsets 3
int styleable Insets_paddingLeftSystemWindowInsets 4
int styleable Insets_paddingRightSystemWindowInsets 5
int styleable Insets_paddingTopSystemWindowInsets 6
int[] styleable KeyAttribute { 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f030159, 0x7f030201, 0x7f030334, 0x7f030336, 0x7f03049d, 0x7f03049f, 0x7f0304a1 }
int styleable KeyAttribute_android_alpha 0
int styleable KeyAttribute_android_transformPivotX 1
int styleable KeyAttribute_android_transformPivotY 2
int styleable KeyAttribute_android_translationX 3
int styleable KeyAttribute_android_translationY 4
int styleable KeyAttribute_android_scaleX 5
int styleable KeyAttribute_android_scaleY 6
int styleable KeyAttribute_android_rotation 7
int styleable KeyAttribute_android_rotationX 8
int styleable KeyAttribute_android_rotationY 9
int styleable KeyAttribute_android_translationZ 10
int styleable KeyAttribute_android_elevation 11
int styleable KeyAttribute_curveFit 12
int styleable KeyAttribute_framePosition 13
int styleable KeyAttribute_motionProgress 14
int styleable KeyAttribute_motionTarget 15
int styleable KeyAttribute_transformPivotTarget 16
int styleable KeyAttribute_transitionEasing 17
int styleable KeyAttribute_transitionPathRotate 18
int[] styleable KeyCycle { 0x0101031f, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f030159, 0x7f030201, 0x7f030334, 0x7f030336, 0x7f03049f, 0x7f0304a1, 0x7f0304b7, 0x7f0304b8, 0x7f0304b9, 0x7f0304ba, 0x7f0304bb }
int styleable KeyCycle_android_alpha 0
int styleable KeyCycle_android_translationX 1
int styleable KeyCycle_android_translationY 2
int styleable KeyCycle_android_scaleX 3
int styleable KeyCycle_android_scaleY 4
int styleable KeyCycle_android_rotation 5
int styleable KeyCycle_android_rotationX 6
int styleable KeyCycle_android_rotationY 7
int styleable KeyCycle_android_translationZ 8
int styleable KeyCycle_android_elevation 9
int styleable KeyCycle_curveFit 10
int styleable KeyCycle_framePosition 11
int styleable KeyCycle_motionProgress 12
int styleable KeyCycle_motionTarget 13
int styleable KeyCycle_transitionEasing 14
int styleable KeyCycle_transitionPathRotate 15
int styleable KeyCycle_waveOffset 16
int styleable KeyCycle_wavePeriod 17
int styleable KeyCycle_wavePhase 18
int styleable KeyCycle_waveShape 19
int styleable KeyCycle_waveVariesBy 20
int[] styleable KeyFrame { }
int[] styleable KeyFramesAcceleration { }
int[] styleable KeyFramesVelocity { }
int[] styleable KeyPosition { 0x7f030159, 0x7f03017f, 0x7f030201, 0x7f030256, 0x7f030336, 0x7f030360, 0x7f030362, 0x7f030363, 0x7f030364, 0x7f030365, 0x7f0303c0, 0x7f03049f }
int styleable KeyPosition_curveFit 0
int styleable KeyPosition_drawPath 1
int styleable KeyPosition_framePosition 2
int styleable KeyPosition_keyPositionType 3
int styleable KeyPosition_motionTarget 4
int styleable KeyPosition_pathMotionArc 5
int styleable KeyPosition_percentHeight 6
int styleable KeyPosition_percentWidth 7
int styleable KeyPosition_percentX 8
int styleable KeyPosition_percentY 9
int styleable KeyPosition_sizePercent 10
int styleable KeyPosition_transitionEasing 11
int[] styleable KeyTimeCycle { 0x0101031f, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f030159, 0x7f030201, 0x7f030334, 0x7f030336, 0x7f03049f, 0x7f0304a1, 0x7f0304b6, 0x7f0304b7, 0x7f0304b8, 0x7f0304b9, 0x7f0304ba }
int styleable KeyTimeCycle_android_alpha 0
int styleable KeyTimeCycle_android_translationX 1
int styleable KeyTimeCycle_android_translationY 2
int styleable KeyTimeCycle_android_scaleX 3
int styleable KeyTimeCycle_android_scaleY 4
int styleable KeyTimeCycle_android_rotation 5
int styleable KeyTimeCycle_android_rotationX 6
int styleable KeyTimeCycle_android_rotationY 7
int styleable KeyTimeCycle_android_translationZ 8
int styleable KeyTimeCycle_android_elevation 9
int styleable KeyTimeCycle_curveFit 10
int styleable KeyTimeCycle_framePosition 11
int styleable KeyTimeCycle_motionProgress 12
int styleable KeyTimeCycle_motionTarget 13
int styleable KeyTimeCycle_transitionEasing 14
int styleable KeyTimeCycle_transitionPathRotate 15
int styleable KeyTimeCycle_waveDecay 16
int styleable KeyTimeCycle_waveOffset 17
int styleable KeyTimeCycle_wavePeriod 18
int styleable KeyTimeCycle_wavePhase 19
int styleable KeyTimeCycle_waveShape 20
int[] styleable KeyTrigger { 0x7f030201, 0x7f030336, 0x7f030337, 0x7f030338, 0x7f030347, 0x7f030349, 0x7f03034a, 0x7f0304a3, 0x7f0304a4, 0x7f0304a5, 0x7f0304b0, 0x7f0304b1, 0x7f0304b2 }
int styleable KeyTrigger_framePosition 0
int styleable KeyTrigger_motionTarget 1
int styleable KeyTrigger_motion_postLayoutCollision 2
int styleable KeyTrigger_motion_triggerOnCollision 3
int styleable KeyTrigger_onCross 4
int styleable KeyTrigger_onNegativeCross 5
int styleable KeyTrigger_onPositiveCross 6
int styleable KeyTrigger_triggerId 7
int styleable KeyTrigger_triggerReceiver 8
int styleable KeyTrigger_triggerSlack 9
int styleable KeyTrigger_viewTransitionOnCross 10
int styleable KeyTrigger_viewTransitionOnNegativeCross 11
int styleable KeyTrigger_viewTransitionOnPositiveCross 12
int[] styleable Layout { 0x010100c4, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x010103b5, 0x010103b6, 0x7f03005f, 0x7f030060, 0x7f030061, 0x7f0300a8, 0x7f03012f, 0x7f030130, 0x7f030205, 0x7f030268, 0x7f030269, 0x7f03026a, 0x7f03026b, 0x7f03026c, 0x7f03026d, 0x7f03026e, 0x7f03026f, 0x7f030270, 0x7f030271, 0x7f030272, 0x7f030273, 0x7f030274, 0x7f030275, 0x7f030276, 0x7f030277, 0x7f030278, 0x7f030279, 0x7f03027a, 0x7f03027b, 0x7f03027c, 0x7f03027d, 0x7f03027e, 0x7f03027f, 0x7f030280, 0x7f030281, 0x7f030282, 0x7f030283, 0x7f030284, 0x7f030285, 0x7f030286, 0x7f030287, 0x7f030288, 0x7f030289, 0x7f03028b, 0x7f03028c, 0x7f03028d, 0x7f03028e, 0x7f03028f, 0x7f030290, 0x7f030291, 0x7f030292, 0x7f030293, 0x7f030294, 0x7f030295, 0x7f030297, 0x7f030298, 0x7f030299, 0x7f03029a, 0x7f03029b, 0x7f03029c, 0x7f03029d, 0x7f03029e, 0x7f03029f, 0x7f0302a2, 0x7f0302a7, 0x7f0302f7, 0x7f0302fb, 0x7f030301, 0x7f030305 }
int styleable Layout_android_orientation 0
int styleable Layout_android_layout_width 1
int styleable Layout_android_layout_height 2
int styleable Layout_android_layout_marginLeft 3
int styleable Layout_android_layout_marginTop 4
int styleable Layout_android_layout_marginRight 5
int styleable Layout_android_layout_marginBottom 6
int styleable Layout_android_layout_marginStart 7
int styleable Layout_android_layout_marginEnd 8
int styleable Layout_barrierAllowsGoneWidgets 9
int styleable Layout_barrierDirection 10
int styleable Layout_barrierMargin 11
int styleable Layout_chainUseRtl 12
int styleable Layout_constraint_referenced_ids 13
int styleable Layout_constraint_referenced_tags 14
int styleable Layout_guidelineUseRtl 15
int styleable Layout_layout_constrainedHeight 16
int styleable Layout_layout_constrainedWidth 17
int styleable Layout_layout_constraintBaseline_creator 18
int styleable Layout_layout_constraintBaseline_toBaselineOf 19
int styleable Layout_layout_constraintBaseline_toBottomOf 20
int styleable Layout_layout_constraintBaseline_toTopOf 21
int styleable Layout_layout_constraintBottom_creator 22
int styleable Layout_layout_constraintBottom_toBottomOf 23
int styleable Layout_layout_constraintBottom_toTopOf 24
int styleable Layout_layout_constraintCircle 25
int styleable Layout_layout_constraintCircleAngle 26
int styleable Layout_layout_constraintCircleRadius 27
int styleable Layout_layout_constraintDimensionRatio 28
int styleable Layout_layout_constraintEnd_toEndOf 29
int styleable Layout_layout_constraintEnd_toStartOf 30
int styleable Layout_layout_constraintGuide_begin 31
int styleable Layout_layout_constraintGuide_end 32
int styleable Layout_layout_constraintGuide_percent 33
int styleable Layout_layout_constraintHeight 34
int styleable Layout_layout_constraintHeight_default 35
int styleable Layout_layout_constraintHeight_max 36
int styleable Layout_layout_constraintHeight_min 37
int styleable Layout_layout_constraintHeight_percent 38
int styleable Layout_layout_constraintHorizontal_bias 39
int styleable Layout_layout_constraintHorizontal_chainStyle 40
int styleable Layout_layout_constraintHorizontal_weight 41
int styleable Layout_layout_constraintLeft_creator 42
int styleable Layout_layout_constraintLeft_toLeftOf 43
int styleable Layout_layout_constraintLeft_toRightOf 44
int styleable Layout_layout_constraintRight_creator 45
int styleable Layout_layout_constraintRight_toLeftOf 46
int styleable Layout_layout_constraintRight_toRightOf 47
int styleable Layout_layout_constraintStart_toEndOf 48
int styleable Layout_layout_constraintStart_toStartOf 49
int styleable Layout_layout_constraintTop_creator 50
int styleable Layout_layout_constraintTop_toBottomOf 51
int styleable Layout_layout_constraintTop_toTopOf 52
int styleable Layout_layout_constraintVertical_bias 53
int styleable Layout_layout_constraintVertical_chainStyle 54
int styleable Layout_layout_constraintVertical_weight 55
int styleable Layout_layout_constraintWidth 56
int styleable Layout_layout_constraintWidth_default 57
int styleable Layout_layout_constraintWidth_max 58
int styleable Layout_layout_constraintWidth_min 59
int styleable Layout_layout_constraintWidth_percent 60
int styleable Layout_layout_editor_absoluteX 61
int styleable Layout_layout_editor_absoluteY 62
int styleable Layout_layout_goneMarginBaseline 63
int styleable Layout_layout_goneMarginBottom 64
int styleable Layout_layout_goneMarginEnd 65
int styleable Layout_layout_goneMarginLeft 66
int styleable Layout_layout_goneMarginRight 67
int styleable Layout_layout_goneMarginStart 68
int styleable Layout_layout_goneMarginTop 69
int styleable Layout_layout_marginBaseline 70
int styleable Layout_layout_wrapBehaviorInParent 71
int styleable Layout_maxHeight 72
int styleable Layout_maxWidth 73
int styleable Layout_minHeight 74
int styleable Layout_minWidth 75
int[] styleable LinearLayoutCompat { 0x010100af, 0x010100c4, 0x01010126, 0x01010127, 0x01010128, 0x7f030174, 0x7f030179, 0x7f0302fc, 0x7f0303b1 }
int styleable LinearLayoutCompat_android_gravity 0
int styleable LinearLayoutCompat_android_orientation 1
int styleable LinearLayoutCompat_android_baselineAligned 2
int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 3
int styleable LinearLayoutCompat_android_weightSum 4
int styleable LinearLayoutCompat_divider 5
int styleable LinearLayoutCompat_dividerPadding 6
int styleable LinearLayoutCompat_measureWithLargestChild 7
int styleable LinearLayoutCompat_showDividers 8
int[] styleable LinearLayoutCompat_Layout { 0x010100b3, 0x010100f4, 0x010100f5, 0x01010181 }
int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
int styleable LinearLayoutCompat_Layout_android_layout_width 1
int styleable LinearLayoutCompat_Layout_android_layout_height 2
int styleable LinearLayoutCompat_Layout_android_layout_weight 3
int[] styleable LinearProgressIndicator { 0x7f03022c, 0x7f030230 }
int styleable LinearProgressIndicator_indeterminateAnimationType 0
int styleable LinearProgressIndicator_indicatorDirectionLinear 1
int[] styleable ListPopupWindow { 0x010102ac, 0x010102ad }
int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
int styleable ListPopupWindow_android_dropDownVerticalOffset 1
int[] styleable MaterialAlertDialog { 0x7f030046, 0x7f030047, 0x7f030048, 0x7f030049 }
int styleable MaterialAlertDialog_backgroundInsetBottom 0
int styleable MaterialAlertDialog_backgroundInsetEnd 1
int styleable MaterialAlertDialog_backgroundInsetStart 2
int styleable MaterialAlertDialog_backgroundInsetTop 3
int[] styleable MaterialAlertDialogTheme { 0x7f0302c6, 0x7f0302c7, 0x7f0302c8, 0x7f0302c9, 0x7f0302ca, 0x7f0302cb }
int styleable MaterialAlertDialogTheme_materialAlertDialogBodyTextStyle 0
int styleable MaterialAlertDialogTheme_materialAlertDialogButtonSpacerVisibility 1
int styleable MaterialAlertDialogTheme_materialAlertDialogTheme 2
int styleable MaterialAlertDialogTheme_materialAlertDialogTitleIconStyle 3
int styleable MaterialAlertDialogTheme_materialAlertDialogTitlePanelStyle 4
int styleable MaterialAlertDialogTheme_materialAlertDialogTitleTextStyle 5
int[] styleable MaterialAutoCompleteTextView { 0x01010220, 0x0101048c, 0x7f0303b9, 0x7f0303ba, 0x7f0303bb, 0x7f0303bc }
int styleable MaterialAutoCompleteTextView_android_inputType 0
int styleable MaterialAutoCompleteTextView_android_popupElevation 1
int styleable MaterialAutoCompleteTextView_simpleItemLayout 2
int styleable MaterialAutoCompleteTextView_simpleItemSelectedColor 3
int styleable MaterialAutoCompleteTextView_simpleItemSelectedRippleColor 4
int styleable MaterialAutoCompleteTextView_simpleItems 5
int[] styleable MaterialButton { 0x010100d4, 0x010101b7, 0x010101b8, 0x010101b9, 0x010101ba, 0x010101e5, 0x7f03004d, 0x7f03004e, 0x7f03014b, 0x7f030193, 0x7f03021c, 0x7f03021e, 0x7f03021f, 0x7f030220, 0x7f030222, 0x7f030223, 0x7f03038e, 0x7f0303a1, 0x7f0303a9, 0x7f0303e5, 0x7f0303e6, 0x7f030484 }
int styleable MaterialButton_android_background 0
int styleable MaterialButton_android_insetLeft 1
int styleable MaterialButton_android_insetRight 2
int styleable MaterialButton_android_insetTop 3
int styleable MaterialButton_android_insetBottom 4
int styleable MaterialButton_android_checkable 5
int styleable MaterialButton_backgroundTint 6
int styleable MaterialButton_backgroundTintMode 7
int styleable MaterialButton_cornerRadius 8
int styleable MaterialButton_elevation 9
int styleable MaterialButton_icon 10
int styleable MaterialButton_iconGravity 11
int styleable MaterialButton_iconPadding 12
int styleable MaterialButton_iconSize 13
int styleable MaterialButton_iconTint 14
int styleable MaterialButton_iconTintMode 15
int styleable MaterialButton_rippleColor 16
int styleable MaterialButton_shapeAppearance 17
int styleable MaterialButton_shapeAppearanceOverlay 18
int styleable MaterialButton_strokeColor 19
int styleable MaterialButton_strokeWidth 20
int styleable MaterialButton_toggleCheckedStateOnClick 21
int[] styleable MaterialButtonToggleGroup { 0x0101000e, 0x7f0300ad, 0x7f03039e, 0x7f0303bf }
int styleable MaterialButtonToggleGroup_android_enabled 0
int styleable MaterialButtonToggleGroup_checkedButton 1
int styleable MaterialButtonToggleGroup_selectionRequired 2
int styleable MaterialButtonToggleGroup_singleSelection 3
int[] styleable MaterialCalendar { 0x0101020d, 0x7f030164, 0x7f030165, 0x7f030166, 0x7f030167, 0x7f030343, 0x7f03037f, 0x7f0304c6, 0x7f0304c7, 0x7f0304c8 }
int styleable MaterialCalendar_android_windowFullscreen 0
int styleable MaterialCalendar_dayInvalidStyle 1
int styleable MaterialCalendar_daySelectedStyle 2
int styleable MaterialCalendar_dayStyle 3
int styleable MaterialCalendar_dayTodayStyle 4
int styleable MaterialCalendar_nestedScrollable 5
int styleable MaterialCalendar_rangeFillColor 6
int styleable MaterialCalendar_yearSelectedStyle 7
int styleable MaterialCalendar_yearStyle 8
int styleable MaterialCalendar_yearTodayStyle 9
int[] styleable MaterialCalendarItem { 0x010101b7, 0x010101b8, 0x010101b9, 0x010101ba, 0x7f03023b, 0x7f030247, 0x7f030248, 0x7f03024f, 0x7f030250, 0x7f030254 }
int styleable MaterialCalendarItem_android_insetLeft 0
int styleable MaterialCalendarItem_android_insetRight 1
int styleable MaterialCalendarItem_android_insetTop 2
int styleable MaterialCalendarItem_android_insetBottom 3
int styleable MaterialCalendarItem_itemFillColor 4
int styleable MaterialCalendarItem_itemShapeAppearance 5
int styleable MaterialCalendarItem_itemShapeAppearanceOverlay 6
int styleable MaterialCalendarItem_itemStrokeColor 7
int styleable MaterialCalendarItem_itemStrokeWidth 8
int styleable MaterialCalendarItem_itemTextColor 9
int[] styleable MaterialCardView { 0x010101e5, 0x7f030098, 0x7f0300af, 0x7f0300b1, 0x7f0300b2, 0x7f0300b3, 0x7f0300b4, 0x7f03038e, 0x7f0303a1, 0x7f0303a9, 0x7f0303dc, 0x7f0303e5, 0x7f0303e6 }
int styleable MaterialCardView_android_checkable 0
int styleable MaterialCardView_cardForegroundColor 1
int styleable MaterialCardView_checkedIcon 2
int styleable MaterialCardView_checkedIconGravity 3
int styleable MaterialCardView_checkedIconMargin 4
int styleable MaterialCardView_checkedIconSize 5
int styleable MaterialCardView_checkedIconTint 6
int styleable MaterialCardView_rippleColor 7
int styleable MaterialCardView_shapeAppearance 8
int styleable MaterialCardView_shapeAppearanceOverlay 9
int styleable MaterialCardView_state_dragged 10
int styleable MaterialCardView_strokeColor 11
int styleable MaterialCardView_strokeWidth 12
int[] styleable MaterialCheckBox { 0x01010107, 0x7f03008a, 0x7f03008c, 0x7f03008e, 0x7f03008f, 0x7f030093, 0x7f0300a7, 0x7f0300b6, 0x7f0301a4, 0x7f0301ab, 0x7f0304aa }
int styleable MaterialCheckBox_android_button 0
int styleable MaterialCheckBox_buttonCompat 1
int styleable MaterialCheckBox_buttonIcon 2
int styleable MaterialCheckBox_buttonIconTint 3
int styleable MaterialCheckBox_buttonIconTintMode 4
int styleable MaterialCheckBox_buttonTint 5
int styleable MaterialCheckBox_centerIfNoTextEnabled 6
int styleable MaterialCheckBox_checkedState 7
int styleable MaterialCheckBox_errorAccessibilityLabel 8
int styleable MaterialCheckBox_errorShown 9
int styleable MaterialCheckBox_useMaterialThemeColors 10
int[] styleable MaterialCheckBoxStates { 0x7f0303dd, 0x7f0303de }
int styleable MaterialCheckBoxStates_state_error 0
int styleable MaterialCheckBoxStates_state_indeterminate 1
int[] styleable MaterialDivider { 0x7f030175, 0x7f030177, 0x7f030178, 0x7f03017a, 0x7f03025e }
int styleable MaterialDivider_dividerColor 0
int styleable MaterialDivider_dividerInsetEnd 1
int styleable MaterialDivider_dividerInsetStart 2
int styleable MaterialDivider_dividerThickness 3
int styleable MaterialDivider_lastItemDecorated 4
int[] styleable MaterialRadioButton { 0x7f030093, 0x7f0304aa }
int styleable MaterialRadioButton_buttonTint 0
int styleable MaterialRadioButton_useMaterialThemeColors 1
int[] styleable MaterialShape { 0x7f0303a1, 0x7f0303a9 }
int styleable MaterialShape_shapeAppearance 0
int styleable MaterialShape_shapeAppearanceOverlay 1
int[] styleable MaterialSwitch { 0x7f030460, 0x7f030461, 0x7f030462, 0x7f030496, 0x7f030497, 0x7f030498 }
int styleable MaterialSwitch_thumbIcon 0
int styleable MaterialSwitch_thumbIconTint 1
int styleable MaterialSwitch_thumbIconTintMode 2
int styleable MaterialSwitch_trackDecoration 3
int styleable MaterialSwitch_trackDecorationTint 4
int styleable MaterialSwitch_trackDecorationTintMode 5
int[] styleable MaterialTextAppearance { 0x010104b6, 0x0101057f, 0x7f0302ac }
int styleable MaterialTextAppearance_android_letterSpacing 0
int styleable MaterialTextAppearance_android_lineHeight 1
int styleable MaterialTextAppearance_lineHeight 2
int[] styleable MaterialTextView { 0x01010034, 0x0101057f, 0x7f0302ac }
int styleable MaterialTextView_android_textAppearance 0
int styleable MaterialTextView_android_lineHeight 1
int styleable MaterialTextView_lineHeight 2
int[] styleable MaterialTimePicker { 0x7f0300d7, 0x7f030257 }
int styleable MaterialTimePicker_clockIcon 0
int styleable MaterialTimePicker_keyboardIcon 1
int[] styleable MaterialToolbar { 0x7f0302bf, 0x7f0302c1, 0x7f03033d, 0x7f0303ee, 0x7f030476 }
int styleable MaterialToolbar_logoAdjustViewBounds 0
int styleable MaterialToolbar_logoScaleType 1
int styleable MaterialToolbar_navigationIconTint 2
int styleable MaterialToolbar_subtitleCentered 3
int styleable MaterialToolbar_titleCentered 4
int[] styleable MenuGroup { 0x0101000e, 0x010100d0, 0x01010194, 0x010101de, 0x010101df, 0x010101e0 }
int styleable MenuGroup_android_enabled 0
int styleable MenuGroup_android_id 1
int styleable MenuGroup_android_visible 2
int styleable MenuGroup_android_menuCategory 3
int styleable MenuGroup_android_orderInCategory 4
int styleable MenuGroup_android_checkableBehavior 5
int[] styleable MenuItem { 0x01010002, 0x0101000e, 0x010100d0, 0x01010106, 0x01010194, 0x010101de, 0x010101df, 0x010101e1, 0x010101e2, 0x010101e3, 0x010101e4, 0x010101e5, 0x0101026f, 0x7f03000f, 0x7f030023, 0x7f030025, 0x7f03002e, 0x7f030133, 0x7f030222, 0x7f030223, 0x7f030345, 0x7f0303af, 0x7f03048c }
int styleable MenuItem_android_icon 0
int styleable MenuItem_android_enabled 1
int styleable MenuItem_android_id 2
int styleable MenuItem_android_checked 3
int styleable MenuItem_android_visible 4
int styleable MenuItem_android_menuCategory 5
int styleable MenuItem_android_orderInCategory 6
int styleable MenuItem_android_title 7
int styleable MenuItem_android_titleCondensed 8
int styleable MenuItem_android_alphabeticShortcut 9
int styleable MenuItem_android_numericShortcut 10
int styleable MenuItem_android_checkable 11
int styleable MenuItem_android_onClick 12
int styleable MenuItem_actionLayout 13
int styleable MenuItem_actionProviderClass 14
int styleable MenuItem_actionViewClass 15
int styleable MenuItem_alphabeticModifiers 16
int styleable MenuItem_contentDescription 17
int styleable MenuItem_iconTint 18
int styleable MenuItem_iconTintMode 19
int styleable MenuItem_numericModifiers 20
int styleable MenuItem_showAsAction 21
int styleable MenuItem_tooltipText 22
int[] styleable MenuView { 0x010100ae, 0x0101012c, 0x0101012d, 0x0101012e, 0x0101012f, 0x01010130, 0x01010131, 0x7f030374, 0x7f0303e7 }
int styleable MenuView_android_windowAnimationStyle 0
int styleable MenuView_android_itemTextAppearance 1
int styleable MenuView_android_horizontalDivider 2
int styleable MenuView_android_verticalDivider 3
int styleable MenuView_android_headerBackground 4
int styleable MenuView_android_itemBackground 5
int styleable MenuView_android_itemIconDisabledAlpha 6
int styleable MenuView_preserveIconSpacing 7
int styleable MenuView_subMenuArrow 8
int[] styleable MockView { 0x7f030306, 0x7f030307, 0x7f030308, 0x7f030309, 0x7f03030a, 0x7f03030b }
int styleable MockView_mock_diagonalsColor 0
int styleable MockView_mock_label 1
int styleable MockView_mock_labelBackgroundColor 2
int styleable MockView_mock_labelColor 3
int styleable MockView_mock_showDiagonals 4
int styleable MockView_mock_showLabel 5
int[] styleable Motion { 0x7f030030, 0x7f030033, 0x7f03017f, 0x7f030333, 0x7f030335, 0x7f030360, 0x7f030378, 0x7f030379, 0x7f03037a, 0x7f03049f }
int styleable Motion_animateCircleAngleTo 0
int styleable Motion_animateRelativeTo 1
int styleable Motion_drawPath 2
int styleable Motion_motionPathRotate 3
int styleable Motion_motionStagger 4
int styleable Motion_pathMotionArc 5
int styleable Motion_quantizeMotionInterpolator 6
int styleable Motion_quantizeMotionPhase 7
int styleable Motion_quantizeMotionSteps 8
int styleable Motion_transitionEasing 9
int[] styleable MotionEffect { 0x7f030329, 0x7f03032a, 0x7f03032b, 0x7f03032c, 0x7f03032d, 0x7f03032e, 0x7f03032f, 0x7f030330 }
int styleable MotionEffect_motionEffect_alpha 0
int styleable MotionEffect_motionEffect_end 1
int styleable MotionEffect_motionEffect_move 2
int styleable MotionEffect_motionEffect_start 3
int styleable MotionEffect_motionEffect_strict 4
int styleable MotionEffect_motionEffect_translationX 5
int styleable MotionEffect_motionEffect_translationY 6
int styleable MotionEffect_motionEffect_viewTransition 7
int[] styleable MotionHelper { 0x7f030348, 0x7f03034b }
int styleable MotionHelper_onHide 0
int styleable MotionHelper_onShow 1
int[] styleable MotionLabel { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x010100af, 0x0101014f, 0x01010164, 0x010103ac, 0x01010535, 0x7f03006f, 0x7f030070, 0x7f030393, 0x7f030441, 0x7f030442, 0x7f030443, 0x7f030444, 0x7f030445, 0x7f030453, 0x7f030454, 0x7f030455, 0x7f030456, 0x7f030458, 0x7f030459, 0x7f03045a, 0x7f03045b }
int styleable MotionLabel_android_textSize 0
int styleable MotionLabel_android_typeface 1
int styleable MotionLabel_android_textStyle 2
int styleable MotionLabel_android_textColor 3
int styleable MotionLabel_android_gravity 4
int styleable MotionLabel_android_text 5
int styleable MotionLabel_android_shadowRadius 6
int styleable MotionLabel_android_fontFamily 7
int styleable MotionLabel_android_autoSizeTextType 8
int styleable MotionLabel_borderRound 9
int styleable MotionLabel_borderRoundPercent 10
int styleable MotionLabel_scaleFromTextSize 11
int styleable MotionLabel_textBackground 12
int styleable MotionLabel_textBackgroundPanX 13
int styleable MotionLabel_textBackgroundPanY 14
int styleable MotionLabel_textBackgroundRotate 15
int styleable MotionLabel_textBackgroundZoom 16
int styleable MotionLabel_textOutlineColor 17
int styleable MotionLabel_textOutlineThickness 18
int styleable MotionLabel_textPanX 19
int styleable MotionLabel_textPanY 20
int styleable MotionLabel_textureBlurFactor 21
int styleable MotionLabel_textureEffect 22
int styleable MotionLabel_textureHeight 23
int styleable MotionLabel_textureWidth 24
int[] styleable MotionLayout { 0x7f030036, 0x7f030158, 0x7f030260, 0x7f03030c, 0x7f030334, 0x7f0303b3 }
int styleable MotionLayout_applyMotionScene 0
int styleable MotionLayout_currentState 1
int styleable MotionLayout_layoutDescription 2
int styleable MotionLayout_motionDebug 3
int styleable MotionLayout_motionProgress 4
int styleable MotionLayout_showPaths 5
int[] styleable MotionScene { 0x7f030168, 0x7f030261 }
int styleable MotionScene_defaultDuration 0
int styleable MotionScene_layoutDuringTransition 1
int[] styleable MotionTelltales { 0x7f030418, 0x7f030419, 0x7f03041a }
int styleable MotionTelltales_telltales_tailColor 0
int styleable MotionTelltales_telltales_tailScale 1
int styleable MotionTelltales_telltales_velocityMode 2
int[] styleable NavigationBarActiveIndicator { 0x01010155, 0x01010159, 0x010101a5, 0x7f0302c2, 0x7f0303a1 }
int styleable NavigationBarActiveIndicator_android_height 0
int styleable NavigationBarActiveIndicator_android_width 1
int styleable NavigationBarActiveIndicator_android_color 2
int styleable NavigationBarActiveIndicator_marginHorizontal 3
int styleable NavigationBarActiveIndicator_shapeAppearance 4
int[] styleable NavigationBarView { 0x7f03004d, 0x7f030193, 0x7f030239, 0x7f03023a, 0x7f03023f, 0x7f030240, 0x7f030244, 0x7f030245, 0x7f030246, 0x7f030252, 0x7f030253, 0x7f030254, 0x7f03025c, 0x7f0302fd }
int styleable NavigationBarView_backgroundTint 0
int styleable NavigationBarView_elevation 1
int styleable NavigationBarView_itemActiveIndicatorStyle 2
int styleable NavigationBarView_itemBackground 3
int styleable NavigationBarView_itemIconSize 4
int styleable NavigationBarView_itemIconTint 5
int styleable NavigationBarView_itemPaddingBottom 6
int styleable NavigationBarView_itemPaddingTop 7
int styleable NavigationBarView_itemRippleColor 8
int styleable NavigationBarView_itemTextAppearanceActive 9
int styleable NavigationBarView_itemTextAppearanceInactive 10
int styleable NavigationBarView_itemTextColor 11
int styleable NavigationBarView_labelVisibilityMode 12
int styleable NavigationBarView_menu 13
int[] styleable NavigationRailView { 0x7f030208, 0x7f030242, 0x7f0302ff, 0x7f030351, 0x7f030357 }
int styleable NavigationRailView_headerLayout 0
int styleable NavigationRailView_itemMinHeight 1
int styleable NavigationRailView_menuGravity 2
int styleable NavigationRailView_paddingBottomSystemWindowInsets 3
int styleable NavigationRailView_paddingTopSystemWindowInsets 4
int[] styleable NavigationView { 0x010100b3, 0x010100d4, 0x010100dd, 0x0101011f, 0x7f030074, 0x7f030177, 0x7f030178, 0x7f03018a, 0x7f030193, 0x7f030208, 0x7f03023a, 0x7f03023c, 0x7f03023e, 0x7f03023f, 0x7f030240, 0x7f030241, 0x7f030246, 0x7f030247, 0x7f030248, 0x7f030249, 0x7f03024a, 0x7f03024b, 0x7f03024c, 0x7f03024d, 0x7f030251, 0x7f030254, 0x7f030255, 0x7f0302fd, 0x7f0303a1, 0x7f0303a9, 0x7f0303e8, 0x7f0303e9, 0x7f0303ea, 0x7f0303eb, 0x7f03048d }
int styleable NavigationView_android_layout_gravity 0
int styleable NavigationView_android_background 1
int styleable NavigationView_android_fitsSystemWindows 2
int styleable NavigationView_android_maxWidth 3
int styleable NavigationView_bottomInsetScrimEnabled 4
int styleable NavigationView_dividerInsetEnd 5
int styleable NavigationView_dividerInsetStart 6
int styleable NavigationView_drawerLayoutCornerSize 7
int styleable NavigationView_elevation 8
int styleable NavigationView_headerLayout 9
int styleable NavigationView_itemBackground 10
int styleable NavigationView_itemHorizontalPadding 11
int styleable NavigationView_itemIconPadding 12
int styleable NavigationView_itemIconSize 13
int styleable NavigationView_itemIconTint 14
int styleable NavigationView_itemMaxLines 15
int styleable NavigationView_itemRippleColor 16
int styleable NavigationView_itemShapeAppearance 17
int styleable NavigationView_itemShapeAppearanceOverlay 18
int styleable NavigationView_itemShapeFillColor 19
int styleable NavigationView_itemShapeInsetBottom 20
int styleable NavigationView_itemShapeInsetEnd 21
int styleable NavigationView_itemShapeInsetStart 22
int styleable NavigationView_itemShapeInsetTop 23
int styleable NavigationView_itemTextAppearance 24
int styleable NavigationView_itemTextColor 25
int styleable NavigationView_itemVerticalPadding 26
int styleable NavigationView_menu 27
int styleable NavigationView_shapeAppearance 28
int styleable NavigationView_shapeAppearanceOverlay 29
int styleable NavigationView_subheaderColor 30
int styleable NavigationView_subheaderInsetEnd 31
int styleable NavigationView_subheaderInsetStart 32
int styleable NavigationView_subheaderTextAppearance 33
int styleable NavigationView_topInsetScrimEnabled 34
int[] styleable OnClick { 0x7f0300d4, 0x7f030417 }
int styleable OnClick_clickAction 0
int styleable OnClick_targetId 1
int[] styleable OnSwipe { 0x7f03003b, 0x7f03017c, 0x7f03017d, 0x7f03017e, 0x7f0302ab, 0x7f0302f3, 0x7f0302fa, 0x7f030339, 0x7f030341, 0x7f03034d, 0x7f03038f, 0x7f0303ca, 0x7f0303cb, 0x7f0303cc, 0x7f0303cd, 0x7f0303ce, 0x7f03048e, 0x7f03048f, 0x7f030490 }
int styleable OnSwipe_autoCompleteMode 0
int styleable OnSwipe_dragDirection 1
int styleable OnSwipe_dragScale 2
int styleable OnSwipe_dragThreshold 3
int styleable OnSwipe_limitBoundsTo 4
int styleable OnSwipe_maxAcceleration 5
int styleable OnSwipe_maxVelocity 6
int styleable OnSwipe_moveWhenScrollAtTop 7
int styleable OnSwipe_nestedScrollFlags 8
int styleable OnSwipe_onTouchUp 9
int styleable OnSwipe_rotationCenterId 10
int styleable OnSwipe_springBoundary 11
int styleable OnSwipe_springDamping 12
int styleable OnSwipe_springMass 13
int styleable OnSwipe_springStiffness 14
int styleable OnSwipe_springStopThreshold 15
int styleable OnSwipe_touchAnchorId 16
int styleable OnSwipe_touchAnchorSide 17
int styleable OnSwipe_touchRegionId 18
int[] styleable PopupWindow { 0x01010176, 0x010102c9, 0x7f03034e }
int styleable PopupWindow_android_popupBackground 0
int styleable PopupWindow_android_popupAnimationStyle 1
int styleable PopupWindow_overlapAnchor 2
int[] styleable PopupWindowBackgroundState { 0x7f0303d9 }
int styleable PopupWindowBackgroundState_state_above_anchor 0
int[] styleable PropertySet { 0x010100dc, 0x0101031f, 0x7f03028a, 0x7f030334, 0x7f0304b3 }
int styleable PropertySet_android_visibility 0
int styleable PropertySet_android_alpha 1
int styleable PropertySet_layout_constraintTag 2
int styleable PropertySet_motionProgress 3
int styleable PropertySet_visibilityMode 4
int[] styleable RadialViewGroup { 0x7f0302e2 }
int styleable RadialViewGroup_materialCircleRadius 0
int[] styleable RangeSlider { 0x7f030303, 0x7f0304ab }
int styleable RangeSlider_minSeparation 0
int styleable RangeSlider_values 1
int[] styleable RecycleListView { 0x7f030350, 0x7f030356 }
int styleable RecycleListView_paddingBottomNoButtons 0
int styleable RecycleListView_paddingTopNoTitle 1
int[] styleable RecyclerView { 0x010100c4, 0x010100eb, 0x010100f1, 0x7f0301ca, 0x7f0301cb, 0x7f0301cc, 0x7f0301cd, 0x7f0301ce, 0x7f030262, 0x7f03038d, 0x7f0303c5, 0x7f0303d0 }
int styleable RecyclerView_android_orientation 0
int styleable RecyclerView_android_clipToPadding 1
int styleable RecyclerView_android_descendantFocusability 2
int styleable RecyclerView_fastScrollEnabled 3
int styleable RecyclerView_fastScrollHorizontalThumbDrawable 4
int styleable RecyclerView_fastScrollHorizontalTrackDrawable 5
int styleable RecyclerView_fastScrollVerticalThumbDrawable 6
int styleable RecyclerView_fastScrollVerticalTrackDrawable 7
int styleable RecyclerView_layoutManager 8
int styleable RecyclerView_reverseLayout 9
int styleable RecyclerView_spanCount 10
int styleable RecyclerView_stackFromEnd 11
int[] styleable ScrimInsetsFrameLayout { 0x7f030234 }
int styleable ScrimInsetsFrameLayout_insetForeground 0
int[] styleable ScrollingViewBehavior_Layout { 0x7f030069 }
int styleable ScrollingViewBehavior_Layout_behavior_overlapTop 0
int[] styleable SearchBar { 0x01010034, 0x0101014f, 0x01010150, 0x7f030169, 0x7f03016b, 0x7f030193, 0x7f0301ff, 0x7f030210, 0x7f03033d, 0x7f0303e5, 0x7f0303e6, 0x7f030474 }
int styleable SearchBar_android_textAppearance 0
int styleable SearchBar_android_text 1
int styleable SearchBar_android_hint 2
int styleable SearchBar_defaultMarginsEnabled 3
int styleable SearchBar_defaultScrollFlagsEnabled 4
int styleable SearchBar_elevation 5
int styleable SearchBar_forceDefaultNavigationOnClickListener 6
int styleable SearchBar_hideNavigationIcon 7
int styleable SearchBar_navigationIconTint 8
int styleable SearchBar_strokeColor 9
int styleable SearchBar_strokeWidth 10
int styleable SearchBar_tintNavigationIcon 11
int[] styleable SearchView { 0x01010034, 0x010100da, 0x0101011f, 0x0101014f, 0x01010150, 0x01010220, 0x01010264, 0x7f030031, 0x7f030032, 0x7f03003d, 0x7f0300d9, 0x7f030129, 0x7f03016a, 0x7f030204, 0x7f030208, 0x7f030210, 0x7f030224, 0x7f03025f, 0x7f03037b, 0x7f03037c, 0x7f030397, 0x7f030398, 0x7f030399, 0x7f0303ec, 0x7f0303f5, 0x7f0304a9, 0x7f0304b4 }
int styleable SearchView_android_textAppearance 0
int styleable SearchView_android_focusable 1
int styleable SearchView_android_maxWidth 2
int styleable SearchView_android_text 3
int styleable SearchView_android_hint 4
int styleable SearchView_android_inputType 5
int styleable SearchView_android_imeOptions 6
int styleable SearchView_animateMenuItems 7
int styleable SearchView_animateNavigationIcon 8
int styleable SearchView_autoShowKeyboard 9
int styleable SearchView_closeIcon 10
int styleable SearchView_commitIcon 11
int styleable SearchView_defaultQueryHint 12
int styleable SearchView_goIcon 13
int styleable SearchView_headerLayout 14
int styleable SearchView_hideNavigationIcon 15
int styleable SearchView_iconifiedByDefault 16
int styleable SearchView_layout 17
int styleable SearchView_queryBackground 18
int styleable SearchView_queryHint 19
int styleable SearchView_searchHintIcon 20
int styleable SearchView_searchIcon 21
int styleable SearchView_searchPrefixText 22
int styleable SearchView_submitBackground 23
int styleable SearchView_suggestionRowLayout 24
int styleable SearchView_useDrawerArrowDrawable 25
int styleable SearchView_voiceIcon 26
int[] styleable ShapeAppearance { 0x7f030146, 0x7f030147, 0x7f030148, 0x7f030149, 0x7f03014a, 0x7f03014c, 0x7f03014d, 0x7f03014e, 0x7f03014f, 0x7f030150 }
int styleable ShapeAppearance_cornerFamily 0
int styleable ShapeAppearance_cornerFamilyBottomLeft 1
int styleable ShapeAppearance_cornerFamilyBottomRight 2
int styleable ShapeAppearance_cornerFamilyTopLeft 3
int styleable ShapeAppearance_cornerFamilyTopRight 4
int styleable ShapeAppearance_cornerSize 5
int styleable ShapeAppearance_cornerSizeBottomLeft 6
int styleable ShapeAppearance_cornerSizeBottomRight 7
int styleable ShapeAppearance_cornerSizeTopLeft 8
int styleable ShapeAppearance_cornerSizeTopRight 9
int[] styleable ShapeableImageView { 0x7f03013a, 0x7f03013b, 0x7f03013c, 0x7f03013d, 0x7f03013e, 0x7f03013f, 0x7f030140, 0x7f0303a1, 0x7f0303a9, 0x7f0303e5, 0x7f0303e6 }
int styleable ShapeableImageView_contentPadding 0
int styleable ShapeableImageView_contentPaddingBottom 1
int styleable ShapeableImageView_contentPaddingEnd 2
int styleable ShapeableImageView_contentPaddingLeft 3
int styleable ShapeableImageView_contentPaddingRight 4
int styleable ShapeableImageView_contentPaddingStart 5
int styleable ShapeableImageView_contentPaddingTop 6
int styleable ShapeableImageView_shapeAppearance 7
int styleable ShapeableImageView_shapeAppearanceOverlay 8
int styleable ShapeableImageView_strokeColor 9
int styleable ShapeableImageView_strokeWidth 10
int[] styleable SideSheetBehavior_Layout { 0x0101011f, 0x01010120, 0x01010440, 0x7f03004d, 0x7f030064, 0x7f030145, 0x7f0303a1, 0x7f0303a9 }
int styleable SideSheetBehavior_Layout_android_maxWidth 0
int styleable SideSheetBehavior_Layout_android_maxHeight 1
int styleable SideSheetBehavior_Layout_android_elevation 2
int styleable SideSheetBehavior_Layout_backgroundTint 3
int styleable SideSheetBehavior_Layout_behavior_draggable 4
int styleable SideSheetBehavior_Layout_coplanarSiblingViewId 5
int styleable SideSheetBehavior_Layout_shapeAppearance 6
int styleable SideSheetBehavior_Layout_shapeAppearanceOverlay 7
int[] styleable Slider { 0x0101000e, 0x01010024, 0x01010146, 0x010102de, 0x010102df, 0x7f030206, 0x7f030207, 0x7f03025a, 0x7f03025b, 0x7f030304, 0x7f03045e, 0x7f03045f, 0x7f030463, 0x7f030464, 0x7f030465, 0x7f030469, 0x7f03046a, 0x7f03046b, 0x7f03046f, 0x7f030470, 0x7f030471, 0x7f030492, 0x7f030493, 0x7f030494, 0x7f030499 }
int styleable Slider_android_enabled 0
int styleable Slider_android_value 1
int styleable Slider_android_stepSize 2
int styleable Slider_android_valueFrom 3
int styleable Slider_android_valueTo 4
int styleable Slider_haloColor 5
int styleable Slider_haloRadius 6
int styleable Slider_labelBehavior 7
int styleable Slider_labelStyle 8
int styleable Slider_minTouchTargetSize 9
int styleable Slider_thumbColor 10
int styleable Slider_thumbElevation 11
int styleable Slider_thumbRadius 12
int styleable Slider_thumbStrokeColor 13
int styleable Slider_thumbStrokeWidth 14
int styleable Slider_tickColor 15
int styleable Slider_tickColorActive 16
int styleable Slider_tickColorInactive 17
int styleable Slider_tickRadiusActive 18
int styleable Slider_tickRadiusInactive 19
int styleable Slider_tickVisible 20
int styleable Slider_trackColor 21
int styleable Slider_trackColorActive 22
int styleable Slider_trackColorInactive 23
int styleable Slider_trackHeight 24
int[] styleable Snackbar { 0x7f0303c2, 0x7f0303c3, 0x7f0303c4 }
int styleable Snackbar_snackbarButtonStyle 0
int styleable Snackbar_snackbarStyle 1
int styleable Snackbar_snackbarTextViewStyle 2
int[] styleable SnackbarLayout { 0x0101011f, 0x7f030024, 0x7f030034, 0x7f03004a, 0x7f03004d, 0x7f03004e, 0x7f030193, 0x7f0302f4, 0x7f0303a1, 0x7f0303a9 }
int styleable SnackbarLayout_android_maxWidth 0
int styleable SnackbarLayout_actionTextColorAlpha 1
int styleable SnackbarLayout_animationMode 2
int styleable SnackbarLayout_backgroundOverlayColorAlpha 3
int styleable SnackbarLayout_backgroundTint 4
int styleable SnackbarLayout_backgroundTintMode 5
int styleable SnackbarLayout_elevation 6
int styleable SnackbarLayout_maxActionInlineWidth 7
int styleable SnackbarLayout_shapeAppearance 8
int styleable SnackbarLayout_shapeAppearanceOverlay 9
int[] styleable Spinner { 0x010100b2, 0x01010176, 0x0101017b, 0x01010262, 0x7f03036f }
int styleable Spinner_android_entries 0
int styleable Spinner_android_popupBackground 1
int styleable Spinner_android_prompt 2
int styleable Spinner_android_dropDownWidth 3
int styleable Spinner_popupTheme 4
int[] styleable State { 0x010100d0, 0x7f030131 }
int styleable State_android_id 0
int styleable State_constraints 1
int[] styleable StateListDrawable { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable StateListDrawable_android_dither 0
int styleable StateListDrawable_android_visible 1
int styleable StateListDrawable_android_variablePadding 2
int styleable StateListDrawable_android_constantSize 3
int styleable StateListDrawable_android_enterFadeDuration 4
int styleable StateListDrawable_android_exitFadeDuration 5
int[] styleable StateListDrawableItem { 0x01010199 }
int styleable StateListDrawableItem_android_drawable 0
int[] styleable StateSet { 0x7f03016c }
int styleable StateSet_defaultState 0
int[] styleable SwitchCompat { 0x01010124, 0x01010125, 0x01010142, 0x7f0303b4, 0x7f0303c9, 0x7f0303f6, 0x7f0303f7, 0x7f0303f9, 0x7f030466, 0x7f030467, 0x7f030468, 0x7f030491, 0x7f03049b, 0x7f03049c }
int styleable SwitchCompat_android_textOn 0
int styleable SwitchCompat_android_textOff 1
int styleable SwitchCompat_android_thumb 2
int styleable SwitchCompat_showText 3
int styleable SwitchCompat_splitTrack 4
int styleable SwitchCompat_switchMinWidth 5
int styleable SwitchCompat_switchPadding 6
int styleable SwitchCompat_switchTextAppearance 7
int styleable SwitchCompat_thumbTextPadding 8
int styleable SwitchCompat_thumbTint 9
int styleable SwitchCompat_thumbTintMode 10
int styleable SwitchCompat_track 11
int styleable SwitchCompat_trackTint 12
int styleable SwitchCompat_trackTintMode 13
int[] styleable SwitchMaterial { 0x7f0304aa }
int styleable SwitchMaterial_useMaterialThemeColors 0
int[] styleable TabItem { 0x01010002, 0x010100f2, 0x0101014f }
int styleable TabItem_android_icon 0
int styleable TabItem_android_layout 1
int styleable TabItem_android_text 2
int[] styleable TabLayout { 0x7f0303fa, 0x7f0303fb, 0x7f0303fc, 0x7f0303fd, 0x7f0303fe, 0x7f0303ff, 0x7f030400, 0x7f030401, 0x7f030402, 0x7f030403, 0x7f030404, 0x7f030405, 0x7f030406, 0x7f030407, 0x7f030408, 0x7f030409, 0x7f03040a, 0x7f03040b, 0x7f03040c, 0x7f03040d, 0x7f03040e, 0x7f03040f, 0x7f030411, 0x7f030412, 0x7f030414, 0x7f030415, 0x7f030416 }
int styleable TabLayout_tabBackground 0
int styleable TabLayout_tabContentStart 1
int styleable TabLayout_tabGravity 2
int styleable TabLayout_tabIconTint 3
int styleable TabLayout_tabIconTintMode 4
int styleable TabLayout_tabIndicator 5
int styleable TabLayout_tabIndicatorAnimationDuration 6
int styleable TabLayout_tabIndicatorAnimationMode 7
int styleable TabLayout_tabIndicatorColor 8
int styleable TabLayout_tabIndicatorFullWidth 9
int styleable TabLayout_tabIndicatorGravity 10
int styleable TabLayout_tabIndicatorHeight 11
int styleable TabLayout_tabInlineLabel 12
int styleable TabLayout_tabMaxWidth 13
int styleable TabLayout_tabMinWidth 14
int styleable TabLayout_tabMode 15
int styleable TabLayout_tabPadding 16
int styleable TabLayout_tabPaddingBottom 17
int styleable TabLayout_tabPaddingEnd 18
int styleable TabLayout_tabPaddingStart 19
int styleable TabLayout_tabPaddingTop 20
int styleable TabLayout_tabRippleColor 21
int styleable TabLayout_tabSelectedTextAppearance 22
int styleable TabLayout_tabSelectedTextColor 23
int styleable TabLayout_tabTextAppearance 24
int styleable TabLayout_tabTextColor 25
int styleable TabLayout_tabUnboundedRipple 26
int[] styleable TextAppearance { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x0101009a, 0x0101009b, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x01010585, 0x7f0301f3, 0x7f0301fc, 0x7f03041b, 0x7f030452 }
int styleable TextAppearance_android_textSize 0
int styleable TextAppearance_android_typeface 1
int styleable TextAppearance_android_textStyle 2
int styleable TextAppearance_android_textColor 3
int styleable TextAppearance_android_textColorHint 4
int styleable TextAppearance_android_textColorLink 5
int styleable TextAppearance_android_shadowColor 6
int styleable TextAppearance_android_shadowDx 7
int styleable TextAppearance_android_shadowDy 8
int styleable TextAppearance_android_shadowRadius 9
int styleable TextAppearance_android_fontFamily 10
int styleable TextAppearance_android_textFontWeight 11
int styleable TextAppearance_fontFamily 12
int styleable TextAppearance_fontVariationSettings 13
int styleable TextAppearance_textAllCaps 14
int styleable TextAppearance_textLocale 15
int[] styleable TextEffects { 0x01010095, 0x01010096, 0x01010097, 0x0101014f, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x7f03006f, 0x7f030070, 0x7f030449, 0x7f030453, 0x7f030454 }
int styleable TextEffects_android_textSize 0
int styleable TextEffects_android_typeface 1
int styleable TextEffects_android_textStyle 2
int styleable TextEffects_android_text 3
int styleable TextEffects_android_shadowColor 4
int styleable TextEffects_android_shadowDx 5
int styleable TextEffects_android_shadowDy 6
int styleable TextEffects_android_shadowRadius 7
int styleable TextEffects_android_fontFamily 8
int styleable TextEffects_borderRound 9
int styleable TextEffects_borderRoundPercent 10
int styleable TextEffects_textFillColor 11
int styleable TextEffects_textOutlineColor 12
int styleable TextEffects_textOutlineThickness 13
int[] styleable TextInputEditText { 0x7f03044d }
int styleable TextInputEditText_textInputLayoutFocusedRectEnabled 0
int[] styleable TextInputLayout { 0x0101000e, 0x0101009a, 0x0101011f, 0x0101013f, 0x01010150, 0x01010157, 0x0101015a, 0x7f030079, 0x7f03007a, 0x7f03007b, 0x7f03007c, 0x7f03007d, 0x7f03007e, 0x7f03007f, 0x7f030080, 0x7f030081, 0x7f030082, 0x7f030083, 0x7f030151, 0x7f030152, 0x7f030153, 0x7f030154, 0x7f030155, 0x7f030156, 0x7f030199, 0x7f03019a, 0x7f03019b, 0x7f03019c, 0x7f03019d, 0x7f03019e, 0x7f03019f, 0x7f0301a0, 0x7f0301a5, 0x7f0301a6, 0x7f0301a7, 0x7f0301a8, 0x7f0301a9, 0x7f0301aa, 0x7f0301ac, 0x7f0301ad, 0x7f0301b0, 0x7f03020a, 0x7f03020b, 0x7f03020c, 0x7f03020d, 0x7f030213, 0x7f030214, 0x7f030215, 0x7f030216, 0x7f03035b, 0x7f03035c, 0x7f03035d, 0x7f03035e, 0x7f03035f, 0x7f030368, 0x7f030369, 0x7f03036a, 0x7f030371, 0x7f030372, 0x7f030373, 0x7f0303a1, 0x7f0303a9, 0x7f0303d2, 0x7f0303d3, 0x7f0303d4, 0x7f0303d5, 0x7f0303d6, 0x7f0303d7, 0x7f0303d8, 0x7f0303f2, 0x7f0303f3, 0x7f0303f4 }
int styleable TextInputLayout_android_enabled 0
int styleable TextInputLayout_android_textColorHint 1
int styleable TextInputLayout_android_maxWidth 2
int styleable TextInputLayout_android_minWidth 3
int styleable TextInputLayout_android_hint 4
int styleable TextInputLayout_android_maxEms 5
int styleable TextInputLayout_android_minEms 6
int styleable TextInputLayout_boxBackgroundColor 7
int styleable TextInputLayout_boxBackgroundMode 8
int styleable TextInputLayout_boxCollapsedPaddingTop 9
int styleable TextInputLayout_boxCornerRadiusBottomEnd 10
int styleable TextInputLayout_boxCornerRadiusBottomStart 11
int styleable TextInputLayout_boxCornerRadiusTopEnd 12
int styleable TextInputLayout_boxCornerRadiusTopStart 13
int styleable TextInputLayout_boxStrokeColor 14
int styleable TextInputLayout_boxStrokeErrorColor 15
int styleable TextInputLayout_boxStrokeWidth 16
int styleable TextInputLayout_boxStrokeWidthFocused 17
int styleable TextInputLayout_counterEnabled 18
int styleable TextInputLayout_counterMaxLength 19
int styleable TextInputLayout_counterOverflowTextAppearance 20
int styleable TextInputLayout_counterOverflowTextColor 21
int styleable TextInputLayout_counterTextAppearance 22
int styleable TextInputLayout_counterTextColor 23
int styleable TextInputLayout_endIconCheckable 24
int styleable TextInputLayout_endIconContentDescription 25
int styleable TextInputLayout_endIconDrawable 26
int styleable TextInputLayout_endIconMinSize 27
int styleable TextInputLayout_endIconMode 28
int styleable TextInputLayout_endIconScaleType 29
int styleable TextInputLayout_endIconTint 30
int styleable TextInputLayout_endIconTintMode 31
int styleable TextInputLayout_errorAccessibilityLiveRegion 32
int styleable TextInputLayout_errorContentDescription 33
int styleable TextInputLayout_errorEnabled 34
int styleable TextInputLayout_errorIconDrawable 35
int styleable TextInputLayout_errorIconTint 36
int styleable TextInputLayout_errorIconTintMode 37
int styleable TextInputLayout_errorTextAppearance 38
int styleable TextInputLayout_errorTextColor 39
int styleable TextInputLayout_expandedHintEnabled 40
int styleable TextInputLayout_helperText 41
int styleable TextInputLayout_helperTextEnabled 42
int styleable TextInputLayout_helperTextTextAppearance 43
int styleable TextInputLayout_helperTextTextColor 44
int styleable TextInputLayout_hintAnimationEnabled 45
int styleable TextInputLayout_hintEnabled 46
int styleable TextInputLayout_hintTextAppearance 47
int styleable TextInputLayout_hintTextColor 48
int styleable TextInputLayout_passwordToggleContentDescription 49
int styleable TextInputLayout_passwordToggleDrawable 50
int styleable TextInputLayout_passwordToggleEnabled 51
int styleable TextInputLayout_passwordToggleTint 52
int styleable TextInputLayout_passwordToggleTintMode 53
int styleable TextInputLayout_placeholderText 54
int styleable TextInputLayout_placeholderTextAppearance 55
int styleable TextInputLayout_placeholderTextColor 56
int styleable TextInputLayout_prefixText 57
int styleable TextInputLayout_prefixTextAppearance 58
int styleable TextInputLayout_prefixTextColor 59
int styleable TextInputLayout_shapeAppearance 60
int styleable TextInputLayout_shapeAppearanceOverlay 61
int styleable TextInputLayout_startIconCheckable 62
int styleable TextInputLayout_startIconContentDescription 63
int styleable TextInputLayout_startIconDrawable 64
int styleable TextInputLayout_startIconMinSize 65
int styleable TextInputLayout_startIconScaleType 66
int styleable TextInputLayout_startIconTint 67
int styleable TextInputLayout_startIconTintMode 68
int styleable TextInputLayout_suffixText 69
int styleable TextInputLayout_suffixTextAppearance 70
int styleable TextInputLayout_suffixTextColor 71
int[] styleable ThemeEnforcement { 0x01010034, 0x7f0301a1, 0x7f0301a2 }
int styleable ThemeEnforcement_android_textAppearance 0
int styleable ThemeEnforcement_enforceMaterialTheme 1
int styleable ThemeEnforcement_enforceTextAppearance 2
int[] styleable Toolbar { 0x010100af, 0x01010140, 0x7f03008b, 0x7f0300e1, 0x7f0300e2, 0x7f030134, 0x7f030135, 0x7f030136, 0x7f030137, 0x7f030138, 0x7f030139, 0x7f0302be, 0x7f0302c0, 0x7f0302f5, 0x7f0302fd, 0x7f03033b, 0x7f03033c, 0x7f03036f, 0x7f0303ed, 0x7f0303ef, 0x7f0303f0, 0x7f030475, 0x7f030479, 0x7f03047a, 0x7f03047b, 0x7f03047c, 0x7f03047d, 0x7f03047e, 0x7f030480, 0x7f030481 }
int styleable Toolbar_android_gravity 0
int styleable Toolbar_android_minHeight 1
int styleable Toolbar_buttonGravity 2
int styleable Toolbar_collapseContentDescription 3
int styleable Toolbar_collapseIcon 4
int styleable Toolbar_contentInsetEnd 5
int styleable Toolbar_contentInsetEndWithActions 6
int styleable Toolbar_contentInsetLeft 7
int styleable Toolbar_contentInsetRight 8
int styleable Toolbar_contentInsetStart 9
int styleable Toolbar_contentInsetStartWithNavigation 10
int styleable Toolbar_logo 11
int styleable Toolbar_logoDescription 12
int styleable Toolbar_maxButtonHeight 13
int styleable Toolbar_menu 14
int styleable Toolbar_navigationContentDescription 15
int styleable Toolbar_navigationIcon 16
int styleable Toolbar_popupTheme 17
int styleable Toolbar_subtitle 18
int styleable Toolbar_subtitleTextAppearance 19
int styleable Toolbar_subtitleTextColor 20
int styleable Toolbar_title 21
int styleable Toolbar_titleMargin 22
int styleable Toolbar_titleMarginBottom 23
int styleable Toolbar_titleMarginEnd 24
int styleable Toolbar_titleMarginStart 25
int styleable Toolbar_titleMarginTop 26
int styleable Toolbar_titleMargins 27
int styleable Toolbar_titleTextAppearance 28
int styleable Toolbar_titleTextColor 29
int[] styleable Tooltip { 0x01010034, 0x01010098, 0x010100d5, 0x010100f6, 0x0101013f, 0x01010140, 0x0101014f, 0x7f03004d }
int styleable Tooltip_android_textAppearance 0
int styleable Tooltip_android_textColor 1
int styleable Tooltip_android_padding 2
int styleable Tooltip_android_layout_margin 3
int styleable Tooltip_android_minWidth 4
int styleable Tooltip_android_minHeight 5
int styleable Tooltip_android_text 6
int styleable Tooltip_backgroundTint 7
int[] styleable Transform { 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f03049d }
int styleable Transform_android_transformPivotX 0
int styleable Transform_android_transformPivotY 1
int styleable Transform_android_translationX 2
int styleable Transform_android_translationY 3
int styleable Transform_android_scaleX 4
int styleable Transform_android_scaleY 5
int styleable Transform_android_rotation 6
int styleable Transform_android_rotationX 7
int styleable Transform_android_rotationY 8
int styleable Transform_android_translationZ 9
int styleable Transform_android_elevation 10
int styleable Transform_transformPivotTarget 11
int[] styleable Transition { 0x010100d0, 0x7f030043, 0x7f03012d, 0x7f03012e, 0x7f03018e, 0x7f030261, 0x7f030331, 0x7f030360, 0x7f0303d1, 0x7f03049e, 0x7f0304a0 }
int styleable Transition_android_id 0
int styleable Transition_autoTransition 1
int styleable Transition_constraintSetEnd 2
int styleable Transition_constraintSetStart 3
int styleable Transition_duration 4
int styleable Transition_layoutDuringTransition 5
int styleable Transition_motionInterpolator 6
int styleable Transition_pathMotionArc 7
int styleable Transition_staggered 8
int styleable Transition_transitionDisable 9
int styleable Transition_transitionFlags 10
int[] styleable Variant { 0x7f030131, 0x7f030388, 0x7f030389, 0x7f03038a, 0x7f03038b }
int styleable Variant_constraints 0
int styleable Variant_region_heightLessThan 1
int styleable Variant_region_heightMoreThan 2
int styleable Variant_region_widthLessThan 3
int styleable Variant_region_widthMoreThan 4
int[] styleable View { 0x01010000, 0x010100da, 0x7f030352, 0x7f030355, 0x7f03045c }
int styleable View_android_theme 0
int styleable View_android_focusable 1
int styleable View_paddingEnd 2
int styleable View_paddingStart 3
int styleable View_theme 4
int[] styleable ViewBackgroundHelper { 0x010100d4, 0x7f03004d, 0x7f03004e }
int styleable ViewBackgroundHelper_android_background 0
int styleable ViewBackgroundHelper_backgroundTint 1
int styleable ViewBackgroundHelper_backgroundTintMode 2
int[] styleable ViewPager2 { 0x010100c4 }
int styleable ViewPager2_android_orientation 0
int[] styleable ViewStubCompat { 0x010100d0, 0x010100f2, 0x010100f3 }
int styleable ViewStubCompat_android_id 0
int styleable ViewStubCompat_android_layout 1
int styleable ViewStubCompat_android_inflatedId 2
int[] styleable ViewTransition { 0x010100d0, 0x7f030000, 0x7f030001, 0x7f0300d3, 0x7f03018e, 0x7f030225, 0x7f030226, 0x7f030331, 0x7f030336, 0x7f03034c, 0x7f030360, 0x7f0303a0, 0x7f03049e, 0x7f0304a7, 0x7f0304af }
int styleable ViewTransition_android_id 0
int styleable ViewTransition_SharedValue 1
int styleable ViewTransition_SharedValueId 2
int styleable ViewTransition_clearsTag 3
int styleable ViewTransition_duration 4
int styleable ViewTransition_ifTagNotSet 5
int styleable ViewTransition_ifTagSet 6
int styleable ViewTransition_motionInterpolator 7
int styleable ViewTransition_motionTarget 8
int styleable ViewTransition_onStateTransition 9
int styleable ViewTransition_pathMotionArc 10
int styleable ViewTransition_setsTag 11
int styleable ViewTransition_transitionDisable 12
int styleable ViewTransition_upDuration 13
int styleable ViewTransition_viewTransitionMode 14
int[] styleable include { 0x7f03012c }
int styleable include_constraintSet 0
int xml backup_rules 0x7f130000
int xml data_extraction_rules 0x7f130001
int xml file_paths 0x7f130002
int xml network_security_config 0x7f130003
int xml oset_file_paths 0x7f130004
