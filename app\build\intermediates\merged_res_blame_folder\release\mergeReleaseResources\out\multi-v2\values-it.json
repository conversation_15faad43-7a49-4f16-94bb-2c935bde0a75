{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-mergeReleaseResources-30:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bde3453fac62cb9a4dc37198ed238829\\transformed\\appcompat-1.6.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,423,526,635,719,824,943,1021,1096,1188,1282,1375,1469,1570,1664,1761,1856,1948,2040,2121,2227,2334,2432,2536,2642,2749,2912,20284", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "418,521,630,714,819,938,1016,1091,1183,1277,1370,1464,1565,1659,1756,1851,1943,2035,2116,2222,2329,2427,2531,2637,2744,2907,3007,20361"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b7d68e85c64c5740c775d2c6236c08f5\\transformed\\core-1.9.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "203", "startColumns": "4", "startOffsets": "20540", "endColumns": "100", "endOffsets": "20636"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5d5fa9c9e36bc912c1cb51114343e1e1\\transformed\\material-1.9.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,352,433,510,609,704,803,943,1026,1092,1187,1272,1334,1422,1484,1553,1616,1689,1752,1806,1927,1984,2046,2100,2177,2314,2399,2481,2616,2697,2778,2869,2924,2975,3041,3114,3194,3285,3360,3437,3506,3583,3671,3760,3853,3946,4020,4100,4194,4245,4311,4395,4483,4545,4609,4672,4787,4897,5003,5112,5171,5226", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,83,80,76,98,94,98,139,82,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,81,134,80,80,90,54,50,65,72,79,90,74,76,68,76,87,88,92,92,73,79,93,50,65,83,87,61,63,62,114,109,105,108,58,54,79", "endOffsets": "263,347,428,505,604,699,798,938,1021,1087,1182,1267,1329,1417,1479,1548,1611,1684,1747,1801,1922,1979,2041,2095,2172,2309,2394,2476,2611,2692,2773,2864,2919,2970,3036,3109,3189,3280,3355,3432,3501,3578,3666,3755,3848,3941,4015,4095,4189,4240,4306,4390,4478,4540,4604,4667,4782,4892,4998,5107,5166,5221,5301"}, "to": {"startLines": "2,42,43,44,45,46,52,53,56,73,77,89,90,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3774,3858,3939,4016,4115,4638,4737,5032,7978,8227,9570,9655,10924,11012,11074,11143,11206,11279,11342,11396,11517,11574,11636,11690,11767,11904,11989,12071,12206,12287,12368,12459,12514,12565,12631,12704,12784,12875,12950,13027,13096,13173,13261,13350,13443,13536,13610,13690,13784,13835,13901,13985,14073,14135,14199,14262,14377,14487,14593,14702,14761,16466", "endLines": "5,42,43,44,45,46,52,53,56,73,77,89,90,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,176", "endColumns": "12,83,80,76,98,94,98,139,82,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,81,134,80,80,90,54,50,65,72,79,90,74,76,68,76,87,88,92,92,73,79,93,50,65,83,87,61,63,62,114,109,105,108,58,54,79", "endOffsets": "313,3853,3934,4011,4110,4205,4732,4872,5110,8039,8317,9650,9712,11007,11069,11138,11201,11274,11337,11391,11512,11569,11631,11685,11762,11899,11984,12066,12201,12282,12363,12454,12509,12560,12626,12699,12779,12870,12945,13022,13091,13168,13256,13345,13438,13531,13605,13685,13779,13830,13896,13980,14068,14130,14194,14257,14372,14482,14588,14697,14756,14811,16541"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-it\\strings.xml", "from": {"startLines": "97,96,93,92,110,99,98,1,2,60,80,59,81,113,56,154,107,47,106,140,141,139,137,136,144,143,134,133,148,69,71,79,46,45,6,54,55,68,67,44,11,82,63,61,64,62,9,32,17,28,29,25,21,31,19,20,26,23,30,15,16,27,22,18,24,112,83,8,7,86,157,158,75,162,78,161,159,160,76,77,48,151,39,37,40,38,36,35,41,153,152,88,111,100,95,94,101,89,105,104,103,102,70,72,87,53,5,51,12,10,129,130,128,117,118,116,147,121,122,120,125,126,124,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8275,8195,7895,7827,9405,8477,8393,16,79,5200,6938,5121,7048,9593,4997,16090,9342,4525,9285,14703,15090,14580,14030,13908,15316,15210,13293,13172,15674,5919,6030,6867,4469,4395,221,4896,4954,5692,5621,4274,477,7154,5438,5274,5512,5354,382,1858,1041,1646,1699,1486,1254,1804,1156,1206,1536,1369,1751,910,980,1593,1319,1094,1427,9531,7248,326,274,7376,16208,16329,6318,16765,6798,16649,16452,16554,6384,6590,4578,15904,4008,2115,4065,3903,2045,1947,4129,16016,15945,7579,9471,8610,8079,7988,8751,7636,9121,9049,8969,8907,5968,6202,7509,4844,169,4709,792,426,12568,13028,12418,9854,10638,9684,15442,10947,11475,10789,11777,12255,11602,4783", "endColumns": "117,79,92,67,65,132,83,62,56,73,109,78,105,58,92,61,38,52,56,386,118,122,548,121,86,105,613,120,193,48,171,70,55,73,52,57,42,226,70,120,314,93,73,79,80,83,43,51,52,52,51,49,64,53,49,47,56,57,52,69,60,52,49,61,58,61,100,55,51,132,120,122,65,165,68,115,101,94,205,207,95,40,56,1787,63,104,69,97,109,73,70,56,59,140,115,90,155,151,163,71,79,61,61,87,69,51,51,73,86,50,459,110,149,783,149,169,231,527,125,157,477,161,174,60", "endOffsets": "8388,8270,7983,7890,9466,8605,8472,74,131,5269,7043,5195,7149,9647,5085,16147,9376,4573,9337,15085,15204,14698,14574,14025,15398,15311,13902,13288,15863,5963,6197,6933,4520,4464,269,4949,4992,5914,5687,4390,787,7243,5507,5349,5588,5433,421,1905,1089,1694,1746,1531,1314,1853,1201,1249,1588,1422,1799,975,1036,1641,1364,1151,1481,9588,7344,377,321,7504,16324,16447,6379,16926,6862,16760,16549,16644,6585,6793,4669,15940,4060,3898,4124,4003,2110,2040,4234,16085,16011,7631,9526,8746,8190,8074,8902,7783,9280,9116,9044,8964,6025,6285,7574,4891,216,4778,874,472,13023,13134,12563,10633,10783,9849,15669,11470,11596,10942,12250,12412,11772,4839"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,47,48,49,50,51,54,55,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,74,75,76,78,79,80,81,82,83,84,85,86,87,88,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,200,201,202,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3012,3130,3210,3303,3371,3437,3570,3654,3717,4210,4284,4394,4473,4579,4877,4970,5115,5154,5207,5264,5651,5770,5893,6442,6564,6651,6757,7371,7492,7686,7735,7907,8044,8100,8174,8322,8380,8423,8650,8721,8842,9157,9251,9325,9405,9486,9717,9761,9813,9866,9919,9971,10021,10086,10140,10190,10238,10295,10353,10406,10476,10537,10590,10640,10702,10761,10823,14816,14872,14924,15057,15178,15301,15367,15533,15602,15718,15820,15915,16121,16329,16425,16546,16603,18391,18455,18560,18630,18728,18838,18912,18983,19040,19100,19241,19357,19448,19604,19756,19920,19992,20072,20134,20196,20366,20436,20488,20641,20715,20802,20853,21313,21424,21574,22358,22508,22678,22910,23438,23564,23722,24200,24362,24537", "endColumns": "117,79,92,67,65,132,83,62,56,73,109,78,105,58,92,61,38,52,56,386,118,122,548,121,86,105,613,120,193,48,171,70,55,73,52,57,42,226,70,120,314,93,73,79,80,83,43,51,52,52,51,49,64,53,49,47,56,57,52,69,60,52,49,61,58,61,100,55,51,132,120,122,65,165,68,115,101,94,205,207,95,40,56,1787,63,104,69,97,109,73,70,56,59,140,115,90,155,151,163,71,79,61,61,87,69,51,51,73,86,50,459,110,149,783,149,169,231,527,125,157,477,161,174,60", "endOffsets": "3125,3205,3298,3366,3432,3565,3649,3712,3769,4279,4389,4468,4574,4633,4965,5027,5149,5202,5259,5646,5765,5888,6437,6559,6646,6752,7366,7487,7681,7730,7902,7973,8095,8169,8222,8375,8418,8645,8716,8837,9152,9246,9320,9400,9481,9565,9756,9808,9861,9914,9966,10016,10081,10135,10185,10233,10290,10348,10401,10471,10532,10585,10635,10697,10756,10818,10919,14867,14919,15052,15173,15296,15362,15528,15597,15713,15815,15910,16116,16324,16420,16461,16598,18386,18450,18555,18625,18723,18833,18907,18978,19035,19095,19236,19352,19443,19599,19751,19915,19987,20067,20129,20191,20279,20431,20483,20535,20710,20797,20848,21308,21419,21569,22353,22503,22673,22905,23433,23559,23717,24195,24357,24532,24593"}}]}]}