<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F8F8F8">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/ll_aa"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:layout_marginTop="15dp"
                android:gravity="center_vertical"
                android:paddingLeft="20dp">

                <View
                    android:layout_width="4dp"
                    android:layout_height="20dp"
                    android:background="#00B767" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:text="广告对接"
                    android:textColor="#000000"
                    android:textSize="18sp"
                    android:textStyle="bold" />
            </LinearLayout>


            <com.test.opensetdemo.MyGridView
                android:id="@+id/gv_ad"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="20dp"
                android:numColumns="2"
                android:verticalSpacing="10px" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:gravity="center_vertical"
                android:paddingLeft="20dp">

                <View
                    android:layout_width="4dp"
                    android:layout_height="20dp"
                    android:background="#00B767" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:text="内容对接"
                    android:textColor="#000000"
                    android:textSize="18sp"
                    android:textStyle="bold" />
            </LinearLayout>


            <com.test.opensetdemo.MyGridView
                android:id="@+id/gv_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="20dp"
                android:numColumns="2"
                android:verticalSpacing="10px" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:gravity="center_vertical"
                android:paddingLeft="20dp">

                <View
                    android:layout_width="4dp"
                    android:layout_height="20dp"
                    android:background="#00B767" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:text="风险识别"
                    android:textColor="#000000"
                    android:textSize="18sp"
                    android:textStyle="bold" />
            </LinearLayout>


            <com.test.opensetdemo.MyGridView
                android:id="@+id/gv_risk"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="20dp"
                android:numColumns="2"
                android:verticalSpacing="10px" />
        </LinearLayout>

    </ScrollView>

    <FrameLayout
        android:id="@+id/fl_suspend"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_alignParentRight="true"
        android:layout_marginTop="300dp"
        android:layout_marginRight="20dp" />
</RelativeLayout>