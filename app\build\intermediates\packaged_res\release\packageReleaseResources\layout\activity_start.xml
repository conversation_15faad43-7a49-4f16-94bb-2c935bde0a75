<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 背景图片 -->
    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/background1"
        android:scaleType="centerCrop"
        android:contentDescription="开始界面背景" />

    <!-- 虚化遮罩层 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/start_overlay" />

    <!-- 内容容器 - 使用ScrollView确保内容可滚动 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center_horizontal"
            android:paddingStart="32dp"
            android:paddingEnd="32dp"
            android:paddingTop="40dp"
            android:paddingBottom="40dp">

            <!-- 顶部间距 -->
            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:minHeight="20dp" />

            <!-- 游戏标题图片 -->
            <ImageView
                android:id="@+id/titleImageView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/title_en"
                android:layout_marginBottom="32dp"
                android:adjustViewBounds="true"
                android:maxWidth="300dp"
                android:contentDescription="@string/title_content_description" />

            <!-- 游戏描述 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/game_description"
                android:textSize="15sp"
                android:textColor="#ffffff"
                android:lineSpacingExtra="3dp"
                android:gravity="center"
                android:layout_marginBottom="40dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:shadowColor="#000000"
                android:shadowDx="2"
                android:shadowDy="2"
                android:shadowRadius="4" />

            <!-- 按钮容器 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center">

                <!-- 开始游戏按钮 -->
                <Button
                    android:id="@+id/startGameButton"
                    android:layout_width="200dp"
                    android:layout_height="60dp"
                    android:text="@string/start_game"
                    android:textSize="18sp"
                    android:textColor="#ffffff"
                    android:textStyle="bold"
                    android:background="@drawable/button_black_frame"
                    android:layout_marginBottom="16dp" />

                <!-- 音乐控制按钮 -->
                <Button
                    android:id="@+id/musicToggleButton"
                    android:layout_width="200dp"
                    android:layout_height="60dp"
                    android:text="@string/music_on"
                    android:textSize="18sp"
                    android:textColor="#ffffff"
                    android:background="@drawable/button_black_frame"
                    android:layout_marginBottom="16dp" />

                <!-- 语言选择按钮 -->
                <Button
                    android:id="@+id/languageButton"
                    android:layout_width="200dp"
                    android:layout_height="60dp"
                    android:text="@string/language"
                    android:textSize="18sp"
                    android:textColor="#ffffff"
                    android:background="@drawable/button_black_frame"
                    android:layout_marginBottom="16dp" />

                <!-- 退出游戏按钮 -->
                <Button
                    android:id="@+id/exitGameButton"
                    android:layout_width="200dp"
                    android:layout_height="60dp"
                    android:text="@string/exit_game"
                    android:textSize="18sp"
                    android:textColor="#ffffff"
                    android:background="@drawable/button_black_frame"
                    android:layout_marginBottom="24dp" />

            </LinearLayout>

            <!-- 底部间距 -->
            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:minHeight="20dp" />

            <!-- 版本信息 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/version"
                android:textSize="12sp"
                android:textColor="#888888"
                android:layout_marginTop="16dp" />

        </LinearLayout>

    </ScrollView>

</FrameLayout>
