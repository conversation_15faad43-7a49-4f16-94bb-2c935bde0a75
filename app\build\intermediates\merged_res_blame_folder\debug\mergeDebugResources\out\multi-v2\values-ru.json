{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-mergeDebugResources-30:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5d5fa9c9e36bc912c1cb51114343e1e1\\transformed\\material-1.9.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,381,459,537,621,719,810,907,1044,1136,1202,1301,1378,1441,1559,1620,1685,1742,1812,1873,1927,2043,2100,2162,2216,2290,2418,2506,2592,2729,2813,2898,2989,3043,3094,3160,3232,3310,3406,3486,3562,3639,3716,3805,3878,3968,4063,4137,4218,4311,4366,4432,4518,4603,4665,4729,4792,4890,4990,5085,5187,5245,5300", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "endColumns": "12,77,77,83,97,90,96,136,91,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,85,136,83,84,90,53,50,65,71,77,95,79,75,76,76,88,72,89,94,73,80,92,54,65,85,84,61,63,62,97,99,94,101,57,54,79", "endOffsets": "376,454,532,616,714,805,902,1039,1131,1197,1296,1373,1436,1554,1615,1680,1737,1807,1868,1922,2038,2095,2157,2211,2285,2413,2501,2587,2724,2808,2893,2984,3038,3089,3155,3227,3305,3401,3481,3557,3634,3711,3800,3873,3963,4058,4132,4213,4306,4361,4427,4513,4598,4660,4724,4787,4885,4985,5080,5182,5240,5295,5375"}, "to": {"startLines": "2,44,45,46,47,48,54,55,58,75,79,91,92,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3874,3952,4030,4114,4212,4721,4818,5098,7718,7966,9311,9388,10637,10755,10816,10881,10938,11008,11069,11123,11239,11296,11358,11412,11486,11614,11702,11788,11925,12009,12094,12185,12239,12290,12356,12428,12506,12602,12682,12758,12835,12912,13001,13074,13164,13259,13333,13414,13507,13562,13628,13714,13799,13861,13925,13988,14086,14186,14281,14383,14441,16050", "endLines": "7,44,45,46,47,48,54,55,58,75,79,91,92,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,178", "endColumns": "12,77,77,83,97,90,96,136,91,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,85,136,83,84,90,53,50,65,71,77,95,79,75,76,76,88,72,89,94,73,80,92,54,65,85,84,61,63,62,97,99,94,101,57,54,79", "endOffsets": "426,3947,4025,4109,4207,4298,4813,4950,5185,7779,8060,9383,9446,10750,10811,10876,10933,11003,11064,11118,11234,11291,11353,11407,11481,11609,11697,11783,11920,12004,12089,12180,12234,12285,12351,12423,12501,12597,12677,12753,12830,12907,12996,13069,13159,13254,13328,13409,13502,13557,13623,13709,13794,13856,13920,13983,14081,14181,14276,14378,14436,14491,16125"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-ru\\strings.xml", "from": {"startLines": "97,96,93,92,110,99,98,1,2,60,80,59,81,113,56,154,107,47,106,140,141,139,137,136,144,143,134,133,148,69,71,79,46,45,6,54,55,68,67,44,11,82,63,61,64,62,9,32,17,28,29,25,21,31,19,20,26,23,30,15,16,27,22,18,24,112,83,8,7,86,157,158,75,162,78,161,159,160,76,77,48,151,39,37,40,38,36,35,41,153,152,88,111,100,95,94,101,89,105,104,103,102,70,72,87,53,5,51,12,10,129,130,128,117,118,116,147,121,122,120,125,126,124,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7958,7885,7599,7537,9041,8152,8081,16,71,5017,6693,4947,6802,9225,4832,14846,8978,4371,8920,13531,13855,13424,12949,12835,14045,13950,12334,12225,14407,5749,5861,6619,4315,4241,209,4742,4790,5541,5472,4119,462,6905,5274,5097,5359,5185,371,1813,996,1601,1654,1441,1209,1759,1111,1161,1491,1324,1706,869,935,1548,1274,1049,1382,9168,7011,315,261,7128,14951,15046,6118,15472,6560,15367,15156,15275,6179,6372,4426,14652,3858,2072,3916,3759,2001,1906,3980,14767,14693,7313,9102,8283,7773,7692,8425,7370,8779,8709,8634,8575,5802,6013,7247,4688,158,4560,761,413,11721,12088,11601,9458,10091,9309,14170,10363,10809,10228,11073,11456,10930,4631", "endColumns": "122,72,92,61,60,130,70,54,56,79,108,69,102,55,84,57,38,54,57,323,93,106,473,113,84,94,499,108,194,52,151,73,55,73,51,47,41,207,68,121,298,105,84,87,89,88,41,50,52,52,51,49,64,53,49,47,56,57,52,65,60,52,49,61,58,56,91,55,53,118,94,109,60,159,58,104,118,91,192,187,101,40,57,1686,63,98,70,94,109,78,73,56,65,141,111,80,149,131,140,69,74,58,58,78,65,53,50,70,81,48,366,105,119,632,135,148,236,445,119,134,382,143,142,56", "endOffsets": "8076,7953,7687,7594,9097,8278,8147,66,123,5092,6797,5012,6900,9276,4912,14899,9012,4421,8973,13850,13944,13526,13418,12944,14125,14040,12829,12329,14597,5797,6008,6688,4366,4310,256,4785,4827,5744,5536,4236,756,7006,5354,5180,5444,5269,408,1859,1044,1649,1701,1486,1269,1808,1156,1204,1543,1377,1754,930,991,1596,1319,1106,1436,9220,7098,366,310,7242,15041,15151,6174,15627,6614,15467,15270,15362,6367,6555,4523,14688,3911,3754,3975,3853,2067,1996,4085,14841,14762,7365,9163,8420,7880,7768,8570,7497,8915,8774,8704,8629,5856,6087,7308,4737,204,4626,838,457,12083,12189,11716,10086,10222,9453,14402,10804,10924,10358,11451,11595,11068,4683"}, "to": {"startLines": "35,36,37,38,39,40,41,42,43,49,50,51,52,53,56,57,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,76,77,78,80,81,82,83,84,85,86,87,88,89,90,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,202,203,204,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3148,3271,3344,3437,3499,3560,3691,3762,3817,4303,4383,4492,4562,4665,4955,5040,5190,5229,5284,5342,5666,5760,5867,6341,6455,6540,6635,7135,7244,7439,7492,7644,7784,7840,7914,8065,8113,8155,8363,8432,8554,8853,8959,9044,9132,9222,9451,9493,9544,9597,9650,9702,9752,9817,9871,9921,9969,10026,10084,10137,10203,10264,10317,10367,10429,10488,10545,14496,14552,14606,14725,14820,14930,14991,15151,15210,15315,15434,15526,15719,15907,16009,16130,16188,17875,17939,18038,18109,18204,18314,18393,18467,18524,18590,18732,18844,18925,19075,19207,19348,19418,19493,19552,19611,19772,19838,19892,20044,20115,20197,20246,20613,20719,20839,21472,21608,21757,21994,22440,22560,22695,23078,23222,23365", "endColumns": "122,72,92,61,60,130,70,54,56,79,108,69,102,55,84,57,38,54,57,323,93,106,473,113,84,94,499,108,194,52,151,73,55,73,51,47,41,207,68,121,298,105,84,87,89,88,41,50,52,52,51,49,64,53,49,47,56,57,52,65,60,52,49,61,58,56,91,55,53,118,94,109,60,159,58,104,118,91,192,187,101,40,57,1686,63,98,70,94,109,78,73,56,65,141,111,80,149,131,140,69,74,58,58,78,65,53,50,70,81,48,366,105,119,632,135,148,236,445,119,134,382,143,142,56", "endOffsets": "3266,3339,3432,3494,3555,3686,3757,3812,3869,4378,4487,4557,4660,4716,5035,5093,5224,5279,5337,5661,5755,5862,6336,6450,6535,6630,7130,7239,7434,7487,7639,7713,7835,7909,7961,8108,8150,8358,8427,8549,8848,8954,9039,9127,9217,9306,9488,9539,9592,9645,9697,9747,9812,9866,9916,9964,10021,10079,10132,10198,10259,10312,10362,10424,10483,10540,10632,14547,14601,14720,14815,14925,14986,15146,15205,15310,15429,15521,15714,15902,16004,16045,16183,17870,17934,18033,18104,18199,18309,18388,18462,18519,18585,18727,18839,18920,19070,19202,19343,19413,19488,19547,19606,19685,19833,19887,19938,20110,20192,20241,20608,20714,20834,21467,21603,21752,21989,22435,22555,22690,23073,23217,23360,23417"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bde3453fac62cb9a4dc37198ed238829\\transformed\\appcompat-1.6.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,201", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "431,546,648,747,833,938,1059,1138,1214,1306,1400,1495,1588,1683,1777,1873,1968,2060,2152,2241,2347,2454,2552,2661,2768,2882,3048,19690", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "541,643,742,828,933,1054,1133,1209,1301,1395,1490,1583,1678,1772,1868,1963,2055,2147,2236,2342,2449,2547,2656,2763,2877,3043,3143,19767"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b7d68e85c64c5740c775d2c6236c08f5\\transformed\\core-1.9.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "205", "startColumns": "4", "startOffsets": "19943", "endColumns": "100", "endOffsets": "20039"}}]}]}