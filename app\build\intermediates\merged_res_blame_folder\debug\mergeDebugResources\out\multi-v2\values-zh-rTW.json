{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-mergeDebugResources-30:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bde3453fac62cb9a4dc37198ed238829\\transformed\\appcompat-1.6.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,186", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1307,1403,1498,1592,1688,1780,1872,1964,2042,2138,2233,2328,2425,2521,2619,2769,13568", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1302,1398,1493,1587,1683,1775,1867,1959,2037,2133,2228,2323,2420,2516,2614,2764,2858,13642"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5d5fa9c9e36bc912c1cb51114343e1e1\\transformed\\material-1.9.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,440,510,587,681,788,861,923,1001,1061,1121,1199,1260,1318,1374,1434,1492,1546,1631,1687,1745,1799,1864,1956,2030,2107,2227,2290,2353,2430,2480,2531,2597,2660,2728,2806,2867,2938,3005,3067,3146,3211,3294,3379,3453,3517,3593,3641,3705,3781,3859,3921,3985,4048,4128,4205,4281,4358,4412,4467", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,76,49,50,65,62,67,77,60,70,66,61,78,64,82,84,73,63,75,47,63,75,77,61,63,62,79,76,75,76,53,54,68", "endOffsets": "242,306,368,435,505,582,676,783,856,918,996,1056,1116,1194,1255,1313,1369,1429,1487,1541,1626,1682,1740,1794,1859,1951,2025,2102,2222,2285,2348,2425,2475,2526,2592,2655,2723,2801,2862,2933,3000,3062,3141,3206,3289,3374,3448,3512,3588,3636,3700,3776,3854,3916,3980,4043,4123,4200,4276,4353,4407,4462,4531"}, "to": {"startLines": "2,42,43,44,45,46,52,53,56,73,77,89,90,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3380,3444,3506,3573,3643,3999,4093,4308,5788,5995,6805,6865,7353,7431,7492,7550,7606,7666,7724,7778,7863,7919,7977,8031,8096,8188,8262,8339,8459,8522,8585,8662,8712,8763,8829,8892,8960,9038,9099,9170,9237,9299,9378,9443,9526,9611,9685,9749,9825,9873,9937,10013,10091,10153,10217,10280,10360,10437,10513,10590,10644,11660", "endLines": "5,42,43,44,45,46,52,53,56,73,77,89,90,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,163", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,76,49,50,65,62,67,77,60,70,66,61,78,64,82,84,73,63,75,47,63,75,77,61,63,62,79,76,75,76,53,54,68", "endOffsets": "292,3439,3501,3568,3638,3715,4088,4195,4376,5845,6068,6860,6920,7426,7487,7545,7601,7661,7719,7773,7858,7914,7972,8026,8091,8183,8257,8334,8454,8517,8580,8657,8707,8758,8824,8887,8955,9033,9094,9165,9232,9294,9373,9438,9521,9606,9680,9744,9820,9868,9932,10008,10086,10148,10212,10275,10355,10432,10508,10585,10639,10694,11724"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-zh-rTW\\strings.xml", "from": {"startLines": "84,83,80,79,97,86,85,1,2,47,67,46,68,100,43,141,94,34,93,127,128,126,124,123,131,130,121,120,135,56,58,66,33,32,6,41,42,55,54,31,11,69,50,48,51,49,9,19,17,15,16,18,99,70,8,7,73,144,145,62,149,65,148,146,147,63,64,35,138,26,24,27,25,23,22,28,140,139,75,98,87,82,81,88,76,92,91,90,89,57,59,74,40,5,38,12,10,116,117,115,104,105,103,134,108,109,107,112,113,111,39", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4390,4334,4144,4094,5109,4521,4465,16,58,2478,3559,2425,3622,5251,2346,8221,5051,1986,5004,7438,7592,7367,7157,7083,7727,7658,6879,6806,7934,2953,3040,3502,1940,1884,178,2260,2305,2842,2791,1816,391,3684,2652,2530,2712,2589,306,819,704,585,643,757,5203,3744,263,221,3821,8292,8363,3205,8616,3452,8549,8430,8491,3256,3354,2033,8068,1577,1002,1629,1513,945,887,1685,8164,8109,3948,5152,4598,4264,4205,4682,3995,4928,4873,4815,4768,2995,3125,3896,2216,134,2115,505,346,6534,6711,6447,5406,5659,5319,7806,5827,6013,5742,6181,6359,6091,2173", "endColumns": "74,55,60,49,42,76,55,41,56,51,62,52,61,48,59,47,38,46,46,153,64,70,208,73,59,68,202,72,111,41,84,56,45,55,42,44,40,110,50,67,113,59,59,58,59,62,39,46,52,57,60,61,47,58,42,41,74,70,66,50,75,49,66,60,57,97,97,62,40,51,510,55,63,56,57,109,56,54,46,50,83,69,58,85,79,75,54,57,46,44,60,51,43,43,57,60,44,176,75,86,252,81,86,127,185,76,84,177,86,89,42", "endOffsets": "4460,4385,4200,4139,5147,4593,4516,53,110,2525,3617,2473,3679,5295,2401,8264,5085,2028,5046,7587,7652,7433,7361,7152,7782,7722,7077,6874,8041,2990,3120,3554,1981,1935,216,2300,2341,2948,2837,1879,500,3739,2707,2584,2767,2647,341,861,752,638,699,814,5246,3798,301,258,3891,8358,8425,3251,8687,3497,8611,8486,8544,3349,3447,2091,8104,1624,1508,1680,1572,997,940,1790,8216,8159,3990,5198,4677,4329,4259,4763,4070,4999,4923,4868,4810,3035,3181,3943,2255,173,2168,561,386,6706,6782,6529,5654,5736,5401,7929,6008,6085,5822,6354,6441,6176,2211"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,47,48,49,50,51,54,55,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,74,75,76,78,79,80,81,82,83,84,85,86,87,88,91,92,93,94,95,96,97,98,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,187,188,189,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2863,2938,2994,3055,3105,3148,3225,3281,3323,3720,3772,3835,3888,3950,4200,4260,4381,4420,4467,4514,4668,4733,4804,5013,5087,5147,5216,5419,5492,5604,5646,5731,5850,5896,5952,6073,6118,6159,6270,6321,6389,6503,6563,6623,6682,6742,6925,6965,7012,7065,7123,7184,7246,7294,10699,10742,10784,10859,10930,10997,11048,11124,11174,11241,11302,11360,11458,11556,11619,11729,11781,12292,12348,12412,12469,12527,12637,12694,12749,12796,12847,12931,13001,13060,13146,13226,13302,13357,13415,13462,13507,13647,13699,13743,13888,13946,14007,14052,14229,14305,14392,14645,14727,14814,14942,15128,15205,15290,15468,15555,15645", "endColumns": "74,55,60,49,42,76,55,41,56,51,62,52,61,48,59,47,38,46,46,153,64,70,208,73,59,68,202,72,111,41,84,56,45,55,42,44,40,110,50,67,113,59,59,58,59,62,39,46,52,57,60,61,47,58,42,41,74,70,66,50,75,49,66,60,57,97,97,62,40,51,510,55,63,56,57,109,56,54,46,50,83,69,58,85,79,75,54,57,46,44,60,51,43,43,57,60,44,176,75,86,252,81,86,127,185,76,84,177,86,89,42", "endOffsets": "2933,2989,3050,3100,3143,3220,3276,3318,3375,3767,3830,3883,3945,3994,4255,4303,4415,4462,4509,4663,4728,4799,5008,5082,5142,5211,5414,5487,5599,5641,5726,5783,5891,5947,5990,6113,6154,6265,6316,6384,6498,6558,6618,6677,6737,6800,6960,7007,7060,7118,7179,7241,7289,7348,10737,10779,10854,10925,10992,11043,11119,11169,11236,11297,11355,11453,11551,11614,11655,11776,12287,12343,12407,12464,12522,12632,12689,12744,12791,12842,12926,12996,13055,13141,13221,13297,13352,13410,13457,13502,13563,13694,13738,13782,13941,14002,14047,14224,14300,14387,14640,14722,14809,14937,15123,15200,15285,15463,15550,15640,15683"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b7d68e85c64c5740c775d2c6236c08f5\\transformed\\core-1.9.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "190", "startColumns": "4", "startOffsets": "13787", "endColumns": "100", "endOffsets": "13883"}}]}]}