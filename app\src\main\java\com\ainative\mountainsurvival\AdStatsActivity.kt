package com.ainative.mountainsurvival

import android.os.Bundle
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity

/**
 * 广告统计查看Activity
 * 显示广告表现、收益和网络统计
 */
class AdStatsActivity : AppCompatActivity() {
    
    private lateinit var statsTextView: TextView
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 创建简单的布局
        setContentView(createLayout())
        
        // 设置标题
        title = "广告统计"
        
        // 显示统计信息
        displayStats()
    }
    
    private fun createLayout(): TextView {
        statsTextView = TextView(this).apply {
            textSize = 14f
            setPadding(32, 32, 32, 32)
            setTextIsSelectable(true)
        }
        return statsTextView
    }
    
    private fun displayStats() {
        val statsReport = AdStatsManager.getStatsReport()
        val networkStats = AdapterManager.getNetworkStats()
        
        val fullReport = StringBuilder()
        fullReport.append(statsReport)
        fullReport.append("\n\n=== 广告源配置 ===\n")
        
        networkStats.entries.sortedByDescending { it.value["weight"] as Int }.forEach { (network, stats) ->
            fullReport.append("${stats["displayName"]}: 权重 ${stats["weight"]}, 优先级 ${stats["priority"]}\n")
        }
        
        fullReport.append("\n=== 说明 ===\n")
        fullReport.append("• 本系统模拟了多个广告网络的行为\n")
        fullReport.append("• 使用瀑布流机制优化广告填充率\n")
        fullReport.append("• 收益数据基于真实eCPM范围模拟\n")
        fullReport.append("• 统计数据本地存储，重装应用后清零\n")
        
        statsTextView.text = fullReport.toString()
    }
}
