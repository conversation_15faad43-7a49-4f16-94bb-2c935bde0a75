pluginManagement {
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_SETTINGS)
    repositories {
        google()
        mavenCentral()

        // 神蓍广告SDK官方仓库配置（按照官方文档）
        // bidmachine
        maven { url = uri("https://artifactory.bidmachine.io/bidmachine") }
        // ironsource
        maven { url = uri("https://android-sdk.is.com") }
        // mintegral
        maven { url = uri("https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_oversea") }
        // pangle
        maven { url = uri("https://artifact.bytedance.com/repository/pangle") }
        // Anythink(Core)
        maven { url = uri("https://jfrog.anythinktech.com/artifactory/overseas_sdk") }
        // adSet (神蓍核心仓库)
        maven {
            isAllowInsecureProtocol = true
            url = uri("http://maven.shenshiads.com/nexus/repository/adset/")
        }

        // 其他仓库
        // GroMore
        maven { url = uri("https://artifact.bytedance.com/repository/Volcengine/") }
        // 荣耀仓库（广告SDK需要）
        maven { url = uri("https://developer.hihonor.com/repo") }
        // JitPack
        maven { url = uri("https://jitpack.io") }

        // 本地AAR文件支持
        flatDir {
            dirs("../app/libs")
        }
    }
}

rootProject.name = "MountainSurvival"
include(":app")
