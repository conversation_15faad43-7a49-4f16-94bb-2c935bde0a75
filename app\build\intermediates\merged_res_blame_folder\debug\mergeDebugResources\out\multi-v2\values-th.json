{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-mergeDebugResources-30:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5d5fa9c9e36bc912c1cb51114343e1e1\\transformed\\material-1.9.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,358,430,513,598,684,783,896,976,1046,1136,1206,1266,1353,1419,1484,1545,1609,1670,1724,1825,1886,1946,2000,2070,2181,2268,2349,2492,2571,2653,2745,2799,2852,2918,2988,3066,3152,3224,3302,3371,3440,3522,3610,3703,3797,3871,3940,4035,4087,4155,4240,4328,4390,4454,4517,4617,4710,4807,4900,4958,5015", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,72,71,82,84,85,98,112,79,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,80,142,78,81,91,53,52,65,69,77,85,71,77,68,68,81,87,92,93,73,68,94,51,67,84,87,61,63,62,99,92,96,92,57,56,76", "endOffsets": "280,353,425,508,593,679,778,891,971,1041,1131,1201,1261,1348,1414,1479,1540,1604,1665,1719,1820,1881,1941,1995,2065,2176,2263,2344,2487,2566,2648,2740,2794,2847,2913,2983,3061,3147,3219,3297,3366,3435,3517,3605,3698,3792,3866,3935,4030,4082,4150,4235,4323,4385,4449,4512,4612,4705,4802,4895,4953,5010,5087"}, "to": {"startLines": "2,42,43,44,45,46,52,53,56,73,77,89,90,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3616,3689,3761,3844,3929,4394,4493,4753,7241,7489,8681,8751,9990,10077,10143,10208,10269,10333,10394,10448,10549,10610,10670,10724,10794,10905,10992,11073,11216,11295,11377,11469,11523,11576,11642,11712,11790,11876,11948,12026,12095,12164,12246,12334,12427,12521,12595,12664,12759,12811,12879,12964,13052,13114,13178,13241,13341,13434,13531,13624,13682,15142", "endLines": "5,42,43,44,45,46,52,53,56,73,77,89,90,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,176", "endColumns": "12,72,71,82,84,85,98,112,79,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,80,142,78,81,91,53,52,65,69,77,85,71,77,68,68,81,87,92,93,73,68,94,51,67,84,87,61,63,62,99,92,96,92,57,56,76", "endOffsets": "330,3684,3756,3839,3924,4010,4488,4601,4828,7306,7574,8746,8806,10072,10138,10203,10264,10328,10389,10443,10544,10605,10665,10719,10789,10900,10987,11068,11211,11290,11372,11464,11518,11571,11637,11707,11785,11871,11943,12021,12090,12159,12241,12329,12422,12516,12590,12659,12754,12806,12874,12959,13047,13109,13173,13236,13336,13429,13526,13619,13677,13734,15214"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bde3453fac62cb9a4dc37198ed238829\\transformed\\appcompat-1.6.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "335,440,533,641,726,828,938,1016,1093,1184,1277,1368,1462,1562,1655,1750,1844,1935,2026,2107,2210,2308,2406,2509,2615,2716,2869,18331", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "435,528,636,721,823,933,1011,1088,1179,1272,1363,1457,1557,1650,1745,1839,1930,2021,2102,2205,2303,2401,2504,2610,2711,2864,2959,18408"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b7d68e85c64c5740c775d2c6236c08f5\\transformed\\core-1.9.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "203", "startColumns": "4", "startOffsets": "18577", "endColumns": "100", "endOffsets": "18673"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-th\\strings.xml", "from": {"startLines": "97,96,93,92,110,99,98,1,2,60,80,59,81,113,56,154,107,47,106,140,141,139,137,136,144,143,134,133,148,69,71,79,46,45,6,54,55,68,67,44,11,82,63,61,64,62,9,32,17,28,29,25,21,31,19,20,26,23,30,15,16,27,22,18,24,112,83,8,7,86,157,158,75,162,78,161,159,160,76,77,48,151,39,37,40,38,36,35,41,153,152,88,111,100,95,94,101,89,105,104,103,102,70,72,87,53,5,51,12,10,129,130,128,117,118,116,147,121,122,120,125,126,124,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7247,7181,6943,6883,8231,7421,7355,16,73,4551,6110,4487,6198,8410,4381,13879,8166,3917,8113,12648,12954,12539,12093,11980,13157,13060,11530,11417,13483,5226,5336,6039,3861,3787,207,4291,4337,5018,4955,3692,444,6292,4779,4625,4855,4699,351,1746,929,1534,1587,1374,1142,1692,1044,1094,1424,1257,1639,805,868,1481,1207,982,1315,8355,6378,303,255,6490,13991,14076,5584,14462,5974,14353,14180,14273,5643,5802,3971,13696,3435,1987,3491,3341,1923,1833,3553,13806,13739,6663,8289,7529,7092,7015,7654,6718,7972,7905,7834,7779,5276,5483,6602,4236,159,4107,698,393,10935,11282,10798,8649,9247,8494,13262,9545,9992,9382,10267,10651,10125,4180", "endColumns": "107,65,71,59,57,107,65,56,56,73,87,63,93,58,85,60,40,53,52,305,104,108,444,112,78,96,448,112,175,49,146,70,55,73,47,45,43,207,62,94,253,85,75,73,75,79,41,50,52,52,51,49,64,53,49,47,56,57,52,62,60,52,49,61,58,54,89,47,47,111,84,103,58,123,64,108,92,79,158,171,101,42,55,1353,61,93,63,89,109,72,66,54,65,124,88,76,124,135,140,66,70,54,59,74,60,54,47,72,79,50,346,109,136,597,133,154,220,446,131,162,383,145,141,55", "endOffsets": "7350,7242,7010,6938,8284,7524,7416,68,125,4620,6193,4546,6287,8464,4462,13935,8202,3966,8161,12949,13054,12643,12533,12088,13231,13152,11974,11525,13654,5271,5478,6105,3912,3856,250,4332,4376,5221,5013,3782,693,6373,4850,4694,4926,4774,388,1792,977,1582,1634,1419,1202,1741,1089,1137,1476,1310,1687,863,924,1529,1252,1039,1369,8405,6463,346,298,6597,14071,14175,5638,14581,6034,14457,14268,14348,5797,5969,4068,13734,3486,3336,3548,3430,1982,1918,3658,13874,13801,6713,8350,7649,7176,7087,7774,6849,8108,7967,7900,7829,5331,5553,6658,4286,202,4175,773,439,11277,11387,10930,9242,9376,8644,13478,9987,10119,9540,10646,10792,10262,4231"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,47,48,49,50,51,54,55,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,74,75,76,78,79,80,81,82,83,84,85,86,87,88,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,200,201,202,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2964,3072,3138,3210,3270,3328,3436,3502,3559,4015,4089,4177,4241,4335,4606,4692,4833,4874,4928,4981,5287,5392,5501,5946,6059,6138,6235,6684,6797,6973,7023,7170,7311,7367,7441,7579,7625,7669,7877,7940,8035,8289,8375,8451,8525,8601,8811,8853,8904,8957,9010,9062,9112,9177,9231,9281,9329,9386,9444,9497,9560,9621,9674,9724,9786,9845,9900,13739,13787,13835,13947,14032,14136,14195,14319,14384,14493,14586,14666,14825,14997,15099,15219,15275,16629,16691,16785,16849,16939,17049,17122,17189,17244,17310,17435,17524,17601,17726,17862,18003,18070,18141,18196,18256,18413,18474,18529,18678,18751,18831,18882,19229,19339,19476,20074,20208,20363,20584,21031,21163,21326,21710,21856,21998", "endColumns": "107,65,71,59,57,107,65,56,56,73,87,63,93,58,85,60,40,53,52,305,104,108,444,112,78,96,448,112,175,49,146,70,55,73,47,45,43,207,62,94,253,85,75,73,75,79,41,50,52,52,51,49,64,53,49,47,56,57,52,62,60,52,49,61,58,54,89,47,47,111,84,103,58,123,64,108,92,79,158,171,101,42,55,1353,61,93,63,89,109,72,66,54,65,124,88,76,124,135,140,66,70,54,59,74,60,54,47,72,79,50,346,109,136,597,133,154,220,446,131,162,383,145,141,55", "endOffsets": "3067,3133,3205,3265,3323,3431,3497,3554,3611,4084,4172,4236,4330,4389,4687,4748,4869,4923,4976,5282,5387,5496,5941,6054,6133,6230,6679,6792,6968,7018,7165,7236,7362,7436,7484,7620,7664,7872,7935,8030,8284,8370,8446,8520,8596,8676,8848,8899,8952,9005,9057,9107,9172,9226,9276,9324,9381,9439,9492,9555,9616,9669,9719,9781,9840,9895,9985,13782,13830,13942,14027,14131,14190,14314,14379,14488,14581,14661,14820,14992,15094,15137,15270,16624,16686,16780,16844,16934,17044,17117,17184,17239,17305,17430,17519,17596,17721,17857,17998,18065,18136,18191,18251,18326,18469,18524,18572,18746,18826,18877,19224,19334,19471,20069,20203,20358,20579,21026,21158,21321,21705,21851,21993,22049"}}]}]}