# Baseline profiles for androidx.appcompat

HSPLandroidx/appcompat/R$styleable;-><clinit>()V
HSPLandroidx/appcompat/app/ActionBar$LayoutParams;-><init>(II)V
HSPLandroidx/appcompat/app/ActionBar;-><init>()V
HSPLandroidx/appcompat/app/AppCompatActivity$1;-><init>(Landroidx/appcompat/app/AppCompatActivity;)V
HSPLandroidx/appcompat/app/AppCompatActivity$2;-><init>(Landroidx/appcompat/app/AppCompatActivity;)V
HSPLandroidx/appcompat/app/AppCompatActivity$2;->onContextAvailable(Landroid/content/Context;)V
HSPLandroidx/appcompat/app/AppCompatActivity;-><init>()V
HSPLandroidx/appcompat/app/AppCompatActivity;->attachBaseContext(Landroid/content/Context;)V
HSPLandroidx/appcompat/app/AppCompatActivity;->getDelegate()Landroidx/appcompat/app/AppCompatDelegate;
HSPLandroidx/appcompat/app/AppCompatActivity;->getMenuInflater()Landroid/view/MenuInflater;
HSPLandroidx/appcompat/app/AppCompatActivity;->getResources()Landroid/content/res/Resources;
HSPLandroidx/appcompat/app/AppCompatActivity;->initDelegate()V
HSPLandroidx/appcompat/app/AppCompatActivity;->initViewTreeOwners()V
HSPLandroidx/appcompat/app/AppCompatActivity;->onContentChanged()V
HSPLandroidx/appcompat/app/AppCompatActivity;->onPostCreate(Landroid/os/Bundle;)V
HSPLandroidx/appcompat/app/AppCompatActivity;->onPostResume()V
HSPLandroidx/appcompat/app/AppCompatActivity;->onStart()V
HSPLandroidx/appcompat/app/AppCompatActivity;->onSupportContentChanged()V
HSPLandroidx/appcompat/app/AppCompatActivity;->onTitleChanged(Ljava/lang/CharSequence;I)V
HSPLandroidx/appcompat/app/AppCompatActivity;->setContentView(I)V
HSPLandroidx/appcompat/app/AppCompatActivity;->setTheme(I)V
HSPLandroidx/appcompat/app/AppCompatDelegate;-><clinit>()V
HSPLandroidx/appcompat/app/AppCompatDelegate;-><init>()V
HSPLandroidx/appcompat/app/AppCompatDelegate;->addActiveDelegate(Landroidx/appcompat/app/AppCompatDelegate;)V
HSPLandroidx/appcompat/app/AppCompatDelegate;->attachBaseContext(Landroid/content/Context;)V
HSPLandroidx/appcompat/app/AppCompatDelegate;->attachBaseContext2(Landroid/content/Context;)Landroid/content/Context;
HSPLandroidx/appcompat/app/AppCompatDelegate;->create(Landroid/app/Activity;Landroidx/appcompat/app/AppCompatCallback;)Landroidx/appcompat/app/AppCompatDelegate;
HSPLandroidx/appcompat/app/AppCompatDelegate;->getDefaultNightMode()I
HSPLandroidx/appcompat/app/AppCompatDelegate;->removeDelegateFromActives(Landroidx/appcompat/app/AppCompatDelegate;)V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl$2;-><init>(Landroidx/appcompat/app/AppCompatDelegateImpl;)V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl$2;->run()V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl$3;-><init>(Landroidx/appcompat/app/AppCompatDelegateImpl;)V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl$5;-><init>(Landroidx/appcompat/app/AppCompatDelegateImpl;)V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl$5;->onAttachedFromWindow()V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl$ActionMenuPresenterCallback;-><init>(Landroidx/appcompat/app/AppCompatDelegateImpl;)V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl$Api17Impl;->createConfigurationContext(Landroid/content/Context;Landroid/content/res/Configuration;)Landroid/content/Context;
HSPLandroidx/appcompat/app/AppCompatDelegateImpl$AppCompatWindowCallback;-><init>(Landroidx/appcompat/app/AppCompatDelegateImpl;Landroid/view/Window$Callback;)V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl$AppCompatWindowCallback;->onContentChanged()V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl$AppCompatWindowCallback;->onCreatePanelMenu(ILandroid/view/Menu;)Z
HSPLandroidx/appcompat/app/AppCompatDelegateImpl$AppCompatWindowCallback;->onCreatePanelView(I)Landroid/view/View;
HSPLandroidx/appcompat/app/AppCompatDelegateImpl$AppCompatWindowCallback;->onPreparePanel(ILandroid/view/View;Landroid/view/Menu;)Z
HSPLandroidx/appcompat/app/AppCompatDelegateImpl$PanelFeatureState;-><init>(I)V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl$PanelFeatureState;->setMenu(Landroidx/appcompat/view/menu/MenuBuilder;)V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;-><clinit>()V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;-><init>(Landroid/app/Activity;Landroidx/appcompat/app/AppCompatCallback;)V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;-><init>(Landroid/content/Context;Landroid/view/Window;Landroidx/appcompat/app/AppCompatCallback;Ljava/lang/Object;)V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->applyDayNight()Z
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->applyDayNight(Z)Z
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->applyFixedSizeWindow()V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->attachBaseContext2(Landroid/content/Context;)Landroid/content/Context;
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->attachToWindow(Landroid/view/Window;)V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->calculateNightMode()I
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->createOverrideConfigurationForDayNight(Landroid/content/Context;ILandroid/content/res/Configuration;)Landroid/content/res/Configuration;
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->createSubDecor()Landroid/view/ViewGroup;
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->createView(Landroid/view/View;Ljava/lang/String;Landroid/content/Context;Landroid/util/AttributeSet;)Landroid/view/View;
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->doInvalidatePanelMenu(I)V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->ensureSubDecor()V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->ensureWindow()V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->getMenuInflater()Landroid/view/MenuInflater;
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->getPanelState(IZ)Landroidx/appcompat/app/AppCompatDelegateImpl$PanelFeatureState;
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->getSupportActionBar()Landroidx/appcompat/app/ActionBar;
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->getTitle()Ljava/lang/CharSequence;
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->getWindowCallback()Landroid/view/Window$Callback;
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->initWindowDecorActionBar()V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->initializePanelMenu(Landroidx/appcompat/app/AppCompatDelegateImpl$PanelFeatureState;)Z
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->installViewFactory()V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->invalidatePanelMenu(I)V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->isActivityManifestHandlingUiMode()Z
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->mapNightMode(Landroid/content/Context;I)I
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->onCreate(Landroid/os/Bundle;)V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->onCreateView(Landroid/view/View;Ljava/lang/String;Landroid/content/Context;Landroid/util/AttributeSet;)Landroid/view/View;
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->onPostCreate(Landroid/os/Bundle;)V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->onPostResume()V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->onStart()V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->onSubDecorInstalled(Landroid/view/ViewGroup;)V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->peekSupportActionBar()Landroidx/appcompat/app/ActionBar;
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->preparePanel(Landroidx/appcompat/app/AppCompatDelegateImpl$PanelFeatureState;Landroid/view/KeyEvent;)Z
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->requestWindowFeature(I)Z
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->sanitizeWindowFeatureId(I)I
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->setContentView(I)V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->setTheme(I)V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->setTitle(Ljava/lang/CharSequence;)V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->throwFeatureRequestIfSubDecorInstalled()V
HSPLandroidx/appcompat/app/AppCompatDelegateImpl;->updateForNightMode(IZ)Z
HSPLandroidx/appcompat/app/AppCompatViewInflater;-><clinit>()V
HSPLandroidx/appcompat/app/AppCompatViewInflater;-><init>()V
HSPLandroidx/appcompat/app/AppCompatViewInflater;->backportAccessibilityAttributes(Landroid/content/Context;Landroid/view/View;Landroid/util/AttributeSet;)V
HSPLandroidx/appcompat/app/AppCompatViewInflater;->checkOnClickListener(Landroid/view/View;Landroid/util/AttributeSet;)V
HSPLandroidx/appcompat/app/AppCompatViewInflater;->createButton(Landroid/content/Context;Landroid/util/AttributeSet;)Landroidx/appcompat/widget/AppCompatButton;
HSPLandroidx/appcompat/app/AppCompatViewInflater;->createEditText(Landroid/content/Context;Landroid/util/AttributeSet;)Landroidx/appcompat/widget/AppCompatEditText;
HSPLandroidx/appcompat/app/AppCompatViewInflater;->createTextView(Landroid/content/Context;Landroid/util/AttributeSet;)Landroidx/appcompat/widget/AppCompatTextView;
HSPLandroidx/appcompat/app/AppCompatViewInflater;->createView(Landroid/content/Context;Ljava/lang/String;Landroid/util/AttributeSet;)Landroid/view/View;
HSPLandroidx/appcompat/app/AppCompatViewInflater;->createView(Landroid/view/View;Ljava/lang/String;Landroid/content/Context;Landroid/util/AttributeSet;ZZZZ)Landroid/view/View;
HSPLandroidx/appcompat/app/AppCompatViewInflater;->themifyContext(Landroid/content/Context;Landroid/util/AttributeSet;ZZ)Landroid/content/Context;
HSPLandroidx/appcompat/app/AppCompatViewInflater;->verifyNotNull(Landroid/view/View;Ljava/lang/String;)V
HSPLandroidx/appcompat/app/WindowDecorActionBar$1;-><init>(Landroidx/appcompat/app/WindowDecorActionBar;)V
HSPLandroidx/appcompat/app/WindowDecorActionBar$2;-><init>(Landroidx/appcompat/app/WindowDecorActionBar;)V
HSPLandroidx/appcompat/app/WindowDecorActionBar$3;-><init>(Landroidx/appcompat/app/WindowDecorActionBar;)V
HSPLandroidx/appcompat/app/WindowDecorActionBar;-><clinit>()V
HSPLandroidx/appcompat/app/WindowDecorActionBar;-><init>(Landroid/app/Activity;Z)V
HSPLandroidx/appcompat/app/WindowDecorActionBar;->getDecorToolbar(Landroid/view/View;)Landroidx/appcompat/widget/DecorToolbar;
HSPLandroidx/appcompat/app/WindowDecorActionBar;->getNavigationMode()I
HSPLandroidx/appcompat/app/WindowDecorActionBar;->getThemedContext()Landroid/content/Context;
HSPLandroidx/appcompat/app/WindowDecorActionBar;->init(Landroid/view/View;)V
HSPLandroidx/appcompat/app/WindowDecorActionBar;->onWindowVisibilityChanged(I)V
HSPLandroidx/appcompat/app/WindowDecorActionBar;->setDefaultDisplayHomeAsUpEnabled(Z)V
HSPLandroidx/appcompat/app/WindowDecorActionBar;->setDisplayHomeAsUpEnabled(Z)V
HSPLandroidx/appcompat/app/WindowDecorActionBar;->setDisplayOptions(II)V
HSPLandroidx/appcompat/app/WindowDecorActionBar;->setElevation(F)V
HSPLandroidx/appcompat/app/WindowDecorActionBar;->setHasEmbeddedTabs(Z)V
HSPLandroidx/appcompat/app/WindowDecorActionBar;->setHomeButtonEnabled(Z)V
HSPLandroidx/appcompat/app/WindowDecorActionBar;->setShowHideAnimationEnabled(Z)V
HSPLandroidx/appcompat/view/ActionBarPolicy;-><init>(Landroid/content/Context;)V
HSPLandroidx/appcompat/view/ActionBarPolicy;->enableHomeButtonByDefault()Z
HSPLandroidx/appcompat/view/ActionBarPolicy;->get(Landroid/content/Context;)Landroidx/appcompat/view/ActionBarPolicy;
HSPLandroidx/appcompat/view/ActionBarPolicy;->getEmbeddedMenuWidthLimit()I
HSPLandroidx/appcompat/view/ActionBarPolicy;->getMaxActionButtons()I
HSPLandroidx/appcompat/view/ActionBarPolicy;->hasEmbeddedTabs()Z
HSPLandroidx/appcompat/view/ActionBarPolicy;->showsOverflowMenuButton()Z
HSPLandroidx/appcompat/view/ContextThemeWrapper;-><init>(Landroid/content/Context;I)V
HSPLandroidx/appcompat/view/ContextThemeWrapper;->applyOverrideConfiguration(Landroid/content/res/Configuration;)V
HSPLandroidx/appcompat/view/ContextThemeWrapper;->getResources()Landroid/content/res/Resources;
HSPLandroidx/appcompat/view/ContextThemeWrapper;->getResourcesInternal()Landroid/content/res/Resources;
HSPLandroidx/appcompat/view/ContextThemeWrapper;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;
HSPLandroidx/appcompat/view/ContextThemeWrapper;->getTheme()Landroid/content/res/Resources$Theme;
HSPLandroidx/appcompat/view/ContextThemeWrapper;->initializeTheme()V
HSPLandroidx/appcompat/view/ContextThemeWrapper;->onApplyThemeResource(Landroid/content/res/Resources$Theme;IZ)V
HSPLandroidx/appcompat/view/SupportMenuInflater;-><clinit>()V
HSPLandroidx/appcompat/view/SupportMenuInflater;-><init>(Landroid/content/Context;)V
HSPLandroidx/appcompat/view/WindowCallbackWrapper;-><init>(Landroid/view/Window$Callback;)V
HSPLandroidx/appcompat/view/WindowCallbackWrapper;->dispatchPopulateAccessibilityEvent(Landroid/view/accessibility/AccessibilityEvent;)Z
HSPLandroidx/appcompat/view/WindowCallbackWrapper;->dispatchTouchEvent(Landroid/view/MotionEvent;)Z
HSPLandroidx/appcompat/view/WindowCallbackWrapper;->getWrapped()Landroid/view/Window$Callback;
HSPLandroidx/appcompat/view/WindowCallbackWrapper;->onAttachedToWindow()V
HSPLandroidx/appcompat/view/WindowCallbackWrapper;->onCreatePanelMenu(ILandroid/view/Menu;)Z
HSPLandroidx/appcompat/view/WindowCallbackWrapper;->onCreatePanelView(I)Landroid/view/View;
HSPLandroidx/appcompat/view/WindowCallbackWrapper;->onPreparePanel(ILandroid/view/View;Landroid/view/Menu;)Z
HSPLandroidx/appcompat/view/WindowCallbackWrapper;->onWindowAttributesChanged(Landroid/view/WindowManager$LayoutParams;)V
HSPLandroidx/appcompat/view/WindowCallbackWrapper;->onWindowFocusChanged(Z)V
HSPLandroidx/appcompat/view/menu/ActionMenuItem;-><init>(Landroid/content/Context;IIIILjava/lang/CharSequence;)V
HSPLandroidx/appcompat/view/menu/BaseMenuPresenter;-><init>(Landroid/content/Context;II)V
HSPLandroidx/appcompat/view/menu/BaseMenuPresenter;->initForMenu(Landroid/content/Context;Landroidx/appcompat/view/menu/MenuBuilder;)V
HSPLandroidx/appcompat/view/menu/BaseMenuPresenter;->setCallback(Landroidx/appcompat/view/menu/MenuPresenter$Callback;)V
HSPLandroidx/appcompat/view/menu/BaseMenuPresenter;->setId(I)V
HSPLandroidx/appcompat/view/menu/BaseMenuPresenter;->updateMenuView(Z)V
HSPLandroidx/appcompat/view/menu/MenuBuilder;-><clinit>()V
HSPLandroidx/appcompat/view/menu/MenuBuilder;-><init>(Landroid/content/Context;)V
HSPLandroidx/appcompat/view/menu/MenuBuilder;->addMenuPresenter(Landroidx/appcompat/view/menu/MenuPresenter;Landroid/content/Context;)V
HSPLandroidx/appcompat/view/menu/MenuBuilder;->dispatchPresenterUpdate(Z)V
HSPLandroidx/appcompat/view/menu/MenuBuilder;->flagActionItems()V
HSPLandroidx/appcompat/view/menu/MenuBuilder;->getActionItems()Ljava/util/ArrayList;
HSPLandroidx/appcompat/view/menu/MenuBuilder;->getNonActionItems()Ljava/util/ArrayList;
HSPLandroidx/appcompat/view/menu/MenuBuilder;->getVisibleItems()Ljava/util/ArrayList;
HSPLandroidx/appcompat/view/menu/MenuBuilder;->hasVisibleItems()Z
HSPLandroidx/appcompat/view/menu/MenuBuilder;->onItemsChanged(Z)V
HSPLandroidx/appcompat/view/menu/MenuBuilder;->setCallback(Landroidx/appcompat/view/menu/MenuBuilder$Callback;)V
HSPLandroidx/appcompat/view/menu/MenuBuilder;->setOverrideVisibleItems(Z)V
HSPLandroidx/appcompat/view/menu/MenuBuilder;->setQwertyMode(Z)V
HSPLandroidx/appcompat/view/menu/MenuBuilder;->setShortcutsVisibleInner(Z)V
HSPLandroidx/appcompat/view/menu/MenuBuilder;->size()I
HSPLandroidx/appcompat/view/menu/MenuBuilder;->startDispatchingItemsChanged()V
HSPLandroidx/appcompat/view/menu/MenuBuilder;->stopDispatchingItemsChanged()V
HSPLandroidx/appcompat/widget/AbsActionBarView$VisibilityAnimListener;-><init>(Landroidx/appcompat/widget/AbsActionBarView;)V
HSPLandroidx/appcompat/widget/AbsActionBarView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
HSPLandroidx/appcompat/widget/ActionBarBackgroundDrawable;-><init>(Landroidx/appcompat/widget/ActionBarContainer;)V
HSPLandroidx/appcompat/widget/ActionBarBackgroundDrawable;->draw(Landroid/graphics/Canvas;)V
HSPLandroidx/appcompat/widget/ActionBarBackgroundDrawable;->getOpacity()I
HSPLandroidx/appcompat/widget/ActionBarBackgroundDrawable;->getOutline(Landroid/graphics/Outline;)V
HSPLandroidx/appcompat/widget/ActionBarContainer;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
HSPLandroidx/appcompat/widget/ActionBarContainer;->drawableStateChanged()V
HSPLandroidx/appcompat/widget/ActionBarContainer;->jumpDrawablesToCurrentState()V
HSPLandroidx/appcompat/widget/ActionBarContainer;->onFinishInflate()V
HSPLandroidx/appcompat/widget/ActionBarContainer;->onLayout(ZIIII)V
HSPLandroidx/appcompat/widget/ActionBarContainer;->onMeasure(II)V
HSPLandroidx/appcompat/widget/ActionBarContainer;->setTabContainer(Landroidx/appcompat/widget/ScrollingTabContainerView;)V
HSPLandroidx/appcompat/widget/ActionBarContextView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
HSPLandroidx/appcompat/widget/ActionBarContextView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
HSPLandroidx/appcompat/widget/ActionBarOverlayLayout$1;-><init>(Landroidx/appcompat/widget/ActionBarOverlayLayout;)V
HSPLandroidx/appcompat/widget/ActionBarOverlayLayout$2;-><init>(Landroidx/appcompat/widget/ActionBarOverlayLayout;)V
HSPLandroidx/appcompat/widget/ActionBarOverlayLayout$3;-><init>(Landroidx/appcompat/widget/ActionBarOverlayLayout;)V
HSPLandroidx/appcompat/widget/ActionBarOverlayLayout$LayoutParams;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
HSPLandroidx/appcompat/widget/ActionBarOverlayLayout;-><clinit>()V
HSPLandroidx/appcompat/widget/ActionBarOverlayLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
HSPLandroidx/appcompat/widget/ActionBarOverlayLayout;->applyInsets(Landroid/view/View;Landroid/graphics/Rect;ZZZZ)Z
HSPLandroidx/appcompat/widget/ActionBarOverlayLayout;->checkLayoutParams(Landroid/view/ViewGroup$LayoutParams;)Z
HSPLandroidx/appcompat/widget/ActionBarOverlayLayout;->generateLayoutParams(Landroid/util/AttributeSet;)Landroid/view/ViewGroup$LayoutParams;
HSPLandroidx/appcompat/widget/ActionBarOverlayLayout;->generateLayoutParams(Landroid/util/AttributeSet;)Landroidx/appcompat/widget/ActionBarOverlayLayout$LayoutParams;
HSPLandroidx/appcompat/widget/ActionBarOverlayLayout;->getDecorToolbar(Landroid/view/View;)Landroidx/appcompat/widget/DecorToolbar;
HSPLandroidx/appcompat/widget/ActionBarOverlayLayout;->init(Landroid/content/Context;)V
HSPLandroidx/appcompat/widget/ActionBarOverlayLayout;->onLayout(ZIIII)V
HSPLandroidx/appcompat/widget/ActionBarOverlayLayout;->onMeasure(II)V
HSPLandroidx/appcompat/widget/ActionBarOverlayLayout;->onStartNestedScroll(Landroid/view/View;Landroid/view/View;I)Z
HSPLandroidx/appcompat/widget/ActionBarOverlayLayout;->onStartNestedScroll(Landroid/view/View;Landroid/view/View;II)Z
HSPLandroidx/appcompat/widget/ActionBarOverlayLayout;->onWindowVisibilityChanged(I)V
HSPLandroidx/appcompat/widget/ActionBarOverlayLayout;->pullChildren()V
HSPLandroidx/appcompat/widget/ActionBarOverlayLayout;->setActionBarVisibilityCallback(Landroidx/appcompat/widget/ActionBarOverlayLayout$ActionBarVisibilityCallback;)V
HSPLandroidx/appcompat/widget/ActionBarOverlayLayout;->setHasNonEmbeddedTabs(Z)V
HSPLandroidx/appcompat/widget/ActionBarOverlayLayout;->setMenu(Landroid/view/Menu;Landroidx/appcompat/view/menu/MenuPresenter$Callback;)V
HSPLandroidx/appcompat/widget/ActionBarOverlayLayout;->setMenuPrepared()V
HSPLandroidx/appcompat/widget/ActionBarOverlayLayout;->setWindowCallback(Landroid/view/Window$Callback;)V
HSPLandroidx/appcompat/widget/ActionBarOverlayLayout;->setWindowTitle(Ljava/lang/CharSequence;)V
HSPLandroidx/appcompat/widget/ActionBarOverlayLayout;->shouldDelayChildPressedState()Z
HSPLandroidx/appcompat/widget/ActionMenuPresenter$OverflowMenuButton$1;-><init>(Landroidx/appcompat/widget/ActionMenuPresenter$OverflowMenuButton;Landroid/view/View;Landroidx/appcompat/widget/ActionMenuPresenter;)V
HSPLandroidx/appcompat/widget/ActionMenuPresenter$OverflowMenuButton;-><init>(Landroidx/appcompat/widget/ActionMenuPresenter;Landroid/content/Context;)V
HSPLandroidx/appcompat/widget/ActionMenuPresenter$PopupPresenterCallback;-><init>(Landroidx/appcompat/widget/ActionMenuPresenter;)V
HSPLandroidx/appcompat/widget/ActionMenuPresenter;-><init>(Landroid/content/Context;)V
HSPLandroidx/appcompat/widget/ActionMenuPresenter;->flagActionItems()Z
HSPLandroidx/appcompat/widget/ActionMenuPresenter;->initForMenu(Landroid/content/Context;Landroidx/appcompat/view/menu/MenuBuilder;)V
HSPLandroidx/appcompat/widget/ActionMenuPresenter;->setExpandedActionViewsExclusive(Z)V
HSPLandroidx/appcompat/widget/ActionMenuPresenter;->setMenuView(Landroidx/appcompat/widget/ActionMenuView;)V
HSPLandroidx/appcompat/widget/ActionMenuPresenter;->updateMenuView(Z)V
HSPLandroidx/appcompat/widget/ActionMenuView;-><init>(Landroid/content/Context;)V
HSPLandroidx/appcompat/widget/ActionMenuView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
HSPLandroidx/appcompat/widget/ActionMenuView;->initialize(Landroidx/appcompat/view/menu/MenuBuilder;)V
HSPLandroidx/appcompat/widget/ActionMenuView;->onLayout(ZIIII)V
HSPLandroidx/appcompat/widget/ActionMenuView;->onMeasure(II)V
HSPLandroidx/appcompat/widget/ActionMenuView;->peekMenu()Landroidx/appcompat/view/menu/MenuBuilder;
HSPLandroidx/appcompat/widget/ActionMenuView;->setMenuCallbacks(Landroidx/appcompat/view/menu/MenuPresenter$Callback;Landroidx/appcompat/view/menu/MenuBuilder$Callback;)V
HSPLandroidx/appcompat/widget/ActionMenuView;->setOnMenuItemClickListener(Landroidx/appcompat/widget/ActionMenuView$OnMenuItemClickListener;)V
HSPLandroidx/appcompat/widget/ActionMenuView;->setOverflowReserved(Z)V
HSPLandroidx/appcompat/widget/ActionMenuView;->setPopupTheme(I)V
HSPLandroidx/appcompat/widget/ActionMenuView;->setPresenter(Landroidx/appcompat/widget/ActionMenuPresenter;)V
HSPLandroidx/appcompat/widget/AppCompatBackgroundHelper;-><init>(Landroid/view/View;)V
HSPLandroidx/appcompat/widget/AppCompatBackgroundHelper;->applySupportBackgroundTint()V
HSPLandroidx/appcompat/widget/AppCompatBackgroundHelper;->loadFromAttributes(Landroid/util/AttributeSet;I)V
HSPLandroidx/appcompat/widget/AppCompatBackgroundHelper;->shouldApplyFrameworkTintUsingColorFilter()Z
HSPLandroidx/appcompat/widget/AppCompatButton;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
HSPLandroidx/appcompat/widget/AppCompatButton;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
HSPLandroidx/appcompat/widget/AppCompatButton;->drawableStateChanged()V
HSPLandroidx/appcompat/widget/AppCompatButton;->getEmojiTextViewHelper()Landroidx/appcompat/widget/AppCompatEmojiTextHelper;
HSPLandroidx/appcompat/widget/AppCompatButton;->onInitializeAccessibilityEvent(Landroid/view/accessibility/AccessibilityEvent;)V
HSPLandroidx/appcompat/widget/AppCompatButton;->onInitializeAccessibilityNodeInfo(Landroid/view/accessibility/AccessibilityNodeInfo;)V
HSPLandroidx/appcompat/widget/AppCompatButton;->onLayout(ZIIII)V
HSPLandroidx/appcompat/widget/AppCompatButton;->onTextChanged(Ljava/lang/CharSequence;III)V
HSPLandroidx/appcompat/widget/AppCompatButton;->setBackgroundDrawable(Landroid/graphics/drawable/Drawable;)V
HSPLandroidx/appcompat/widget/AppCompatButton;->setFilters([Landroid/text/InputFilter;)V
HSPLandroidx/appcompat/widget/AppCompatDrawableManager$1;-><init>()V
HSPLandroidx/appcompat/widget/AppCompatDrawableManager$1;->arrayContains([II)Z
HSPLandroidx/appcompat/widget/AppCompatDrawableManager$1;->createDrawableFor(Landroidx/appcompat/widget/ResourceManagerInternal;Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;
HSPLandroidx/appcompat/widget/AppCompatDrawableManager$1;->getTintListForDrawableRes(Landroid/content/Context;I)Landroid/content/res/ColorStateList;
HSPLandroidx/appcompat/widget/AppCompatDrawableManager$1;->tintDrawable(Landroid/content/Context;ILandroid/graphics/drawable/Drawable;)Z
HSPLandroidx/appcompat/widget/AppCompatDrawableManager$1;->tintDrawableUsingColorFilter(Landroid/content/Context;ILandroid/graphics/drawable/Drawable;)Z
HSPLandroidx/appcompat/widget/AppCompatDrawableManager;-><clinit>()V
HSPLandroidx/appcompat/widget/AppCompatDrawableManager;-><init>()V
HSPLandroidx/appcompat/widget/AppCompatDrawableManager;->access$000()Landroid/graphics/PorterDuff$Mode;
HSPLandroidx/appcompat/widget/AppCompatDrawableManager;->get()Landroidx/appcompat/widget/AppCompatDrawableManager;
HSPLandroidx/appcompat/widget/AppCompatDrawableManager;->getDrawable(Landroid/content/Context;IZ)Landroid/graphics/drawable/Drawable;
HSPLandroidx/appcompat/widget/AppCompatDrawableManager;->getTintList(Landroid/content/Context;I)Landroid/content/res/ColorStateList;
HSPLandroidx/appcompat/widget/AppCompatDrawableManager;->preload()V
HSPLandroidx/appcompat/widget/AppCompatEditText;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
HSPLandroidx/appcompat/widget/AppCompatEditText;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
HSPLandroidx/appcompat/widget/AppCompatEditText;->drawableStateChanged()V
HSPLandroidx/appcompat/widget/AppCompatEditText;->getText()Landroid/text/Editable;
HSPLandroidx/appcompat/widget/AppCompatEditText;->getText()Ljava/lang/CharSequence;
HSPLandroidx/appcompat/widget/AppCompatEditText;->setBackgroundDrawable(Landroid/graphics/drawable/Drawable;)V
HSPLandroidx/appcompat/widget/AppCompatEditText;->setKeyListener(Landroid/text/method/KeyListener;)V
HSPLandroidx/appcompat/widget/AppCompatEmojiEditTextHelper;-><init>(Landroid/widget/EditText;)V
HSPLandroidx/appcompat/widget/AppCompatEmojiEditTextHelper;->getKeyListener(Landroid/text/method/KeyListener;)Landroid/text/method/KeyListener;
HSPLandroidx/appcompat/widget/AppCompatEmojiEditTextHelper;->initKeyListener()V
HSPLandroidx/appcompat/widget/AppCompatEmojiEditTextHelper;->loadFromAttributes(Landroid/util/AttributeSet;I)V
HSPLandroidx/appcompat/widget/AppCompatEmojiEditTextHelper;->setEnabled(Z)V
HSPLandroidx/appcompat/widget/AppCompatEmojiTextHelper;-><init>(Landroid/widget/TextView;)V
HSPLandroidx/appcompat/widget/AppCompatEmojiTextHelper;->getFilters([Landroid/text/InputFilter;)[Landroid/text/InputFilter;
HSPLandroidx/appcompat/widget/AppCompatEmojiTextHelper;->loadFromAttributes(Landroid/util/AttributeSet;I)V
HSPLandroidx/appcompat/widget/AppCompatEmojiTextHelper;->setEnabled(Z)V
HSPLandroidx/appcompat/widget/AppCompatImageButton;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
HSPLandroidx/appcompat/widget/AppCompatImageButton;->setBackgroundDrawable(Landroid/graphics/drawable/Drawable;)V
HSPLandroidx/appcompat/widget/AppCompatImageButton;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V
HSPLandroidx/appcompat/widget/AppCompatImageHelper;-><init>(Landroid/widget/ImageView;)V
HSPLandroidx/appcompat/widget/AppCompatImageHelper;->applyImageLevel()V
HSPLandroidx/appcompat/widget/AppCompatImageHelper;->applySupportImageTint()V
HSPLandroidx/appcompat/widget/AppCompatImageHelper;->loadFromAttributes(Landroid/util/AttributeSet;I)V
HSPLandroidx/appcompat/widget/AppCompatImageView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
HSPLandroidx/appcompat/widget/AppCompatImageView;->setBackgroundDrawable(Landroid/graphics/drawable/Drawable;)V
HSPLandroidx/appcompat/widget/AppCompatImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V
HSPLandroidx/appcompat/widget/AppCompatTextClassifierHelper;-><init>(Landroid/widget/TextView;)V
HSPLandroidx/appcompat/widget/AppCompatTextHelper$1;-><init>(Landroidx/appcompat/widget/AppCompatTextHelper;IILjava/lang/ref/WeakReference;)V
HSPLandroidx/appcompat/widget/AppCompatTextHelper$1;->onFontRetrievalFailed(I)V
HSPLandroidx/appcompat/widget/AppCompatTextHelper;-><init>(Landroid/widget/TextView;)V
HSPLandroidx/appcompat/widget/AppCompatTextHelper;->applyCompoundDrawablesTints()V
HSPLandroidx/appcompat/widget/AppCompatTextHelper;->createTintInfo(Landroid/content/Context;Landroidx/appcompat/widget/AppCompatDrawableManager;I)Landroidx/appcompat/widget/TintInfo;
HSPLandroidx/appcompat/widget/AppCompatTextHelper;->loadFromAttributes(Landroid/util/AttributeSet;I)V
HSPLandroidx/appcompat/widget/AppCompatTextHelper;->onLayout(ZIIII)V
HSPLandroidx/appcompat/widget/AppCompatTextHelper;->onSetTextAppearance(Landroid/content/Context;I)V
HSPLandroidx/appcompat/widget/AppCompatTextHelper;->setCompoundDrawables(Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;)V
HSPLandroidx/appcompat/widget/AppCompatTextHelper;->updateTypefaceAndStyle(Landroid/content/Context;Landroidx/appcompat/widget/TintTypedArray;)V
HSPLandroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V
HSPLandroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
HSPLandroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
HSPLandroidx/appcompat/widget/AppCompatTextView;->consumeTextFutureAndSetBlocking()V
HSPLandroidx/appcompat/widget/AppCompatTextView;->drawableStateChanged()V
HSPLandroidx/appcompat/widget/AppCompatTextView;->getEmojiTextViewHelper()Landroidx/appcompat/widget/AppCompatEmojiTextHelper;
HSPLandroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;
HSPLandroidx/appcompat/widget/AppCompatTextView;->onLayout(ZIIII)V
HSPLandroidx/appcompat/widget/AppCompatTextView;->onMeasure(II)V
HSPLandroidx/appcompat/widget/AppCompatTextView;->onTextChanged(Ljava/lang/CharSequence;III)V
HSPLandroidx/appcompat/widget/AppCompatTextView;->setCompoundDrawables(Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;)V
HSPLandroidx/appcompat/widget/AppCompatTextView;->setCompoundDrawablesWithIntrinsicBounds(Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;)V
HSPLandroidx/appcompat/widget/AppCompatTextView;->setFilters([Landroid/text/InputFilter;)V
HSPLandroidx/appcompat/widget/AppCompatTextView;->setTextAppearance(Landroid/content/Context;I)V
HSPLandroidx/appcompat/widget/AppCompatTextView;->setTypeface(Landroid/graphics/Typeface;I)V
HSPLandroidx/appcompat/widget/AppCompatTextViewAutoSizeHelper$Impl23;-><init>()V
HSPLandroidx/appcompat/widget/AppCompatTextViewAutoSizeHelper$Impl29;-><init>()V
HSPLandroidx/appcompat/widget/AppCompatTextViewAutoSizeHelper$Impl;-><init>()V
HSPLandroidx/appcompat/widget/AppCompatTextViewAutoSizeHelper;-><clinit>()V
HSPLandroidx/appcompat/widget/AppCompatTextViewAutoSizeHelper;-><init>(Landroid/widget/TextView;)V
HSPLandroidx/appcompat/widget/AppCompatTextViewAutoSizeHelper;->getAutoSizeTextType()I
HSPLandroidx/appcompat/widget/AppCompatTextViewAutoSizeHelper;->loadFromAttributes(Landroid/util/AttributeSet;I)V
HSPLandroidx/appcompat/widget/AppCompatTextViewAutoSizeHelper;->supportsAutoSizeText()Z
HSPLandroidx/appcompat/widget/ContentFrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
HSPLandroidx/appcompat/widget/ContentFrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
HSPLandroidx/appcompat/widget/ContentFrameLayout;->getMinWidthMajor()Landroid/util/TypedValue;
HSPLandroidx/appcompat/widget/ContentFrameLayout;->getMinWidthMinor()Landroid/util/TypedValue;
HSPLandroidx/appcompat/widget/ContentFrameLayout;->onAttachedToWindow()V
HSPLandroidx/appcompat/widget/ContentFrameLayout;->onMeasure(II)V
HSPLandroidx/appcompat/widget/ContentFrameLayout;->setAttachListener(Landroidx/appcompat/widget/ContentFrameLayout$OnAttachListener;)V
HSPLandroidx/appcompat/widget/ContentFrameLayout;->setDecorPadding(IIII)V
HSPLandroidx/appcompat/widget/ForwardingListener;-><init>(Landroid/view/View;)V
HSPLandroidx/appcompat/widget/LinearLayoutCompat;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
HSPLandroidx/appcompat/widget/LinearLayoutCompat;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
HSPLandroidx/appcompat/widget/LinearLayoutCompat;->getVirtualChildCount()I
HSPLandroidx/appcompat/widget/LinearLayoutCompat;->layoutHorizontal(IIII)V
HSPLandroidx/appcompat/widget/LinearLayoutCompat;->measureHorizontal(II)V
HSPLandroidx/appcompat/widget/LinearLayoutCompat;->onInitializeAccessibilityNodeInfo(Landroid/view/accessibility/AccessibilityNodeInfo;)V
HSPLandroidx/appcompat/widget/LinearLayoutCompat;->onLayout(ZIIII)V
HSPLandroidx/appcompat/widget/LinearLayoutCompat;->onMeasure(II)V
HSPLandroidx/appcompat/widget/LinearLayoutCompat;->setBaselineAligned(Z)V
HSPLandroidx/appcompat/widget/LinearLayoutCompat;->setDividerDrawable(Landroid/graphics/drawable/Drawable;)V
HSPLandroidx/appcompat/widget/RtlSpacingHelper;-><init>()V
HSPLandroidx/appcompat/widget/RtlSpacingHelper;->getEnd()I
HSPLandroidx/appcompat/widget/RtlSpacingHelper;->getStart()I
HSPLandroidx/appcompat/widget/RtlSpacingHelper;->setAbsolute(II)V
HSPLandroidx/appcompat/widget/RtlSpacingHelper;->setDirection(Z)V
HSPLandroidx/appcompat/widget/RtlSpacingHelper;->setRelative(II)V
HSPLandroidx/appcompat/widget/ThemeUtils;-><clinit>()V
HSPLandroidx/appcompat/widget/ThemeUtils;->checkAppCompatTheme(Landroid/view/View;Landroid/content/Context;)V
HSPLandroidx/appcompat/widget/TintContextWrapper;-><clinit>()V
HSPLandroidx/appcompat/widget/TintContextWrapper;->shouldWrap(Landroid/content/Context;)Z
HSPLandroidx/appcompat/widget/TintContextWrapper;->wrap(Landroid/content/Context;)Landroid/content/Context;
HSPLandroidx/appcompat/widget/TintTypedArray;-><init>(Landroid/content/Context;Landroid/content/res/TypedArray;)V
HSPLandroidx/appcompat/widget/TintTypedArray;->getBoolean(IZ)Z
HSPLandroidx/appcompat/widget/TintTypedArray;->getColor(II)I
HSPLandroidx/appcompat/widget/TintTypedArray;->getColorStateList(I)Landroid/content/res/ColorStateList;
HSPLandroidx/appcompat/widget/TintTypedArray;->getDimension(IF)F
HSPLandroidx/appcompat/widget/TintTypedArray;->getDimensionPixelOffset(II)I
HSPLandroidx/appcompat/widget/TintTypedArray;->getDimensionPixelSize(II)I
HSPLandroidx/appcompat/widget/TintTypedArray;->getDrawable(I)Landroid/graphics/drawable/Drawable;
HSPLandroidx/appcompat/widget/TintTypedArray;->getDrawableIfKnown(I)Landroid/graphics/drawable/Drawable;
HSPLandroidx/appcompat/widget/TintTypedArray;->getFloat(IF)F
HSPLandroidx/appcompat/widget/TintTypedArray;->getFont(IILandroidx/core/content/res/ResourcesCompat$FontCallback;)Landroid/graphics/Typeface;
HSPLandroidx/appcompat/widget/TintTypedArray;->getInt(II)I
HSPLandroidx/appcompat/widget/TintTypedArray;->getInteger(II)I
HSPLandroidx/appcompat/widget/TintTypedArray;->getLayoutDimension(II)I
HSPLandroidx/appcompat/widget/TintTypedArray;->getResourceId(II)I
HSPLandroidx/appcompat/widget/TintTypedArray;->getString(I)Ljava/lang/String;
HSPLandroidx/appcompat/widget/TintTypedArray;->getText(I)Ljava/lang/CharSequence;
HSPLandroidx/appcompat/widget/TintTypedArray;->getWrappedTypeArray()Landroid/content/res/TypedArray;
HSPLandroidx/appcompat/widget/TintTypedArray;->hasValue(I)Z
HSPLandroidx/appcompat/widget/TintTypedArray;->obtainStyledAttributes(Landroid/content/Context;I[I)Landroidx/appcompat/widget/TintTypedArray;
HSPLandroidx/appcompat/widget/TintTypedArray;->obtainStyledAttributes(Landroid/content/Context;Landroid/util/AttributeSet;[I)Landroidx/appcompat/widget/TintTypedArray;
HSPLandroidx/appcompat/widget/TintTypedArray;->obtainStyledAttributes(Landroid/content/Context;Landroid/util/AttributeSet;[III)Landroidx/appcompat/widget/TintTypedArray;
HSPLandroidx/appcompat/widget/TintTypedArray;->recycle()V
HSPLandroidx/appcompat/widget/Toolbar$$ExternalSyntheticLambda0;-><init>(Landroidx/appcompat/widget/Toolbar;)V
HSPLandroidx/appcompat/widget/Toolbar$1;-><init>(Landroidx/appcompat/widget/Toolbar;)V
HSPLandroidx/appcompat/widget/Toolbar$2;-><init>(Landroidx/appcompat/widget/Toolbar;)V
HSPLandroidx/appcompat/widget/Toolbar$ExpandedActionViewMenuPresenter;-><init>(Landroidx/appcompat/widget/Toolbar;)V
HSPLandroidx/appcompat/widget/Toolbar$ExpandedActionViewMenuPresenter;->flagActionItems()Z
HSPLandroidx/appcompat/widget/Toolbar$ExpandedActionViewMenuPresenter;->initForMenu(Landroid/content/Context;Landroidx/appcompat/view/menu/MenuBuilder;)V
HSPLandroidx/appcompat/widget/Toolbar$ExpandedActionViewMenuPresenter;->updateMenuView(Z)V
HSPLandroidx/appcompat/widget/Toolbar$LayoutParams;-><init>(II)V
HSPLandroidx/appcompat/widget/Toolbar;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
HSPLandroidx/appcompat/widget/Toolbar;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
HSPLandroidx/appcompat/widget/Toolbar;->addCustomViewsWithGravity(Ljava/util/List;I)V
HSPLandroidx/appcompat/widget/Toolbar;->addSystemView(Landroid/view/View;Z)V
HSPLandroidx/appcompat/widget/Toolbar;->checkLayoutParams(Landroid/view/ViewGroup$LayoutParams;)Z
HSPLandroidx/appcompat/widget/Toolbar;->ensureContentInsets()V
HSPLandroidx/appcompat/widget/Toolbar;->ensureMenuView()V
HSPLandroidx/appcompat/widget/Toolbar;->ensureNavButtonView()V
HSPLandroidx/appcompat/widget/Toolbar;->generateDefaultLayoutParams()Landroidx/appcompat/widget/Toolbar$LayoutParams;
HSPLandroidx/appcompat/widget/Toolbar;->getChildTop(Landroid/view/View;I)I
HSPLandroidx/appcompat/widget/Toolbar;->getChildVerticalGravity(I)I
HSPLandroidx/appcompat/widget/Toolbar;->getContentInsetEnd()I
HSPLandroidx/appcompat/widget/Toolbar;->getContentInsetStart()I
HSPLandroidx/appcompat/widget/Toolbar;->getCurrentContentInsetEnd()I
HSPLandroidx/appcompat/widget/Toolbar;->getCurrentContentInsetLeft()I
HSPLandroidx/appcompat/widget/Toolbar;->getCurrentContentInsetRight()I
HSPLandroidx/appcompat/widget/Toolbar;->getCurrentContentInsetStart()I
HSPLandroidx/appcompat/widget/Toolbar;->getHorizontalMargins(Landroid/view/View;)I
HSPLandroidx/appcompat/widget/Toolbar;->getNavigationContentDescription()Ljava/lang/CharSequence;
HSPLandroidx/appcompat/widget/Toolbar;->getNavigationIcon()Landroid/graphics/drawable/Drawable;
HSPLandroidx/appcompat/widget/Toolbar;->getSubtitle()Ljava/lang/CharSequence;
HSPLandroidx/appcompat/widget/Toolbar;->getTitle()Ljava/lang/CharSequence;
HSPLandroidx/appcompat/widget/Toolbar;->getVerticalMargins(Landroid/view/View;)I
HSPLandroidx/appcompat/widget/Toolbar;->getViewListMeasuredWidth(Ljava/util/List;[I)I
HSPLandroidx/appcompat/widget/Toolbar;->getWrapper()Landroidx/appcompat/widget/DecorToolbar;
HSPLandroidx/appcompat/widget/Toolbar;->isChildOrHidden(Landroid/view/View;)Z
HSPLandroidx/appcompat/widget/Toolbar;->layoutChildRight(Landroid/view/View;I[II)I
HSPLandroidx/appcompat/widget/Toolbar;->measureChildCollapseMargins(Landroid/view/View;IIII[I)I
HSPLandroidx/appcompat/widget/Toolbar;->measureChildConstrained(Landroid/view/View;IIIII)V
HSPLandroidx/appcompat/widget/Toolbar;->onLayout(ZIIII)V
HSPLandroidx/appcompat/widget/Toolbar;->onMeasure(II)V
HSPLandroidx/appcompat/widget/Toolbar;->onRtlPropertiesChanged(I)V
HSPLandroidx/appcompat/widget/Toolbar;->setCollapsible(Z)V
HSPLandroidx/appcompat/widget/Toolbar;->setContentInsetsRelative(II)V
HSPLandroidx/appcompat/widget/Toolbar;->setMenu(Landroidx/appcompat/view/menu/MenuBuilder;Landroidx/appcompat/widget/ActionMenuPresenter;)V
HSPLandroidx/appcompat/widget/Toolbar;->setNavigationContentDescription(Ljava/lang/CharSequence;)V
HSPLandroidx/appcompat/widget/Toolbar;->setNavigationIcon(Landroid/graphics/drawable/Drawable;)V
HSPLandroidx/appcompat/widget/Toolbar;->setNavigationOnClickListener(Landroid/view/View$OnClickListener;)V
HSPLandroidx/appcompat/widget/Toolbar;->setPopupTheme(I)V
HSPLandroidx/appcompat/widget/Toolbar;->setSubtitle(Ljava/lang/CharSequence;)V
HSPLandroidx/appcompat/widget/Toolbar;->setSubtitleTextAppearance(Landroid/content/Context;I)V
HSPLandroidx/appcompat/widget/Toolbar;->setTitle(Ljava/lang/CharSequence;)V
HSPLandroidx/appcompat/widget/Toolbar;->setTitleTextAppearance(Landroid/content/Context;I)V
HSPLandroidx/appcompat/widget/Toolbar;->shouldCollapse()Z
HSPLandroidx/appcompat/widget/Toolbar;->shouldLayout(Landroid/view/View;)Z
HSPLandroidx/appcompat/widget/ToolbarWidgetWrapper$1;-><init>(Landroidx/appcompat/widget/ToolbarWidgetWrapper;)V
HSPLandroidx/appcompat/widget/ToolbarWidgetWrapper;-><init>(Landroidx/appcompat/widget/Toolbar;Z)V
HSPLandroidx/appcompat/widget/ToolbarWidgetWrapper;-><init>(Landroidx/appcompat/widget/Toolbar;ZII)V
HSPLandroidx/appcompat/widget/ToolbarWidgetWrapper;->getContext()Landroid/content/Context;
HSPLandroidx/appcompat/widget/ToolbarWidgetWrapper;->getDisplayOptions()I
HSPLandroidx/appcompat/widget/ToolbarWidgetWrapper;->getNavigationMode()I
HSPLandroidx/appcompat/widget/ToolbarWidgetWrapper;->setCollapsible(Z)V
HSPLandroidx/appcompat/widget/ToolbarWidgetWrapper;->setDefaultNavigationContentDescription(I)V
HSPLandroidx/appcompat/widget/ToolbarWidgetWrapper;->setDisplayOptions(I)V
HSPLandroidx/appcompat/widget/ToolbarWidgetWrapper;->setEmbeddedTabView(Landroidx/appcompat/widget/ScrollingTabContainerView;)V
HSPLandroidx/appcompat/widget/ToolbarWidgetWrapper;->setHomeButtonEnabled(Z)V
HSPLandroidx/appcompat/widget/ToolbarWidgetWrapper;->setMenu(Landroid/view/Menu;Landroidx/appcompat/view/menu/MenuPresenter$Callback;)V
HSPLandroidx/appcompat/widget/ToolbarWidgetWrapper;->setMenuPrepared()V
HSPLandroidx/appcompat/widget/ToolbarWidgetWrapper;->setNavigationIcon(Landroid/graphics/drawable/Drawable;)V
HSPLandroidx/appcompat/widget/ToolbarWidgetWrapper;->setTitleInt(Ljava/lang/CharSequence;)V
HSPLandroidx/appcompat/widget/ToolbarWidgetWrapper;->setWindowCallback(Landroid/view/Window$Callback;)V
HSPLandroidx/appcompat/widget/ToolbarWidgetWrapper;->setWindowTitle(Ljava/lang/CharSequence;)V
HSPLandroidx/appcompat/widget/ToolbarWidgetWrapper;->updateNavigationIcon()V
HSPLandroidx/appcompat/widget/TooltipCompat;->setTooltipText(Landroid/view/View;Ljava/lang/CharSequence;)V
HSPLandroidx/appcompat/widget/VectorEnabledTintResources;-><clinit>()V
HSPLandroidx/appcompat/widget/VectorEnabledTintResources;->isCompatVectorFromResourcesEnabled()Z
HSPLandroidx/appcompat/widget/VectorEnabledTintResources;->shouldBeUsed()Z
HSPLandroidx/appcompat/widget/ViewUtils;-><clinit>()V
HSPLandroidx/appcompat/widget/ViewUtils;->isLayoutRtl(Landroid/view/View;)Z
HSPLandroidx/appcompat/widget/ViewUtils;->makeOptionalFitsSystemWindows(Landroid/view/View;)V
Landroidx/appcompat/R$attr;
Landroidx/appcompat/R$bool;
Landroidx/appcompat/R$drawable;
Landroidx/appcompat/R$id;
Landroidx/appcompat/R$layout;
Landroidx/appcompat/R$string;
Landroidx/appcompat/R$style;
Landroidx/appcompat/R$styleable;
Landroidx/appcompat/app/ActionBar$LayoutParams;
Landroidx/appcompat/app/ActionBar;
Landroidx/appcompat/app/ActionBarDrawerToggle$DelegateProvider;
Landroidx/appcompat/app/AppCompatActivity$1;
Landroidx/appcompat/app/AppCompatActivity$2;
Landroidx/appcompat/app/AppCompatActivity;
Landroidx/appcompat/app/AppCompatCallback;
Landroidx/appcompat/app/AppCompatDelegate;
Landroidx/appcompat/app/AppCompatDelegateImpl$2;
Landroidx/appcompat/app/AppCompatDelegateImpl$3;
Landroidx/appcompat/app/AppCompatDelegateImpl$5;
Landroidx/appcompat/app/AppCompatDelegateImpl$ActionMenuPresenterCallback;
Landroidx/appcompat/app/AppCompatDelegateImpl$Api17Impl;
Landroidx/appcompat/app/AppCompatDelegateImpl$AppCompatWindowCallback;
Landroidx/appcompat/app/AppCompatDelegateImpl$PanelFeatureState;
Landroidx/appcompat/app/AppCompatDelegateImpl;
Landroidx/appcompat/app/AppCompatViewInflater;
Landroidx/appcompat/app/ToolbarActionBar;
Landroidx/appcompat/app/WindowDecorActionBar$1;
Landroidx/appcompat/app/WindowDecorActionBar$2;
Landroidx/appcompat/app/WindowDecorActionBar$3;
Landroidx/appcompat/app/WindowDecorActionBar;
Landroidx/appcompat/content/res/AppCompatResources;
Landroidx/appcompat/graphics/drawable/DrawableWrapper;
Landroidx/appcompat/resources/R$drawable;
Landroidx/appcompat/view/ActionBarPolicy;
Landroidx/appcompat/view/ContextThemeWrapper;
Landroidx/appcompat/view/SupportMenuInflater;
Landroidx/appcompat/view/WindowCallbackWrapper;
Landroidx/appcompat/view/menu/ActionMenuItem;
Landroidx/appcompat/view/menu/BaseMenuPresenter;
Landroidx/appcompat/view/menu/MenuBuilder$Callback;
Landroidx/appcompat/view/menu/MenuBuilder$ItemInvoker;
Landroidx/appcompat/view/menu/MenuBuilder;
Landroidx/appcompat/view/menu/MenuPresenter$Callback;
Landroidx/appcompat/view/menu/MenuPresenter;
Landroidx/appcompat/view/menu/MenuView;
Landroidx/appcompat/widget/AbsActionBarView$VisibilityAnimListener;
Landroidx/appcompat/widget/AbsActionBarView;
Landroidx/appcompat/widget/ActionBarBackgroundDrawable;
Landroidx/appcompat/widget/ActionBarContainer;
Landroidx/appcompat/widget/ActionBarContextView;
Landroidx/appcompat/widget/ActionBarOverlayLayout$1;
Landroidx/appcompat/widget/ActionBarOverlayLayout$2;
Landroidx/appcompat/widget/ActionBarOverlayLayout$3;
Landroidx/appcompat/widget/ActionBarOverlayLayout$ActionBarVisibilityCallback;
Landroidx/appcompat/widget/ActionBarOverlayLayout$LayoutParams;
Landroidx/appcompat/widget/ActionBarOverlayLayout;
Landroidx/appcompat/widget/ActionMenuPresenter$OverflowMenuButton$1;
Landroidx/appcompat/widget/ActionMenuPresenter$OverflowMenuButton;
Landroidx/appcompat/widget/ActionMenuPresenter$PopupPresenterCallback;
Landroidx/appcompat/widget/ActionMenuPresenter;
Landroidx/appcompat/widget/ActionMenuView$ActionMenuChildView;
Landroidx/appcompat/widget/ActionMenuView$OnMenuItemClickListener;
Landroidx/appcompat/widget/ActionMenuView;
Landroidx/appcompat/widget/AppCompatBackgroundHelper;
Landroidx/appcompat/widget/AppCompatButton;
Landroidx/appcompat/widget/AppCompatDrawableManager$1;
Landroidx/appcompat/widget/AppCompatDrawableManager;
Landroidx/appcompat/widget/AppCompatEditText;
Landroidx/appcompat/widget/AppCompatEmojiEditTextHelper;
Landroidx/appcompat/widget/AppCompatEmojiTextHelper;
Landroidx/appcompat/widget/AppCompatImageButton;
Landroidx/appcompat/widget/AppCompatImageHelper;
Landroidx/appcompat/widget/AppCompatImageView;
Landroidx/appcompat/widget/AppCompatTextClassifierHelper;
Landroidx/appcompat/widget/AppCompatTextHelper$1;
Landroidx/appcompat/widget/AppCompatTextHelper;
Landroidx/appcompat/widget/AppCompatTextView;
Landroidx/appcompat/widget/AppCompatTextViewAutoSizeHelper$Impl23;
Landroidx/appcompat/widget/AppCompatTextViewAutoSizeHelper$Impl29;
Landroidx/appcompat/widget/AppCompatTextViewAutoSizeHelper$Impl;
Landroidx/appcompat/widget/AppCompatTextViewAutoSizeHelper;
Landroidx/appcompat/widget/ContentFrameLayout$OnAttachListener;
Landroidx/appcompat/widget/ContentFrameLayout;
Landroidx/appcompat/widget/DecorContentParent;
Landroidx/appcompat/widget/DecorToolbar;
Landroidx/appcompat/widget/DrawableUtils;
Landroidx/appcompat/widget/EmojiCompatConfigurationView;
Landroidx/appcompat/widget/ForwardingListener;
Landroidx/appcompat/widget/LinearLayoutCompat;
Landroidx/appcompat/widget/ResourceManagerInternal$ColorFilterLruCache;
Landroidx/appcompat/widget/ResourceManagerInternal$ResourceManagerHooks;
Landroidx/appcompat/widget/ResourceManagerInternal;
Landroidx/appcompat/widget/ResourcesWrapper;
Landroidx/appcompat/widget/RtlSpacingHelper;
Landroidx/appcompat/widget/ThemeUtils;
Landroidx/appcompat/widget/TintContextWrapper;
Landroidx/appcompat/widget/TintResources;
Landroidx/appcompat/widget/TintTypedArray;
Landroidx/appcompat/widget/Toolbar$$ExternalSyntheticLambda0;
Landroidx/appcompat/widget/Toolbar$1;
Landroidx/appcompat/widget/Toolbar$2;
Landroidx/appcompat/widget/Toolbar$ExpandedActionViewMenuPresenter;
Landroidx/appcompat/widget/Toolbar$LayoutParams;
Landroidx/appcompat/widget/Toolbar;
Landroidx/appcompat/widget/ToolbarWidgetWrapper$1;
Landroidx/appcompat/widget/ToolbarWidgetWrapper;
Landroidx/appcompat/widget/TooltipCompat;
Landroidx/appcompat/widget/VectorEnabledTintResources;
Landroidx/appcompat/widget/ViewUtils;
PLandroidx/appcompat/app/ActionBar;->onDestroy()V
PLandroidx/appcompat/app/AppCompatActivity;->dispatchKeyEvent(Landroid/view/KeyEvent;)Z
PLandroidx/appcompat/app/AppCompatActivity;->getSupportActionBar()Landroidx/appcompat/app/ActionBar;
PLandroidx/appcompat/app/AppCompatActivity;->onDestroy()V
PLandroidx/appcompat/app/AppCompatActivity;->onKeyDown(ILandroid/view/KeyEvent;)Z
PLandroidx/appcompat/app/AppCompatActivity;->onStop()V
PLandroidx/appcompat/app/AppCompatActivity;->performMenuItemShortcut(Landroid/view/KeyEvent;)Z
PLandroidx/appcompat/app/AppCompatDelegate;->removeActivityDelegate(Landroidx/appcompat/app/AppCompatDelegate;)V
PLandroidx/appcompat/app/AppCompatDelegateImpl$5;->onDetachedFromWindow()V
PLandroidx/appcompat/app/AppCompatDelegateImpl$ActionMenuPresenterCallback;->onCloseMenu(Landroidx/appcompat/view/menu/MenuBuilder;Z)V
PLandroidx/appcompat/app/AppCompatDelegateImpl$AppCompatWindowCallback;->dispatchKeyEvent(Landroid/view/KeyEvent;)Z
PLandroidx/appcompat/app/AppCompatDelegateImpl;->checkCloseActionMenu(Landroidx/appcompat/view/menu/MenuBuilder;)V
PLandroidx/appcompat/app/AppCompatDelegateImpl;->cleanupAutoManagers()V
PLandroidx/appcompat/app/AppCompatDelegateImpl;->dismissPopups()V
PLandroidx/appcompat/app/AppCompatDelegateImpl;->dispatchKeyEvent(Landroid/view/KeyEvent;)Z
PLandroidx/appcompat/app/AppCompatDelegateImpl;->endOnGoingFadeAnimation()V
PLandroidx/appcompat/app/AppCompatDelegateImpl;->onBackPressed()Z
PLandroidx/appcompat/app/AppCompatDelegateImpl;->onDestroy()V
PLandroidx/appcompat/app/AppCompatDelegateImpl;->onKeyDown(ILandroid/view/KeyEvent;)Z
PLandroidx/appcompat/app/AppCompatDelegateImpl;->onKeyUp(ILandroid/view/KeyEvent;)Z
PLandroidx/appcompat/app/AppCompatDelegateImpl;->onStop()V
PLandroidx/appcompat/app/WindowDecorActionBar;->collapseActionView()Z
PLandroidx/appcompat/view/WindowCallbackWrapper;->dispatchKeyEvent(Landroid/view/KeyEvent;)Z
PLandroidx/appcompat/view/WindowCallbackWrapper;->onDetachedFromWindow()V
PLandroidx/appcompat/view/menu/BaseMenuPresenter;->onCloseMenu(Landroidx/appcompat/view/menu/MenuBuilder;Z)V
PLandroidx/appcompat/view/menu/MenuBuilder;->close()V
PLandroidx/appcompat/view/menu/MenuBuilder;->close(Z)V
PLandroidx/appcompat/widget/ActionBarContainer;->verifyDrawable(Landroid/graphics/drawable/Drawable;)Z
PLandroidx/appcompat/widget/ActionBarContextView;->onDetachedFromWindow()V
PLandroidx/appcompat/widget/ActionBarOverlayLayout;->dismissPopups()V
PLandroidx/appcompat/widget/ActionBarOverlayLayout;->haltActionBarHideOffsetAnimations()V
PLandroidx/appcompat/widget/ActionBarOverlayLayout;->onDetachedFromWindow()V
PLandroidx/appcompat/widget/ActionMenuPresenter;->dismissPopupMenus()Z
PLandroidx/appcompat/widget/ActionMenuPresenter;->hideOverflowMenu()Z
PLandroidx/appcompat/widget/ActionMenuPresenter;->hideSubMenus()Z
PLandroidx/appcompat/widget/ActionMenuPresenter;->onCloseMenu(Landroidx/appcompat/view/menu/MenuBuilder;Z)V
PLandroidx/appcompat/widget/ActionMenuView;->dismissPopupMenus()V
PLandroidx/appcompat/widget/ActionMenuView;->onDetachedFromWindow()V
PLandroidx/appcompat/widget/ContentFrameLayout;->onDetachedFromWindow()V
PLandroidx/appcompat/widget/Toolbar$ExpandedActionViewMenuPresenter;->onCloseMenu(Landroidx/appcompat/view/menu/MenuBuilder;Z)V
PLandroidx/appcompat/widget/Toolbar;->dismissPopupMenus()V
PLandroidx/appcompat/widget/Toolbar;->hasExpandedActionView()Z
PLandroidx/appcompat/widget/Toolbar;->onDetachedFromWindow()V
PLandroidx/appcompat/widget/ToolbarWidgetWrapper;->dismissPopupMenus()V
PLandroidx/appcompat/widget/ToolbarWidgetWrapper;->hasExpandedActionView()Z

HSPLandroidx/recyclerview/R$styleable;-><clinit>()V
HSPLandroidx/recyclerview/widget/AdapterHelper$UpdateOp;-><init>(IIILjava/lang/Object;)V
HSPLandroidx/recyclerview/widget/AdapterHelper;-><init>(Landroidx/recyclerview/widget/AdapterHelper$Callback;)V
HSPLandroidx/recyclerview/widget/AdapterHelper;-><init>(Landroidx/recyclerview/widget/AdapterHelper$Callback;Z)V
HSPLandroidx/recyclerview/widget/AdapterHelper;->applyAdd(Landroidx/recyclerview/widget/AdapterHelper$UpdateOp;)V
HSPLandroidx/recyclerview/widget/AdapterHelper;->consumePostponedUpdates()V
HSPLandroidx/recyclerview/widget/AdapterHelper;->consumeUpdatesInOnePass()V
HSPLandroidx/recyclerview/widget/AdapterHelper;->findPositionOffset(I)I
HSPLandroidx/recyclerview/widget/AdapterHelper;->findPositionOffset(II)I
HSPLandroidx/recyclerview/widget/AdapterHelper;->hasPendingUpdates()Z
HSPLandroidx/recyclerview/widget/AdapterHelper;->obtainUpdateOp(IIILjava/lang/Object;)Landroidx/recyclerview/widget/AdapterHelper$UpdateOp;
HSPLandroidx/recyclerview/widget/AdapterHelper;->onItemRangeInserted(II)Z
HSPLandroidx/recyclerview/widget/AdapterHelper;->postponeAndUpdateViewHolders(Landroidx/recyclerview/widget/AdapterHelper$UpdateOp;)V
HSPLandroidx/recyclerview/widget/AdapterHelper;->preProcess()V
HSPLandroidx/recyclerview/widget/AdapterHelper;->recycleUpdateOp(Landroidx/recyclerview/widget/AdapterHelper$UpdateOp;)V
HSPLandroidx/recyclerview/widget/AdapterHelper;->recycleUpdateOpsAndClearList(Ljava/util/List;)V
HSPLandroidx/recyclerview/widget/AdapterHelper;->reset()V
HSPLandroidx/recyclerview/widget/AdapterListUpdateCallback;-><init>(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V
HSPLandroidx/recyclerview/widget/AdapterListUpdateCallback;->onInserted(II)V
HSPLandroidx/recyclerview/widget/AsyncDifferConfig$Builder;-><clinit>()V
HSPLandroidx/recyclerview/widget/AsyncDifferConfig$Builder;-><init>(Landroidx/recyclerview/widget/DiffUtil$ItemCallback;)V
HSPLandroidx/recyclerview/widget/AsyncDifferConfig$Builder;->build()Landroidx/recyclerview/widget/AsyncDifferConfig;
HSPLandroidx/recyclerview/widget/AsyncDifferConfig$Builder;->setBackgroundThreadExecutor(Ljava/util/concurrent/Executor;)Landroidx/recyclerview/widget/AsyncDifferConfig$Builder;
HSPLandroidx/recyclerview/widget/AsyncDifferConfig;-><init>(Ljava/util/concurrent/Executor;Ljava/util/concurrent/Executor;Landroidx/recyclerview/widget/DiffUtil$ItemCallback;)V
HSPLandroidx/recyclerview/widget/AsyncDifferConfig;->getMainThreadExecutor()Ljava/util/concurrent/Executor;
HSPLandroidx/recyclerview/widget/AsyncListDiffer$MainThreadExecutor;-><init>()V
HSPLandroidx/recyclerview/widget/AsyncListDiffer;-><clinit>()V
HSPLandroidx/recyclerview/widget/AsyncListDiffer;-><init>(Landroidx/recyclerview/widget/ListUpdateCallback;Landroidx/recyclerview/widget/AsyncDifferConfig;)V
HSPLandroidx/recyclerview/widget/AsyncListDiffer;->addListListener(Landroidx/recyclerview/widget/AsyncListDiffer$ListListener;)V
HSPLandroidx/recyclerview/widget/AsyncListDiffer;->getCurrentList()Ljava/util/List;
HSPLandroidx/recyclerview/widget/AsyncListDiffer;->onCurrentListChanged(Ljava/util/List;Ljava/lang/Runnable;)V
HSPLandroidx/recyclerview/widget/AsyncListDiffer;->submitList(Ljava/util/List;)V
HSPLandroidx/recyclerview/widget/AsyncListDiffer;->submitList(Ljava/util/List;Ljava/lang/Runnable;)V
HSPLandroidx/recyclerview/widget/ChildHelper$Bucket;-><init>()V
HSPLandroidx/recyclerview/widget/ChildHelper$Bucket;->clear(I)V
HSPLandroidx/recyclerview/widget/ChildHelper$Bucket;->countOnesBefore(I)I
HSPLandroidx/recyclerview/widget/ChildHelper$Bucket;->get(I)Z
HSPLandroidx/recyclerview/widget/ChildHelper$Bucket;->insert(IZ)V
HSPLandroidx/recyclerview/widget/ChildHelper$Bucket;->remove(I)Z
HSPLandroidx/recyclerview/widget/ChildHelper$Bucket;->reset()V
HSPLandroidx/recyclerview/widget/ChildHelper;-><init>(Landroidx/recyclerview/widget/ChildHelper$Callback;)V
HSPLandroidx/recyclerview/widget/ChildHelper;->addView(Landroid/view/View;IZ)V
HSPLandroidx/recyclerview/widget/ChildHelper;->findHiddenNonRemovedView(I)Landroid/view/View;
HSPLandroidx/recyclerview/widget/ChildHelper;->getChildAt(I)Landroid/view/View;
HSPLandroidx/recyclerview/widget/ChildHelper;->getChildCount()I
HSPLandroidx/recyclerview/widget/ChildHelper;->getOffset(I)I
HSPLandroidx/recyclerview/widget/ChildHelper;->getUnfilteredChildAt(I)Landroid/view/View;
HSPLandroidx/recyclerview/widget/ChildHelper;->getUnfilteredChildCount()I
HSPLandroidx/recyclerview/widget/ChildHelper;->isHidden(Landroid/view/View;)Z
HSPLandroidx/recyclerview/widget/ChildHelper;->removeAllViewsUnfiltered()V
HSPLandroidx/recyclerview/widget/ChildHelper;->removeViewAt(I)V
HSPLandroidx/recyclerview/widget/ChildHelper;->removeViewIfHidden(Landroid/view/View;)Z
HSPLandroidx/recyclerview/widget/DefaultItemAnimator$3;-><init>(Landroidx/recyclerview/widget/DefaultItemAnimator;Ljava/util/ArrayList;)V
HSPLandroidx/recyclerview/widget/DefaultItemAnimator$3;->run()V
HSPLandroidx/recyclerview/widget/DefaultItemAnimator$5;-><init>(Landroidx/recyclerview/widget/DefaultItemAnimator;Landroidx/recyclerview/widget/RecyclerView$ViewHolder;Landroid/view/View;Landroid/view/ViewPropertyAnimator;)V
HSPLandroidx/recyclerview/widget/DefaultItemAnimator$5;->onAnimationEnd(Landroid/animation/Animator;)V
HSPLandroidx/recyclerview/widget/DefaultItemAnimator$5;->onAnimationStart(Landroid/animation/Animator;)V
HSPLandroidx/recyclerview/widget/DefaultItemAnimator;-><init>()V
HSPLandroidx/recyclerview/widget/DefaultItemAnimator;->animateAdd(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)Z
HSPLandroidx/recyclerview/widget/DefaultItemAnimator;->animateAddImpl(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)V
HSPLandroidx/recyclerview/widget/DefaultItemAnimator;->dispatchFinishedWhenDone()V
HSPLandroidx/recyclerview/widget/DefaultItemAnimator;->endAnimation(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)V
HSPLandroidx/recyclerview/widget/DefaultItemAnimator;->endAnimations()V
HSPLandroidx/recyclerview/widget/DefaultItemAnimator;->endChangeAnimation(Ljava/util/List;Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)V
HSPLandroidx/recyclerview/widget/DefaultItemAnimator;->isRunning()Z
HSPLandroidx/recyclerview/widget/DefaultItemAnimator;->resetAnimation(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)V
HSPLandroidx/recyclerview/widget/DefaultItemAnimator;->runPendingAnimations()V
HSPLandroidx/recyclerview/widget/DiffUtil$ItemCallback;-><init>()V
HSPLandroidx/recyclerview/widget/GapWorker$1;-><init>()V
HSPLandroidx/recyclerview/widget/GapWorker$LayoutPrefetchRegistryImpl;-><init>()V
HSPLandroidx/recyclerview/widget/GapWorker$LayoutPrefetchRegistryImpl;->addPosition(II)V
HSPLandroidx/recyclerview/widget/GapWorker$LayoutPrefetchRegistryImpl;->clearPrefetchPositions()V
HSPLandroidx/recyclerview/widget/GapWorker$LayoutPrefetchRegistryImpl;->collectPrefetchPositionsFromView(Landroidx/recyclerview/widget/RecyclerView;Z)V
HSPLandroidx/recyclerview/widget/GapWorker$LayoutPrefetchRegistryImpl;->lastPrefetchIncludedPosition(I)Z
HSPLandroidx/recyclerview/widget/GapWorker$LayoutPrefetchRegistryImpl;->setPrefetchVector(II)V
HSPLandroidx/recyclerview/widget/GapWorker$Task;-><init>()V
HSPLandroidx/recyclerview/widget/GapWorker$Task;->clear()V
HSPLandroidx/recyclerview/widget/GapWorker;-><clinit>()V
HSPLandroidx/recyclerview/widget/GapWorker;-><init>()V
HSPLandroidx/recyclerview/widget/GapWorker;->add(Landroidx/recyclerview/widget/RecyclerView;)V
HSPLandroidx/recyclerview/widget/GapWorker;->buildTaskList()V
HSPLandroidx/recyclerview/widget/GapWorker;->flushTaskWithDeadline(Landroidx/recyclerview/widget/GapWorker$Task;J)V
HSPLandroidx/recyclerview/widget/GapWorker;->flushTasksWithDeadline(J)V
HSPLandroidx/recyclerview/widget/GapWorker;->isPrefetchPositionAttached(Landroidx/recyclerview/widget/RecyclerView;I)Z
HSPLandroidx/recyclerview/widget/GapWorker;->postFromTraversal(Landroidx/recyclerview/widget/RecyclerView;II)V
HSPLandroidx/recyclerview/widget/GapWorker;->prefetch(J)V
HSPLandroidx/recyclerview/widget/GapWorker;->prefetchPositionWithDeadline(Landroidx/recyclerview/widget/RecyclerView;IJ)Landroidx/recyclerview/widget/RecyclerView$ViewHolder;
HSPLandroidx/recyclerview/widget/GapWorker;->run()V
HSPLandroidx/recyclerview/widget/LinearLayoutManager$AnchorInfo;-><init>()V
HSPLandroidx/recyclerview/widget/LinearLayoutManager$AnchorInfo;->assignCoordinateFromPadding()V
HSPLandroidx/recyclerview/widget/LinearLayoutManager$AnchorInfo;->reset()V
HSPLandroidx/recyclerview/widget/LinearLayoutManager$LayoutChunkResult;-><init>()V
HSPLandroidx/recyclerview/widget/LinearLayoutManager$LayoutChunkResult;->resetInternal()V
HSPLandroidx/recyclerview/widget/LinearLayoutManager$LayoutState;-><init>()V
HSPLandroidx/recyclerview/widget/LinearLayoutManager$LayoutState;->hasMore(Landroidx/recyclerview/widget/RecyclerView$State;)Z
HSPLandroidx/recyclerview/widget/LinearLayoutManager$LayoutState;->next(Landroidx/recyclerview/widget/RecyclerView$Recycler;)Landroid/view/View;
HSPLandroidx/recyclerview/widget/LinearLayoutManager;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;II)V
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->assertNotInLayoutOrScroll(Ljava/lang/String;)V
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->calculateExtraLayoutSpace(Landroidx/recyclerview/widget/RecyclerView$State;[I)V
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->canScrollHorizontally()Z
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->canScrollVertically()Z
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->collectAdjacentPrefetchPositions(IILandroidx/recyclerview/widget/RecyclerView$State;Landroidx/recyclerview/widget/RecyclerView$LayoutManager$LayoutPrefetchRegistry;)V
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->collectPrefetchPositionsForLayoutState(Landroidx/recyclerview/widget/RecyclerView$State;Landroidx/recyclerview/widget/LinearLayoutManager$LayoutState;Landroidx/recyclerview/widget/RecyclerView$LayoutManager$LayoutPrefetchRegistry;)V
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->computeScrollExtent(Landroidx/recyclerview/widget/RecyclerView$State;)I
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->computeScrollOffset(Landroidx/recyclerview/widget/RecyclerView$State;)I
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->computeScrollRange(Landroidx/recyclerview/widget/RecyclerView$State;)I
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->computeVerticalScrollExtent(Landroidx/recyclerview/widget/RecyclerView$State;)I
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->computeVerticalScrollOffset(Landroidx/recyclerview/widget/RecyclerView$State;)I
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->computeVerticalScrollRange(Landroidx/recyclerview/widget/RecyclerView$State;)I
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->createLayoutState()Landroidx/recyclerview/widget/LinearLayoutManager$LayoutState;
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->ensureLayoutState()V
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->fill(Landroidx/recyclerview/widget/RecyclerView$Recycler;Landroidx/recyclerview/widget/LinearLayoutManager$LayoutState;Landroidx/recyclerview/widget/RecyclerView$State;Z)I
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->findFirstVisibleChildClosestToEnd(ZZ)Landroid/view/View;
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->findFirstVisibleChildClosestToStart(ZZ)Landroid/view/View;
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->findFirstVisibleItemPosition()I
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->findLastVisibleItemPosition()I
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->findOneVisibleChild(IIZZ)Landroid/view/View;
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->fixLayoutEndGap(ILandroidx/recyclerview/widget/RecyclerView$Recycler;Landroidx/recyclerview/widget/RecyclerView$State;Z)I
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->fixLayoutStartGap(ILandroidx/recyclerview/widget/RecyclerView$Recycler;Landroidx/recyclerview/widget/RecyclerView$State;Z)I
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->getChildClosestToEnd()Landroid/view/View;
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->getExtraLayoutSpace(Landroidx/recyclerview/widget/RecyclerView$State;)I
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->isAutoMeasureEnabled()Z
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->isLayoutRTL()Z
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->layoutChunk(Landroidx/recyclerview/widget/RecyclerView$Recycler;Landroidx/recyclerview/widget/RecyclerView$State;Landroidx/recyclerview/widget/LinearLayoutManager$LayoutState;Landroidx/recyclerview/widget/LinearLayoutManager$LayoutChunkResult;)V
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->layoutForPredictiveAnimations(Landroidx/recyclerview/widget/RecyclerView$Recycler;Landroidx/recyclerview/widget/RecyclerView$State;II)V
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->onAnchorReady(Landroidx/recyclerview/widget/RecyclerView$Recycler;Landroidx/recyclerview/widget/RecyclerView$State;Landroidx/recyclerview/widget/LinearLayoutManager$AnchorInfo;I)V
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->onInitializeAccessibilityEvent(Landroid/view/accessibility/AccessibilityEvent;)V
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->onLayoutChildren(Landroidx/recyclerview/widget/RecyclerView$Recycler;Landroidx/recyclerview/widget/RecyclerView$State;)V
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->onLayoutCompleted(Landroidx/recyclerview/widget/RecyclerView$State;)V
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->recycleByLayoutState(Landroidx/recyclerview/widget/RecyclerView$Recycler;Landroidx/recyclerview/widget/LinearLayoutManager$LayoutState;)V
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->recycleChildren(Landroidx/recyclerview/widget/RecyclerView$Recycler;II)V
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->recycleViewsFromStart(Landroidx/recyclerview/widget/RecyclerView$Recycler;II)V
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->resolveIsInfinite()Z
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->resolveShouldLayoutReverse()V
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->scrollBy(ILandroidx/recyclerview/widget/RecyclerView$Recycler;Landroidx/recyclerview/widget/RecyclerView$State;)I
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->scrollVerticallyBy(ILandroidx/recyclerview/widget/RecyclerView$Recycler;Landroidx/recyclerview/widget/RecyclerView$State;)I
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->setOrientation(I)V
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->setReverseLayout(Z)V
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->setStackFromEnd(Z)V
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->supportsPredictiveItemAnimations()Z
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->updateAnchorFromChildren(Landroidx/recyclerview/widget/RecyclerView$Recycler;Landroidx/recyclerview/widget/RecyclerView$State;Landroidx/recyclerview/widget/LinearLayoutManager$AnchorInfo;)Z
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->updateAnchorFromPendingData(Landroidx/recyclerview/widget/RecyclerView$State;Landroidx/recyclerview/widget/LinearLayoutManager$AnchorInfo;)Z
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->updateAnchorInfoForLayout(Landroidx/recyclerview/widget/RecyclerView$Recycler;Landroidx/recyclerview/widget/RecyclerView$State;Landroidx/recyclerview/widget/LinearLayoutManager$AnchorInfo;)V
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->updateLayoutState(IIZLandroidx/recyclerview/widget/RecyclerView$State;)V
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->updateLayoutStateToFillEnd(II)V
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->updateLayoutStateToFillEnd(Landroidx/recyclerview/widget/LinearLayoutManager$AnchorInfo;)V
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->updateLayoutStateToFillStart(II)V
HSPLandroidx/recyclerview/widget/LinearLayoutManager;->updateLayoutStateToFillStart(Landroidx/recyclerview/widget/LinearLayoutManager$AnchorInfo;)V
HSPLandroidx/recyclerview/widget/ListAdapter$1;-><init>(Landroidx/recyclerview/widget/ListAdapter;)V
HSPLandroidx/recyclerview/widget/ListAdapter$1;->onCurrentListChanged(Ljava/util/List;Ljava/util/List;)V
HSPLandroidx/recyclerview/widget/ListAdapter;-><init>(Landroidx/recyclerview/widget/AsyncDifferConfig;)V
HSPLandroidx/recyclerview/widget/ListAdapter;->getItem(I)Ljava/lang/Object;
HSPLandroidx/recyclerview/widget/ListAdapter;->getItemCount()I
HSPLandroidx/recyclerview/widget/ListAdapter;->onCurrentListChanged(Ljava/util/List;Ljava/util/List;)V
HSPLandroidx/recyclerview/widget/ListAdapter;->submitList(Ljava/util/List;)V
HSPLandroidx/recyclerview/widget/OpReorderer;-><init>(Landroidx/recyclerview/widget/OpReorderer$Callback;)V
HSPLandroidx/recyclerview/widget/OpReorderer;->getLastMoveOutOfOrder(Ljava/util/List;)I
HSPLandroidx/recyclerview/widget/OpReorderer;->reorderOps(Ljava/util/List;)V
HSPLandroidx/recyclerview/widget/OrientationHelper$2;-><init>(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V
HSPLandroidx/recyclerview/widget/OrientationHelper$2;->getDecoratedEnd(Landroid/view/View;)I
HSPLandroidx/recyclerview/widget/OrientationHelper$2;->getDecoratedMeasurement(Landroid/view/View;)I
HSPLandroidx/recyclerview/widget/OrientationHelper$2;->getDecoratedMeasurementInOther(Landroid/view/View;)I
HSPLandroidx/recyclerview/widget/OrientationHelper$2;->getDecoratedStart(Landroid/view/View;)I
HSPLandroidx/recyclerview/widget/OrientationHelper$2;->getEndAfterPadding()I
HSPLandroidx/recyclerview/widget/OrientationHelper$2;->getEndPadding()I
HSPLandroidx/recyclerview/widget/OrientationHelper$2;->getMode()I
HSPLandroidx/recyclerview/widget/OrientationHelper$2;->getStartAfterPadding()I
HSPLandroidx/recyclerview/widget/OrientationHelper$2;->getTotalSpace()I
HSPLandroidx/recyclerview/widget/OrientationHelper$2;->getTransformedEndWithDecoration(Landroid/view/View;)I
HSPLandroidx/recyclerview/widget/OrientationHelper$2;->offsetChildren(I)V
HSPLandroidx/recyclerview/widget/OrientationHelper;-><init>(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V
HSPLandroidx/recyclerview/widget/OrientationHelper;-><init>(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;Landroidx/recyclerview/widget/OrientationHelper$1;)V
HSPLandroidx/recyclerview/widget/OrientationHelper;->createOrientationHelper(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;I)Landroidx/recyclerview/widget/OrientationHelper;
HSPLandroidx/recyclerview/widget/OrientationHelper;->createVerticalHelper(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)Landroidx/recyclerview/widget/OrientationHelper;
HSPLandroidx/recyclerview/widget/OrientationHelper;->onLayoutComplete()V
HSPLandroidx/recyclerview/widget/RecyclerView$1;-><init>(Landroidx/recyclerview/widget/RecyclerView;)V
HSPLandroidx/recyclerview/widget/RecyclerView$2;-><init>(Landroidx/recyclerview/widget/RecyclerView;)V
HSPLandroidx/recyclerview/widget/RecyclerView$2;->run()V
HSPLandroidx/recyclerview/widget/RecyclerView$3;-><init>()V
HSPLandroidx/recyclerview/widget/RecyclerView$4;-><init>(Landroidx/recyclerview/widget/RecyclerView;)V
HSPLandroidx/recyclerview/widget/RecyclerView$4;->processAppeared(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;Landroidx/recyclerview/widget/RecyclerView$ItemAnimator$ItemHolderInfo;Landroidx/recyclerview/widget/RecyclerView$ItemAnimator$ItemHolderInfo;)V
HSPLandroidx/recyclerview/widget/RecyclerView$5;-><init>(Landroidx/recyclerview/widget/RecyclerView;)V
HSPLandroidx/recyclerview/widget/RecyclerView$5;->addView(Landroid/view/View;I)V
HSPLandroidx/recyclerview/widget/RecyclerView$5;->getChildAt(I)Landroid/view/View;
HSPLandroidx/recyclerview/widget/RecyclerView$5;->getChildCount()I
HSPLandroidx/recyclerview/widget/RecyclerView$5;->indexOfChild(Landroid/view/View;)I
HSPLandroidx/recyclerview/widget/RecyclerView$5;->removeAllViews()V
HSPLandroidx/recyclerview/widget/RecyclerView$5;->removeViewAt(I)V
HSPLandroidx/recyclerview/widget/RecyclerView$6;-><init>(Landroidx/recyclerview/widget/RecyclerView;)V
HSPLandroidx/recyclerview/widget/RecyclerView$6;->dispatchUpdate(Landroidx/recyclerview/widget/AdapterHelper$UpdateOp;)V
HSPLandroidx/recyclerview/widget/RecyclerView$6;->offsetPositionsForAdd(II)V
HSPLandroidx/recyclerview/widget/RecyclerView$6;->onDispatchSecondPass(Landroidx/recyclerview/widget/AdapterHelper$UpdateOp;)V
HSPLandroidx/recyclerview/widget/RecyclerView$Adapter$StateRestorationPolicy;-><clinit>()V
HSPLandroidx/recyclerview/widget/RecyclerView$Adapter$StateRestorationPolicy;-><init>(Ljava/lang/String;I)V
HSPLandroidx/recyclerview/widget/RecyclerView$Adapter;-><init>()V
HSPLandroidx/recyclerview/widget/RecyclerView$Adapter;->bindViewHolder(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;I)V
HSPLandroidx/recyclerview/widget/RecyclerView$Adapter;->createViewHolder(Landroid/view/ViewGroup;I)Landroidx/recyclerview/widget/RecyclerView$ViewHolder;
HSPLandroidx/recyclerview/widget/RecyclerView$Adapter;->getItemViewType(I)I
HSPLandroidx/recyclerview/widget/RecyclerView$Adapter;->hasStableIds()Z
HSPLandroidx/recyclerview/widget/RecyclerView$Adapter;->notifyItemRangeInserted(II)V
HSPLandroidx/recyclerview/widget/RecyclerView$Adapter;->onAttachedToRecyclerView(Landroidx/recyclerview/widget/RecyclerView;)V
HSPLandroidx/recyclerview/widget/RecyclerView$Adapter;->onBindViewHolder(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;ILjava/util/List;)V
HSPLandroidx/recyclerview/widget/RecyclerView$Adapter;->onViewAttachedToWindow(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)V
HSPLandroidx/recyclerview/widget/RecyclerView$Adapter;->onViewDetachedFromWindow(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)V
HSPLandroidx/recyclerview/widget/RecyclerView$Adapter;->onViewRecycled(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)V
HSPLandroidx/recyclerview/widget/RecyclerView$Adapter;->registerAdapterDataObserver(Landroidx/recyclerview/widget/RecyclerView$AdapterDataObserver;)V
HSPLandroidx/recyclerview/widget/RecyclerView$AdapterDataObservable;-><init>()V
HSPLandroidx/recyclerview/widget/RecyclerView$AdapterDataObservable;->notifyItemRangeInserted(II)V
HSPLandroidx/recyclerview/widget/RecyclerView$AdapterDataObserver;-><init>()V
HSPLandroidx/recyclerview/widget/RecyclerView$EdgeEffectFactory;-><init>()V
HSPLandroidx/recyclerview/widget/RecyclerView$ItemAnimator$ItemHolderInfo;-><init>()V
HSPLandroidx/recyclerview/widget/RecyclerView$ItemAnimator$ItemHolderInfo;->setFrom(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)Landroidx/recyclerview/widget/RecyclerView$ItemAnimator$ItemHolderInfo;
HSPLandroidx/recyclerview/widget/RecyclerView$ItemAnimator$ItemHolderInfo;->setFrom(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;I)Landroidx/recyclerview/widget/RecyclerView$ItemAnimator$ItemHolderInfo;
HSPLandroidx/recyclerview/widget/RecyclerView$ItemAnimator;-><init>()V
HSPLandroidx/recyclerview/widget/RecyclerView$ItemAnimator;->dispatchAnimationFinished(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)V
HSPLandroidx/recyclerview/widget/RecyclerView$ItemAnimator;->dispatchAnimationsFinished()V
HSPLandroidx/recyclerview/widget/RecyclerView$ItemAnimator;->getAddDuration()J
HSPLandroidx/recyclerview/widget/RecyclerView$ItemAnimator;->obtainHolderInfo()Landroidx/recyclerview/widget/RecyclerView$ItemAnimator$ItemHolderInfo;
HSPLandroidx/recyclerview/widget/RecyclerView$ItemAnimator;->onAnimationFinished(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)V
HSPLandroidx/recyclerview/widget/RecyclerView$ItemAnimator;->recordPostLayoutInformation(Landroidx/recyclerview/widget/RecyclerView$State;Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)Landroidx/recyclerview/widget/RecyclerView$ItemAnimator$ItemHolderInfo;
HSPLandroidx/recyclerview/widget/RecyclerView$ItemAnimator;->setListener(Landroidx/recyclerview/widget/RecyclerView$ItemAnimator$ItemAnimatorListener;)V
HSPLandroidx/recyclerview/widget/RecyclerView$ItemAnimatorRestoreListener;-><init>(Landroidx/recyclerview/widget/RecyclerView;)V
HSPLandroidx/recyclerview/widget/RecyclerView$ItemAnimatorRestoreListener;->onAnimationFinished(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager$1;-><init>(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager$2;-><init>(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager$2;->getChildAt(I)Landroid/view/View;
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager$2;->getChildEnd(Landroid/view/View;)I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager$2;->getChildStart(Landroid/view/View;)I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager$2;->getParentEnd()I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager$2;->getParentStart()I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager$Properties;-><init>()V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;-><init>()V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->addView(Landroid/view/View;)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->addView(Landroid/view/View;I)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->addViewInt(Landroid/view/View;IZ)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->assertNotInLayoutOrScroll(Ljava/lang/String;)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->checkLayoutParams(Landroidx/recyclerview/widget/RecyclerView$LayoutParams;)Z
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->chooseSize(III)I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->detachAndScrapAttachedViews(Landroidx/recyclerview/widget/RecyclerView$Recycler;)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->dispatchAttachedToWindow(Landroidx/recyclerview/widget/RecyclerView;)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->generateLayoutParams(Landroid/content/Context;Landroid/util/AttributeSet;)Landroidx/recyclerview/widget/RecyclerView$LayoutParams;
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->getBottomDecorationHeight(Landroid/view/View;)I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->getChildAt(I)Landroid/view/View;
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->getChildCount()I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->getChildMeasureSpec(IIIIZ)I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->getColumnCountForAccessibility(Landroidx/recyclerview/widget/RecyclerView$Recycler;Landroidx/recyclerview/widget/RecyclerView$State;)I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->getDecoratedBottom(Landroid/view/View;)I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->getDecoratedMeasuredHeight(Landroid/view/View;)I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->getDecoratedMeasuredWidth(Landroid/view/View;)I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->getDecoratedTop(Landroid/view/View;)I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->getFocusedChild()Landroid/view/View;
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->getHeight()I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->getHeightMode()I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->getLayoutDirection()I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->getPaddingBottom()I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->getPaddingLeft()I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->getPaddingRight()I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->getPaddingTop()I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->getPosition(Landroid/view/View;)I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->getProperties(Landroid/content/Context;Landroid/util/AttributeSet;II)Landroidx/recyclerview/widget/RecyclerView$LayoutManager$Properties;
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->getRowCountForAccessibility(Landroidx/recyclerview/widget/RecyclerView$Recycler;Landroidx/recyclerview/widget/RecyclerView$State;)I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->getSelectionModeForAccessibility(Landroidx/recyclerview/widget/RecyclerView$Recycler;Landroidx/recyclerview/widget/RecyclerView$State;)I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->getTopDecorationHeight(Landroid/view/View;)I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->getTransformedBoundingBox(Landroid/view/View;ZLandroid/graphics/Rect;)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->getWidth()I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->getWidthMode()I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->isItemPrefetchEnabled()Z
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->isLayoutHierarchical(Landroidx/recyclerview/widget/RecyclerView$Recycler;Landroidx/recyclerview/widget/RecyclerView$State;)Z
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->layoutDecoratedWithMargins(Landroid/view/View;IIII)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->measureChildWithMargins(Landroid/view/View;II)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->offsetChildrenVertical(I)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->onAdapterChanged(Landroidx/recyclerview/widget/RecyclerView$Adapter;Landroidx/recyclerview/widget/RecyclerView$Adapter;)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->onAttachedToWindow(Landroidx/recyclerview/widget/RecyclerView;)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->onInitializeAccessibilityEvent(Landroid/view/accessibility/AccessibilityEvent;)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->onInitializeAccessibilityEvent(Landroidx/recyclerview/widget/RecyclerView$Recycler;Landroidx/recyclerview/widget/RecyclerView$State;Landroid/view/accessibility/AccessibilityEvent;)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->onInitializeAccessibilityNodeInfo(Landroidx/core/view/accessibility/AccessibilityNodeInfoCompat;)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->onInitializeAccessibilityNodeInfo(Landroidx/recyclerview/widget/RecyclerView$Recycler;Landroidx/recyclerview/widget/RecyclerView$State;Landroidx/core/view/accessibility/AccessibilityNodeInfoCompat;)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->onInitializeAccessibilityNodeInfoForItem(Landroid/view/View;Landroidx/core/view/accessibility/AccessibilityNodeInfoCompat;)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->onInitializeAccessibilityNodeInfoForItem(Landroidx/recyclerview/widget/RecyclerView$Recycler;Landroidx/recyclerview/widget/RecyclerView$State;Landroid/view/View;Landroidx/core/view/accessibility/AccessibilityNodeInfoCompat;)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->onItemsAdded(Landroidx/recyclerview/widget/RecyclerView;II)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->onLayoutCompleted(Landroidx/recyclerview/widget/RecyclerView$State;)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->onMeasure(Landroidx/recyclerview/widget/RecyclerView$Recycler;Landroidx/recyclerview/widget/RecyclerView$State;II)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->onScrollStateChanged(I)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->removeAndRecycleAllViews(Landroidx/recyclerview/widget/RecyclerView$Recycler;)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->removeAndRecycleScrapInt(Landroidx/recyclerview/widget/RecyclerView$Recycler;)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->removeAndRecycleViewAt(ILandroidx/recyclerview/widget/RecyclerView$Recycler;)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->removeViewAt(I)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->requestLayout()V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->setExactMeasureSpecsFrom(Landroidx/recyclerview/widget/RecyclerView;)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->setMeasureSpecs(II)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->setRecyclerView(Landroidx/recyclerview/widget/RecyclerView;)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->shouldMeasureChild(Landroid/view/View;IILandroidx/recyclerview/widget/RecyclerView$LayoutParams;)Z
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->stopSmoothScroller()V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutParams;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutParams;->getViewLayoutPosition()I
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutParams;->isItemChanged()Z
HSPLandroidx/recyclerview/widget/RecyclerView$LayoutParams;->isItemRemoved()Z
HSPLandroidx/recyclerview/widget/RecyclerView$OnScrollListener;-><init>()V
HSPLandroidx/recyclerview/widget/RecyclerView$OnScrollListener;->onScrollStateChanged(Landroidx/recyclerview/widget/RecyclerView;I)V
HSPLandroidx/recyclerview/widget/RecyclerView$RecycledViewPool$ScrapData;-><init>()V
HSPLandroidx/recyclerview/widget/RecyclerView$RecycledViewPool;-><init>()V
HSPLandroidx/recyclerview/widget/RecyclerView$RecycledViewPool;->attach()V
HSPLandroidx/recyclerview/widget/RecyclerView$RecycledViewPool;->clear()V
HSPLandroidx/recyclerview/widget/RecyclerView$RecycledViewPool;->factorInBindTime(IJ)V
HSPLandroidx/recyclerview/widget/RecyclerView$RecycledViewPool;->factorInCreateTime(IJ)V
HSPLandroidx/recyclerview/widget/RecyclerView$RecycledViewPool;->getRecycledView(I)Landroidx/recyclerview/widget/RecyclerView$ViewHolder;
HSPLandroidx/recyclerview/widget/RecyclerView$RecycledViewPool;->getScrapDataForType(I)Landroidx/recyclerview/widget/RecyclerView$RecycledViewPool$ScrapData;
HSPLandroidx/recyclerview/widget/RecyclerView$RecycledViewPool;->onAdapterChanged(Landroidx/recyclerview/widget/RecyclerView$Adapter;Landroidx/recyclerview/widget/RecyclerView$Adapter;Z)V
HSPLandroidx/recyclerview/widget/RecyclerView$RecycledViewPool;->putRecycledView(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)V
HSPLandroidx/recyclerview/widget/RecyclerView$RecycledViewPool;->runningAverage(JJ)J
HSPLandroidx/recyclerview/widget/RecyclerView$RecycledViewPool;->willBindInTime(IJJ)Z
HSPLandroidx/recyclerview/widget/RecyclerView$RecycledViewPool;->willCreateInTime(IJJ)Z
HSPLandroidx/recyclerview/widget/RecyclerView$Recycler;-><init>(Landroidx/recyclerview/widget/RecyclerView;)V
HSPLandroidx/recyclerview/widget/RecyclerView$Recycler;->addViewHolderToRecycledViewPool(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;Z)V
HSPLandroidx/recyclerview/widget/RecyclerView$Recycler;->attachAccessibilityDelegateOnBind(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)V
HSPLandroidx/recyclerview/widget/RecyclerView$Recycler;->clear()V
HSPLandroidx/recyclerview/widget/RecyclerView$Recycler;->clearOldPositions()V
HSPLandroidx/recyclerview/widget/RecyclerView$Recycler;->clearScrap()V
HSPLandroidx/recyclerview/widget/RecyclerView$Recycler;->dispatchViewRecycled(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)V
HSPLandroidx/recyclerview/widget/RecyclerView$Recycler;->getRecycledViewPool()Landroidx/recyclerview/widget/RecyclerView$RecycledViewPool;
HSPLandroidx/recyclerview/widget/RecyclerView$Recycler;->getScrapCount()I
HSPLandroidx/recyclerview/widget/RecyclerView$Recycler;->getScrapList()Ljava/util/List;
HSPLandroidx/recyclerview/widget/RecyclerView$Recycler;->getScrapOrHiddenOrCachedHolderForPosition(IZ)Landroidx/recyclerview/widget/RecyclerView$ViewHolder;
HSPLandroidx/recyclerview/widget/RecyclerView$Recycler;->getViewForPosition(I)Landroid/view/View;
HSPLandroidx/recyclerview/widget/RecyclerView$Recycler;->getViewForPosition(IZ)Landroid/view/View;
HSPLandroidx/recyclerview/widget/RecyclerView$Recycler;->markItemDecorInsetsDirty()V
HSPLandroidx/recyclerview/widget/RecyclerView$Recycler;->markKnownViewsInvalid()V
HSPLandroidx/recyclerview/widget/RecyclerView$Recycler;->offsetPositionRecordsForInsert(II)V
HSPLandroidx/recyclerview/widget/RecyclerView$Recycler;->onAdapterChanged(Landroidx/recyclerview/widget/RecyclerView$Adapter;Landroidx/recyclerview/widget/RecyclerView$Adapter;Z)V
HSPLandroidx/recyclerview/widget/RecyclerView$Recycler;->recycleAndClearCachedViews()V
HSPLandroidx/recyclerview/widget/RecyclerView$Recycler;->recycleCachedViewAt(I)V
HSPLandroidx/recyclerview/widget/RecyclerView$Recycler;->recycleView(Landroid/view/View;)V
HSPLandroidx/recyclerview/widget/RecyclerView$Recycler;->recycleViewHolderInternal(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)V
HSPLandroidx/recyclerview/widget/RecyclerView$Recycler;->tryBindViewHolderByDeadline(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;IIJ)Z
HSPLandroidx/recyclerview/widget/RecyclerView$Recycler;->tryGetViewHolderForPositionByDeadline(IZJ)Landroidx/recyclerview/widget/RecyclerView$ViewHolder;
HSPLandroidx/recyclerview/widget/RecyclerView$Recycler;->updateViewCacheSize()V
HSPLandroidx/recyclerview/widget/RecyclerView$Recycler;->validateViewHolderForOffsetPosition(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)Z
HSPLandroidx/recyclerview/widget/RecyclerView$RecyclerViewDataObserver;-><init>(Landroidx/recyclerview/widget/RecyclerView;)V
HSPLandroidx/recyclerview/widget/RecyclerView$RecyclerViewDataObserver;->onItemRangeInserted(II)V
HSPLandroidx/recyclerview/widget/RecyclerView$RecyclerViewDataObserver;->triggerUpdateProcessor()V
HSPLandroidx/recyclerview/widget/RecyclerView$State;-><init>()V
HSPLandroidx/recyclerview/widget/RecyclerView$State;->assertLayoutStep(I)V
HSPLandroidx/recyclerview/widget/RecyclerView$State;->getItemCount()I
HSPLandroidx/recyclerview/widget/RecyclerView$State;->hasTargetScrollPosition()Z
HSPLandroidx/recyclerview/widget/RecyclerView$State;->isPreLayout()Z
HSPLandroidx/recyclerview/widget/RecyclerView$State;->willRunPredictiveAnimations()Z
HSPLandroidx/recyclerview/widget/RecyclerView$ViewFlinger;-><init>(Landroidx/recyclerview/widget/RecyclerView;)V
HSPLandroidx/recyclerview/widget/RecyclerView$ViewFlinger;->fling(II)V
HSPLandroidx/recyclerview/widget/RecyclerView$ViewFlinger;->internalPostOnAnimation()V
HSPLandroidx/recyclerview/widget/RecyclerView$ViewFlinger;->postOnAnimation()V
HSPLandroidx/recyclerview/widget/RecyclerView$ViewFlinger;->run()V
HSPLandroidx/recyclerview/widget/RecyclerView$ViewFlinger;->stop()V
HSPLandroidx/recyclerview/widget/RecyclerView$ViewHolder;-><clinit>()V
HSPLandroidx/recyclerview/widget/RecyclerView$ViewHolder;-><init>(Landroid/view/View;)V
HSPLandroidx/recyclerview/widget/RecyclerView$ViewHolder;->clearPayload()V
HSPLandroidx/recyclerview/widget/RecyclerView$ViewHolder;->doesTransientStatePreventRecycling()Z
HSPLandroidx/recyclerview/widget/RecyclerView$ViewHolder;->getItemViewType()I
HSPLandroidx/recyclerview/widget/RecyclerView$ViewHolder;->getLayoutPosition()I
HSPLandroidx/recyclerview/widget/RecyclerView$ViewHolder;->getUnmodifiedPayloads()Ljava/util/List;
HSPLandroidx/recyclerview/widget/RecyclerView$ViewHolder;->hasAnyOfTheFlags(I)Z
HSPLandroidx/recyclerview/widget/RecyclerView$ViewHolder;->isAttachedToTransitionOverlay()Z
HSPLandroidx/recyclerview/widget/RecyclerView$ViewHolder;->isBound()Z
HSPLandroidx/recyclerview/widget/RecyclerView$ViewHolder;->isInvalid()Z
HSPLandroidx/recyclerview/widget/RecyclerView$ViewHolder;->isRecyclable()Z
HSPLandroidx/recyclerview/widget/RecyclerView$ViewHolder;->isRemoved()Z
HSPLandroidx/recyclerview/widget/RecyclerView$ViewHolder;->isScrap()Z
HSPLandroidx/recyclerview/widget/RecyclerView$ViewHolder;->isTmpDetached()Z
HSPLandroidx/recyclerview/widget/RecyclerView$ViewHolder;->isUpdated()Z
HSPLandroidx/recyclerview/widget/RecyclerView$ViewHolder;->needsUpdate()Z
HSPLandroidx/recyclerview/widget/RecyclerView$ViewHolder;->resetInternal()V
HSPLandroidx/recyclerview/widget/RecyclerView$ViewHolder;->setFlags(II)V
HSPLandroidx/recyclerview/widget/RecyclerView$ViewHolder;->setIsRecyclable(Z)V
HSPLandroidx/recyclerview/widget/RecyclerView$ViewHolder;->shouldBeKeptAsChild()Z
HSPLandroidx/recyclerview/widget/RecyclerView$ViewHolder;->shouldIgnore()Z
HSPLandroidx/recyclerview/widget/RecyclerView$ViewHolder;->wasReturnedFromScrap()Z
HSPLandroidx/recyclerview/widget/RecyclerView;-><clinit>()V
HSPLandroidx/recyclerview/widget/RecyclerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
HSPLandroidx/recyclerview/widget/RecyclerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
HSPLandroidx/recyclerview/widget/RecyclerView;->absorbGlows(II)V
HSPLandroidx/recyclerview/widget/RecyclerView;->access$200(Landroidx/recyclerview/widget/RecyclerView;)Z
HSPLandroidx/recyclerview/widget/RecyclerView;->addOnScrollListener(Landroidx/recyclerview/widget/RecyclerView$OnScrollListener;)V
HSPLandroidx/recyclerview/widget/RecyclerView;->animateAppearance(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;Landroidx/recyclerview/widget/RecyclerView$ItemAnimator$ItemHolderInfo;Landroidx/recyclerview/widget/RecyclerView$ItemAnimator$ItemHolderInfo;)V
HSPLandroidx/recyclerview/widget/RecyclerView;->assertNotInLayoutOrScroll(Ljava/lang/String;)V
HSPLandroidx/recyclerview/widget/RecyclerView;->checkLayoutParams(Landroid/view/ViewGroup$LayoutParams;)Z
HSPLandroidx/recyclerview/widget/RecyclerView;->clearNestedRecyclerViewIfNotNested(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)V
HSPLandroidx/recyclerview/widget/RecyclerView;->clearOldPositions()V
HSPLandroidx/recyclerview/widget/RecyclerView;->computeHorizontalScrollExtent()I
HSPLandroidx/recyclerview/widget/RecyclerView;->computeHorizontalScrollOffset()I
HSPLandroidx/recyclerview/widget/RecyclerView;->computeHorizontalScrollRange()I
HSPLandroidx/recyclerview/widget/RecyclerView;->computeVerticalScrollExtent()I
HSPLandroidx/recyclerview/widget/RecyclerView;->computeVerticalScrollOffset()I
HSPLandroidx/recyclerview/widget/RecyclerView;->computeVerticalScrollRange()I
HSPLandroidx/recyclerview/widget/RecyclerView;->considerReleasingGlowsOnScroll(II)V
HSPLandroidx/recyclerview/widget/RecyclerView;->consumePendingUpdateOperations()V
HSPLandroidx/recyclerview/widget/RecyclerView;->createLayoutManager(Landroid/content/Context;Ljava/lang/String;Landroid/util/AttributeSet;II)V
HSPLandroidx/recyclerview/widget/RecyclerView;->defaultOnMeasure(II)V
HSPLandroidx/recyclerview/widget/RecyclerView;->didChildRangeChange(II)Z
HSPLandroidx/recyclerview/widget/RecyclerView;->dispatchChildAttached(Landroid/view/View;)V
HSPLandroidx/recyclerview/widget/RecyclerView;->dispatchChildDetached(Landroid/view/View;)V
HSPLandroidx/recyclerview/widget/RecyclerView;->dispatchContentChangedIfNecessary()V
HSPLandroidx/recyclerview/widget/RecyclerView;->dispatchLayout()V
HSPLandroidx/recyclerview/widget/RecyclerView;->dispatchLayoutStep1()V
HSPLandroidx/recyclerview/widget/RecyclerView;->dispatchLayoutStep2()V
HSPLandroidx/recyclerview/widget/RecyclerView;->dispatchLayoutStep3()V
HSPLandroidx/recyclerview/widget/RecyclerView;->dispatchNestedFling(FFZ)Z
HSPLandroidx/recyclerview/widget/RecyclerView;->dispatchNestedPreFling(FF)Z
HSPLandroidx/recyclerview/widget/RecyclerView;->dispatchNestedPreScroll(II[I[II)Z
HSPLandroidx/recyclerview/widget/RecyclerView;->dispatchNestedScroll(IIII[II[I)V
HSPLandroidx/recyclerview/widget/RecyclerView;->dispatchOnScrollStateChanged(I)V
HSPLandroidx/recyclerview/widget/RecyclerView;->dispatchOnScrolled(II)V
HSPLandroidx/recyclerview/widget/RecyclerView;->dispatchPendingImportantForAccessibilityChanges()V
HSPLandroidx/recyclerview/widget/RecyclerView;->dispatchToOnItemTouchListeners(Landroid/view/MotionEvent;)Z
HSPLandroidx/recyclerview/widget/RecyclerView;->draw(Landroid/graphics/Canvas;)V
HSPLandroidx/recyclerview/widget/RecyclerView;->drawChild(Landroid/graphics/Canvas;Landroid/view/View;J)Z
HSPLandroidx/recyclerview/widget/RecyclerView;->fillRemainingScrollValues(Landroidx/recyclerview/widget/RecyclerView$State;)V
HSPLandroidx/recyclerview/widget/RecyclerView;->findInterceptingOnItemTouchListener(Landroid/view/MotionEvent;)Z
HSPLandroidx/recyclerview/widget/RecyclerView;->findMinMaxChildLayoutPositions([I)V
HSPLandroidx/recyclerview/widget/RecyclerView;->findNestedRecyclerView(Landroid/view/View;)Landroidx/recyclerview/widget/RecyclerView;
HSPLandroidx/recyclerview/widget/RecyclerView;->fling(II)Z
HSPLandroidx/recyclerview/widget/RecyclerView;->generateLayoutParams(Landroid/util/AttributeSet;)Landroid/view/ViewGroup$LayoutParams;
HSPLandroidx/recyclerview/widget/RecyclerView;->getAccessibilityClassName()Ljava/lang/CharSequence;
HSPLandroidx/recyclerview/widget/RecyclerView;->getChangedHolderKey(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)J
HSPLandroidx/recyclerview/widget/RecyclerView;->getChildViewHolder(Landroid/view/View;)Landroidx/recyclerview/widget/RecyclerView$ViewHolder;
HSPLandroidx/recyclerview/widget/RecyclerView;->getChildViewHolderInt(Landroid/view/View;)Landroidx/recyclerview/widget/RecyclerView$ViewHolder;
HSPLandroidx/recyclerview/widget/RecyclerView;->getFullClassName(Landroid/content/Context;Ljava/lang/String;)Ljava/lang/String;
HSPLandroidx/recyclerview/widget/RecyclerView;->getItemDecorInsetsForChild(Landroid/view/View;)Landroid/graphics/Rect;
HSPLandroidx/recyclerview/widget/RecyclerView;->getLayoutManager()Landroidx/recyclerview/widget/RecyclerView$LayoutManager;
HSPLandroidx/recyclerview/widget/RecyclerView;->getNanoTime()J
HSPLandroidx/recyclerview/widget/RecyclerView;->getScrollState()I
HSPLandroidx/recyclerview/widget/RecyclerView;->getScrollingChildHelper()Landroidx/core/view/NestedScrollingChildHelper;
HSPLandroidx/recyclerview/widget/RecyclerView;->hasPendingAdapterUpdates()Z
HSPLandroidx/recyclerview/widget/RecyclerView;->initAdapterManager()V
HSPLandroidx/recyclerview/widget/RecyclerView;->initAutofill()V
HSPLandroidx/recyclerview/widget/RecyclerView;->initChildrenHelper()V
HSPLandroidx/recyclerview/widget/RecyclerView;->invalidateGlows()V
HSPLandroidx/recyclerview/widget/RecyclerView;->isAccessibilityEnabled()Z
HSPLandroidx/recyclerview/widget/RecyclerView;->isAttachedToWindow()Z
HSPLandroidx/recyclerview/widget/RecyclerView;->isComputingLayout()Z
HSPLandroidx/recyclerview/widget/RecyclerView;->markItemDecorInsetsDirty()V
HSPLandroidx/recyclerview/widget/RecyclerView;->markKnownViewsInvalid()V
HSPLandroidx/recyclerview/widget/RecyclerView;->offsetChildrenVertical(I)V
HSPLandroidx/recyclerview/widget/RecyclerView;->offsetPositionRecordsForInsert(II)V
HSPLandroidx/recyclerview/widget/RecyclerView;->onAttachedToWindow()V
HSPLandroidx/recyclerview/widget/RecyclerView;->onChildAttachedToWindow(Landroid/view/View;)V
HSPLandroidx/recyclerview/widget/RecyclerView;->onChildDetachedFromWindow(Landroid/view/View;)V
HSPLandroidx/recyclerview/widget/RecyclerView;->onDraw(Landroid/graphics/Canvas;)V
HSPLandroidx/recyclerview/widget/RecyclerView;->onEnterLayoutOrScroll()V
HSPLandroidx/recyclerview/widget/RecyclerView;->onExitLayoutOrScroll()V
HSPLandroidx/recyclerview/widget/RecyclerView;->onExitLayoutOrScroll(Z)V
HSPLandroidx/recyclerview/widget/RecyclerView;->onInterceptTouchEvent(Landroid/view/MotionEvent;)Z
HSPLandroidx/recyclerview/widget/RecyclerView;->onLayout(ZIIII)V
HSPLandroidx/recyclerview/widget/RecyclerView;->onMeasure(II)V
HSPLandroidx/recyclerview/widget/RecyclerView;->onScrollStateChanged(I)V
HSPLandroidx/recyclerview/widget/RecyclerView;->onScrolled(II)V
HSPLandroidx/recyclerview/widget/RecyclerView;->onSizeChanged(IIII)V
HSPLandroidx/recyclerview/widget/RecyclerView;->onTouchEvent(Landroid/view/MotionEvent;)Z
HSPLandroidx/recyclerview/widget/RecyclerView;->postAnimationRunner()V
HSPLandroidx/recyclerview/widget/RecyclerView;->predictiveItemAnimationsEnabled()Z
HSPLandroidx/recyclerview/widget/RecyclerView;->processAdapterUpdatesAndSetAnimationFlags()V
HSPLandroidx/recyclerview/widget/RecyclerView;->processDataSetCompletelyChanged(Z)V
HSPLandroidx/recyclerview/widget/RecyclerView;->pullGlows(FFFF)V
HSPLandroidx/recyclerview/widget/RecyclerView;->recoverFocusFromState()V
HSPLandroidx/recyclerview/widget/RecyclerView;->releaseGlows()V
HSPLandroidx/recyclerview/widget/RecyclerView;->removeAndRecycleViews()V
HSPLandroidx/recyclerview/widget/RecyclerView;->removeAnimatingView(Landroid/view/View;)Z
HSPLandroidx/recyclerview/widget/RecyclerView;->repositionShadowingViews()V
HSPLandroidx/recyclerview/widget/RecyclerView;->requestLayout()V
HSPLandroidx/recyclerview/widget/RecyclerView;->resetFocusInfo()V
HSPLandroidx/recyclerview/widget/RecyclerView;->resetScroll()V
HSPLandroidx/recyclerview/widget/RecyclerView;->saveFocusInfo()V
HSPLandroidx/recyclerview/widget/RecyclerView;->saveOldPositions()V
HSPLandroidx/recyclerview/widget/RecyclerView;->scrollByInternal(IILandroid/view/MotionEvent;I)Z
HSPLandroidx/recyclerview/widget/RecyclerView;->scrollStep(II[I)V
HSPLandroidx/recyclerview/widget/RecyclerView;->sendAccessibilityEventUnchecked(Landroid/view/accessibility/AccessibilityEvent;)V
HSPLandroidx/recyclerview/widget/RecyclerView;->setAccessibilityDelegateCompat(Landroidx/recyclerview/widget/RecyclerViewAccessibilityDelegate;)V
HSPLandroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V
HSPLandroidx/recyclerview/widget/RecyclerView;->setAdapterInternal(Landroidx/recyclerview/widget/RecyclerView$Adapter;ZZ)V
HSPLandroidx/recyclerview/widget/RecyclerView;->setLayoutFrozen(Z)V
HSPLandroidx/recyclerview/widget/RecyclerView;->setLayoutManager(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V
HSPLandroidx/recyclerview/widget/RecyclerView;->setNestedScrollingEnabled(Z)V
HSPLandroidx/recyclerview/widget/RecyclerView;->setScrollState(I)V
HSPLandroidx/recyclerview/widget/RecyclerView;->shouldDeferAccessibilityEvent(Landroid/view/accessibility/AccessibilityEvent;)Z
HSPLandroidx/recyclerview/widget/RecyclerView;->startInterceptRequestLayout()V
HSPLandroidx/recyclerview/widget/RecyclerView;->startNestedScroll(II)Z
HSPLandroidx/recyclerview/widget/RecyclerView;->stopInterceptRequestLayout(Z)V
HSPLandroidx/recyclerview/widget/RecyclerView;->stopNestedScroll()V
HSPLandroidx/recyclerview/widget/RecyclerView;->stopNestedScroll(I)V
HSPLandroidx/recyclerview/widget/RecyclerView;->stopScroll()V
HSPLandroidx/recyclerview/widget/RecyclerView;->stopScrollersInternal()V
HSPLandroidx/recyclerview/widget/RecyclerView;->suppressLayout(Z)V
HSPLandroidx/recyclerview/widget/RecyclerViewAccessibilityDelegate$ItemDelegate;-><init>(Landroidx/recyclerview/widget/RecyclerViewAccessibilityDelegate;)V
HSPLandroidx/recyclerview/widget/RecyclerViewAccessibilityDelegate$ItemDelegate;->getAccessibilityNodeProvider(Landroid/view/View;)Landroidx/core/view/accessibility/AccessibilityNodeProviderCompat;
HSPLandroidx/recyclerview/widget/RecyclerViewAccessibilityDelegate$ItemDelegate;->getAndRemoveOriginalDelegateForItem(Landroid/view/View;)Landroidx/core/view/AccessibilityDelegateCompat;
HSPLandroidx/recyclerview/widget/RecyclerViewAccessibilityDelegate$ItemDelegate;->onInitializeAccessibilityNodeInfo(Landroid/view/View;Landroidx/core/view/accessibility/AccessibilityNodeInfoCompat;)V
HSPLandroidx/recyclerview/widget/RecyclerViewAccessibilityDelegate$ItemDelegate;->saveOriginalDelegate(Landroid/view/View;)V
HSPLandroidx/recyclerview/widget/RecyclerViewAccessibilityDelegate;-><init>(Landroidx/recyclerview/widget/RecyclerView;)V
HSPLandroidx/recyclerview/widget/RecyclerViewAccessibilityDelegate;->getItemDelegate()Landroidx/core/view/AccessibilityDelegateCompat;
HSPLandroidx/recyclerview/widget/RecyclerViewAccessibilityDelegate;->onInitializeAccessibilityEvent(Landroid/view/View;Landroid/view/accessibility/AccessibilityEvent;)V
HSPLandroidx/recyclerview/widget/RecyclerViewAccessibilityDelegate;->onInitializeAccessibilityNodeInfo(Landroid/view/View;Landroidx/core/view/accessibility/AccessibilityNodeInfoCompat;)V
HSPLandroidx/recyclerview/widget/RecyclerViewAccessibilityDelegate;->shouldIgnore()Z
HSPLandroidx/recyclerview/widget/ScrollbarHelper;->computeScrollExtent(Landroidx/recyclerview/widget/RecyclerView$State;Landroidx/recyclerview/widget/OrientationHelper;Landroid/view/View;Landroid/view/View;Landroidx/recyclerview/widget/RecyclerView$LayoutManager;Z)I
HSPLandroidx/recyclerview/widget/ScrollbarHelper;->computeScrollOffset(Landroidx/recyclerview/widget/RecyclerView$State;Landroidx/recyclerview/widget/OrientationHelper;Landroid/view/View;Landroid/view/View;Landroidx/recyclerview/widget/RecyclerView$LayoutManager;ZZ)I
HSPLandroidx/recyclerview/widget/ScrollbarHelper;->computeScrollRange(Landroidx/recyclerview/widget/RecyclerView$State;Landroidx/recyclerview/widget/OrientationHelper;Landroid/view/View;Landroid/view/View;Landroidx/recyclerview/widget/RecyclerView$LayoutManager;Z)I
HSPLandroidx/recyclerview/widget/SimpleItemAnimator;-><init>()V
HSPLandroidx/recyclerview/widget/SimpleItemAnimator;->animateAppearance(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;Landroidx/recyclerview/widget/RecyclerView$ItemAnimator$ItemHolderInfo;Landroidx/recyclerview/widget/RecyclerView$ItemAnimator$ItemHolderInfo;)Z
HSPLandroidx/recyclerview/widget/SimpleItemAnimator;->dispatchAddFinished(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)V
HSPLandroidx/recyclerview/widget/SimpleItemAnimator;->dispatchAddStarting(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)V
HSPLandroidx/recyclerview/widget/SimpleItemAnimator;->onAddFinished(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)V
HSPLandroidx/recyclerview/widget/SimpleItemAnimator;->onAddStarting(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)V
HSPLandroidx/recyclerview/widget/ViewBoundsCheck$BoundFlags;-><init>()V
HSPLandroidx/recyclerview/widget/ViewBoundsCheck$BoundFlags;->addFlags(I)V
HSPLandroidx/recyclerview/widget/ViewBoundsCheck$BoundFlags;->boundsMatch()Z
HSPLandroidx/recyclerview/widget/ViewBoundsCheck$BoundFlags;->compare(II)I
HSPLandroidx/recyclerview/widget/ViewBoundsCheck$BoundFlags;->resetFlags()V
HSPLandroidx/recyclerview/widget/ViewBoundsCheck$BoundFlags;->setBounds(IIII)V
HSPLandroidx/recyclerview/widget/ViewBoundsCheck;-><init>(Landroidx/recyclerview/widget/ViewBoundsCheck$Callback;)V
HSPLandroidx/recyclerview/widget/ViewBoundsCheck;->findOneViewWithinBoundFlags(IIII)Landroid/view/View;
HSPLandroidx/recyclerview/widget/ViewInfoStore$InfoRecord;-><clinit>()V
HSPLandroidx/recyclerview/widget/ViewInfoStore$InfoRecord;-><init>()V
HSPLandroidx/recyclerview/widget/ViewInfoStore$InfoRecord;->obtain()Landroidx/recyclerview/widget/ViewInfoStore$InfoRecord;
HSPLandroidx/recyclerview/widget/ViewInfoStore$InfoRecord;->recycle(Landroidx/recyclerview/widget/ViewInfoStore$InfoRecord;)V
HSPLandroidx/recyclerview/widget/ViewInfoStore;-><init>()V
HSPLandroidx/recyclerview/widget/ViewInfoStore;->addToPostLayout(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;Landroidx/recyclerview/widget/RecyclerView$ItemAnimator$ItemHolderInfo;)V
HSPLandroidx/recyclerview/widget/ViewInfoStore;->clear()V
HSPLandroidx/recyclerview/widget/ViewInfoStore;->getFromOldChangeHolders(J)Landroidx/recyclerview/widget/RecyclerView$ViewHolder;
HSPLandroidx/recyclerview/widget/ViewInfoStore;->process(Landroidx/recyclerview/widget/ViewInfoStore$ProcessCallback;)V
HSPLandroidx/recyclerview/widget/ViewInfoStore;->removeFromDisappearedInLayout(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)V
HSPLandroidx/recyclerview/widget/ViewInfoStore;->removeViewHolder(Landroidx/recyclerview/widget/RecyclerView$ViewHolder;)V
PLandroidx/recyclerview/widget/GapWorker;->remove(Landroidx/recyclerview/widget/RecyclerView;)V
PLandroidx/recyclerview/widget/LinearLayoutManager$SavedState$1;-><init>()V
PLandroidx/recyclerview/widget/LinearLayoutManager$SavedState;-><clinit>()V
PLandroidx/recyclerview/widget/LinearLayoutManager$SavedState;-><init>()V
PLandroidx/recyclerview/widget/LinearLayoutManager;->getChildClosestToStart()Landroid/view/View;
PLandroidx/recyclerview/widget/LinearLayoutManager;->onDetachedFromWindow(Landroidx/recyclerview/widget/RecyclerView;Landroidx/recyclerview/widget/RecyclerView$Recycler;)V
PLandroidx/recyclerview/widget/LinearLayoutManager;->onSaveInstanceState()Landroid/os/Parcelable;
PLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->dispatchDetachedFromWindow(Landroidx/recyclerview/widget/RecyclerView;Landroidx/recyclerview/widget/RecyclerView$Recycler;)V
PLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->onDetachedFromWindow(Landroidx/recyclerview/widget/RecyclerView;)V
PLandroidx/recyclerview/widget/RecyclerView$LayoutManager;->onDetachedFromWindow(Landroidx/recyclerview/widget/RecyclerView;Landroidx/recyclerview/widget/RecyclerView$Recycler;)V
PLandroidx/recyclerview/widget/RecyclerView$SavedState$1;-><init>()V
PLandroidx/recyclerview/widget/RecyclerView$SavedState;-><clinit>()V
PLandroidx/recyclerview/widget/RecyclerView$SavedState;-><init>(Landroid/os/Parcelable;)V
PLandroidx/recyclerview/widget/RecyclerView;->dispatchSaveInstanceState(Landroid/util/SparseArray;)V
PLandroidx/recyclerview/widget/RecyclerView;->onDetachedFromWindow()V
PLandroidx/recyclerview/widget/RecyclerView;->onSaveInstanceState()Landroid/os/Parcelable;
PLandroidx/recyclerview/widget/RecyclerViewAccessibilityDelegate$ItemDelegate;->onInitializeAccessibilityEvent(Landroid/view/View;Landroid/view/accessibility/AccessibilityEvent;)V
PLandroidx/recyclerview/widget/RecyclerViewAccessibilityDelegate$ItemDelegate;->sendAccessibilityEventUnchecked(Landroid/view/View;Landroid/view/accessibility/AccessibilityEvent;)V
PLandroidx/recyclerview/widget/ViewInfoStore$InfoRecord;->drainCache()V
PLandroidx/recyclerview/widget/ViewInfoStore;->onDetach()V

# Baseline profiles for androidx.activity

HSPLandroidx/activity/ComponentActivity$1;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$2;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$3;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$3;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/activity/ComponentActivity$4;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$4;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/activity/ComponentActivity$5;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$5;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/activity/ComponentActivity$6;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$7;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$7;->onContextAvailable(Landroid/content/Context;)V
HSPLandroidx/activity/ComponentActivity;-><init>()V
HSPLandroidx/activity/ComponentActivity;->addOnContextAvailableListener(Landroidx/activity/contextaware/OnContextAvailableListener;)V
HSPLandroidx/activity/ComponentActivity;->ensureViewModelStore()V
HSPLandroidx/activity/ComponentActivity;->getActivityResultRegistry()Landroidx/activity/result/ActivityResultRegistry;
HSPLandroidx/activity/ComponentActivity;->getLifecycle()Landroidx/lifecycle/Lifecycle;
HSPLandroidx/activity/ComponentActivity;->getOnBackPressedDispatcher()Landroidx/activity/OnBackPressedDispatcher;
HSPLandroidx/activity/ComponentActivity;->getSavedStateRegistry()Landroidx/savedstate/SavedStateRegistry;
HSPLandroidx/activity/ComponentActivity;->getViewModelStore()Landroidx/lifecycle/ViewModelStore;
HSPLandroidx/activity/ComponentActivity;->onCreate(Landroid/os/Bundle;)V
HSPLandroidx/activity/OnBackPressedCallback;-><init>(Z)V
HSPLandroidx/activity/OnBackPressedCallback;->addCancellable(Landroidx/activity/Cancellable;)V
HSPLandroidx/activity/OnBackPressedCallback;->remove()V
HSPLandroidx/activity/OnBackPressedCallback;->setEnabled(Z)V
HSPLandroidx/activity/OnBackPressedDispatcher$LifecycleOnBackPressedCancellable;-><init>(Landroidx/activity/OnBackPressedDispatcher;Landroidx/lifecycle/Lifecycle;Landroidx/activity/OnBackPressedCallback;)V
HSPLandroidx/activity/OnBackPressedDispatcher$LifecycleOnBackPressedCancellable;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/activity/OnBackPressedDispatcher$OnBackPressedCancellable;-><init>(Landroidx/activity/OnBackPressedDispatcher;Landroidx/activity/OnBackPressedCallback;)V
HSPLandroidx/activity/OnBackPressedDispatcher;-><init>(Ljava/lang/Runnable;)V
HSPLandroidx/activity/OnBackPressedDispatcher;->addCallback(Landroidx/lifecycle/LifecycleOwner;Landroidx/activity/OnBackPressedCallback;)V
HSPLandroidx/activity/OnBackPressedDispatcher;->addCancellableCallback(Landroidx/activity/OnBackPressedCallback;)Landroidx/activity/Cancellable;
HSPLandroidx/activity/contextaware/ContextAwareHelper;-><init>()V
HSPLandroidx/activity/contextaware/ContextAwareHelper;->addOnContextAvailableListener(Landroidx/activity/contextaware/OnContextAvailableListener;)V
HSPLandroidx/activity/contextaware/ContextAwareHelper;->dispatchOnContextAvailable(Landroid/content/Context;)V
HSPLandroidx/activity/result/ActivityResultLauncher;-><init>()V
HSPLandroidx/activity/result/ActivityResultRegistry$3;-><init>(Landroidx/activity/result/ActivityResultRegistry;Ljava/lang/String;ILandroidx/activity/result/contract/ActivityResultContract;)V
HSPLandroidx/activity/result/ActivityResultRegistry$CallbackAndContract;-><init>(Landroidx/activity/result/ActivityResultCallback;Landroidx/activity/result/contract/ActivityResultContract;)V
HSPLandroidx/activity/result/ActivityResultRegistry;-><init>()V
HSPLandroidx/activity/result/ActivityResultRegistry;->bindRcKey(ILjava/lang/String;)V
HSPLandroidx/activity/result/ActivityResultRegistry;->generateRandomNumber()I
HSPLandroidx/activity/result/ActivityResultRegistry;->register(Ljava/lang/String;Landroidx/activity/result/contract/ActivityResultContract;Landroidx/activity/result/ActivityResultCallback;)Landroidx/activity/result/ActivityResultLauncher;
HSPLandroidx/activity/result/ActivityResultRegistry;->registerKey(Ljava/lang/String;)I
HSPLandroidx/activity/result/contract/ActivityResultContract;-><init>()V
HSPLandroidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions;-><init>()V
HSPLandroidx/activity/result/contract/ActivityResultContracts$StartActivityForResult;-><init>()V
Landroidx/activity/Cancellable;
Landroidx/activity/ComponentActivity$1;
Landroidx/activity/ComponentActivity$2;
Landroidx/activity/ComponentActivity$3;
Landroidx/activity/ComponentActivity$4;
Landroidx/activity/ComponentActivity$5;
Landroidx/activity/ComponentActivity$6;
Landroidx/activity/ComponentActivity$7;
Landroidx/activity/ComponentActivity$NonConfigurationInstances;
Landroidx/activity/ComponentActivity;
Landroidx/activity/OnBackPressedCallback;
Landroidx/activity/OnBackPressedDispatcher$LifecycleOnBackPressedCancellable;
Landroidx/activity/OnBackPressedDispatcher$OnBackPressedCancellable;
Landroidx/activity/OnBackPressedDispatcher;
Landroidx/activity/OnBackPressedDispatcherOwner;
Landroidx/activity/contextaware/ContextAware;
Landroidx/activity/contextaware/ContextAwareHelper;
Landroidx/activity/contextaware/OnContextAvailableListener;
Landroidx/activity/result/ActivityResult;
Landroidx/activity/result/ActivityResultCallback;
Landroidx/activity/result/ActivityResultCaller;
Landroidx/activity/result/ActivityResultLauncher;
Landroidx/activity/result/ActivityResultRegistry$3;
Landroidx/activity/result/ActivityResultRegistry$CallbackAndContract;
Landroidx/activity/result/ActivityResultRegistry;
Landroidx/activity/result/ActivityResultRegistryOwner;
Landroidx/activity/result/contract/ActivityResultContract;
Landroidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions;
Landroidx/activity/result/contract/ActivityResultContracts$StartActivityForResult;
PLandroidx/activity/ComponentActivity$1;->run()V
PLandroidx/activity/ComponentActivity;->access$001(Landroidx/activity/ComponentActivity;)V
PLandroidx/activity/ComponentActivity;->onBackPressed()V
PLandroidx/activity/OnBackPressedCallback;->isEnabled()Z
PLandroidx/activity/OnBackPressedCallback;->removeCancellable(Landroidx/activity/Cancellable;)V
PLandroidx/activity/OnBackPressedDispatcher$LifecycleOnBackPressedCancellable;->cancel()V
PLandroidx/activity/OnBackPressedDispatcher$OnBackPressedCancellable;->cancel()V
PLandroidx/activity/OnBackPressedDispatcher;->onBackPressed()V
PLandroidx/activity/contextaware/ContextAwareHelper;->clearAvailableContext()V
PLandroidx/activity/result/ActivityResultRegistry$3;->unregister()V
PLandroidx/activity/result/ActivityResultRegistry;->unregister(Ljava/lang/String;)V

# Baseline profiles for Lifecycle ViewModel

HSPLandroidx/lifecycle/ViewModel;-><init>()V
HSPLandroidx/lifecycle/ViewModelLazy;-><init>(Lkotlin/reflect/KClass;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)V
HSPLandroidx/lifecycle/ViewModelLazy;->getValue()Landroidx/lifecycle/ViewModel;
HSPLandroidx/lifecycle/ViewModelLazy;->getValue()Ljava/lang/Object;
HSPLandroidx/lifecycle/ViewModelProvider;-><init>(Landroidx/lifecycle/ViewModelStore;Landroidx/lifecycle/ViewModelProvider$Factory;)V
HSPLandroidx/lifecycle/ViewModelProvider;->get(Ljava/lang/Class;)Landroidx/lifecycle/ViewModel;
HSPLandroidx/lifecycle/ViewModelProvider;->get(Ljava/lang/String;Ljava/lang/Class;)Landroidx/lifecycle/ViewModel;
HSPLandroidx/lifecycle/ViewModelStore;-><init>()V
HSPLandroidx/lifecycle/ViewModelStore;->get(Ljava/lang/String;)Landroidx/lifecycle/ViewModel;
HSPLandroidx/lifecycle/ViewModelStore;->put(Ljava/lang/String;Landroidx/lifecycle/ViewModel;)V
PLandroidx/lifecycle/ViewModel;->clear()V
PLandroidx/lifecycle/ViewModel;->onCleared()V
PLandroidx/lifecycle/ViewModelStore;->clear()V

# Baseline Profile rules for lifecycle-runtime

HPLandroidx/lifecycle/LifecycleRegistry;->backwardPass(Landroidx/lifecycle/LifecycleOwner;)V
HSPLandroidx/lifecycle/LifecycleRegistry$ObserverWithState;-><init>(Landroidx/lifecycle/LifecycleObserver;Landroidx/lifecycle/Lifecycle$State;)V
HSPLandroidx/lifecycle/LifecycleRegistry$ObserverWithState;->dispatchEvent(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/lifecycle/LifecycleRegistry;-><init>(Landroidx/lifecycle/LifecycleOwner;)V
HSPLandroidx/lifecycle/LifecycleRegistry;-><init>(Landroidx/lifecycle/LifecycleOwner;Z)V
HSPLandroidx/lifecycle/LifecycleRegistry;->addObserver(Landroidx/lifecycle/LifecycleObserver;)V
HSPLandroidx/lifecycle/LifecycleRegistry;->calculateTargetState(Landroidx/lifecycle/LifecycleObserver;)Landroidx/lifecycle/Lifecycle$State;
HSPLandroidx/lifecycle/LifecycleRegistry;->enforceMainThreadIfNeeded(Ljava/lang/String;)V
HSPLandroidx/lifecycle/LifecycleRegistry;->forwardPass(Landroidx/lifecycle/LifecycleOwner;)V
HSPLandroidx/lifecycle/LifecycleRegistry;->getCurrentState()Landroidx/lifecycle/Lifecycle$State;
HSPLandroidx/lifecycle/LifecycleRegistry;->handleLifecycleEvent(Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/lifecycle/LifecycleRegistry;->isSynced()Z
HSPLandroidx/lifecycle/LifecycleRegistry;->min(Landroidx/lifecycle/Lifecycle$State;Landroidx/lifecycle/Lifecycle$State;)Landroidx/lifecycle/Lifecycle$State;
HSPLandroidx/lifecycle/LifecycleRegistry;->moveToState(Landroidx/lifecycle/Lifecycle$State;)V
HSPLandroidx/lifecycle/LifecycleRegistry;->popParentState()V
HSPLandroidx/lifecycle/LifecycleRegistry;->pushParentState(Landroidx/lifecycle/Lifecycle$State;)V
HSPLandroidx/lifecycle/LifecycleRegistry;->removeObserver(Landroidx/lifecycle/LifecycleObserver;)V
HSPLandroidx/lifecycle/LifecycleRegistry;->setCurrentState(Landroidx/lifecycle/Lifecycle$State;)V
HSPLandroidx/lifecycle/LifecycleRegistry;->sync()V
HSPLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;-><init>()V
HSPLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityCreated(Landroid/app/Activity;Landroid/os/Bundle;)V
HSPLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityPostCreated(Landroid/app/Activity;Landroid/os/Bundle;)V
HSPLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityPostResumed(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityPostStarted(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityResumed(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityStarted(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->registerIn(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/ReportFragment;-><init>()V
HSPLandroidx/lifecycle/ReportFragment;->dispatch(Landroid/app/Activity;Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/lifecycle/ReportFragment;->dispatch(Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/lifecycle/ReportFragment;->dispatchCreate(Landroidx/lifecycle/ReportFragment$ActivityInitializationListener;)V
HSPLandroidx/lifecycle/ReportFragment;->dispatchResume(Landroidx/lifecycle/ReportFragment$ActivityInitializationListener;)V
HSPLandroidx/lifecycle/ReportFragment;->dispatchStart(Landroidx/lifecycle/ReportFragment$ActivityInitializationListener;)V
HSPLandroidx/lifecycle/ReportFragment;->injectIfNeededIn(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/ReportFragment;->onActivityCreated(Landroid/os/Bundle;)V
HSPLandroidx/lifecycle/ReportFragment;->onResume()V
HSPLandroidx/lifecycle/ReportFragment;->onStart()V
HSPLandroidx/lifecycle/ViewTreeLifecycleOwner;->set(Landroid/view/View;Landroidx/lifecycle/LifecycleOwner;)V
HSPLandroidx/lifecycle/ViewTreeViewModelStoreOwner;->set(Landroid/view/View;Landroidx/lifecycle/ViewModelStoreOwner;)V
PLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityDestroyed(Landroid/app/Activity;)V
PLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityPaused(Landroid/app/Activity;)V
PLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityPreDestroyed(Landroid/app/Activity;)V
PLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityPrePaused(Landroid/app/Activity;)V
PLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityPreStopped(Landroid/app/Activity;)V
PLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityStopped(Landroid/app/Activity;)V
PLandroidx/lifecycle/ReportFragment;->onDestroy()V
PLandroidx/lifecycle/ReportFragment;->onPause()V
PLandroidx/lifecycle/ReportFragment;->onStop()V

# Baseline Profile Rules for androidx.startup

Landroidx/startup/AppInitializer;
HSPLandroidx/startup/AppInitializer;->**(**)**

# Baseline profiles for lifecycle-livedata-core

HSPLandroidx/lifecycle/LiveData$1;-><init>(Landroidx/lifecycle/LiveData;)V
HSPLandroidx/lifecycle/LiveData$1;->run()V
HSPLandroidx/lifecycle/LiveData$AlwaysActiveObserver;-><init>(Landroidx/lifecycle/LiveData;Landroidx/lifecycle/Observer;)V
HSPLandroidx/lifecycle/LiveData$AlwaysActiveObserver;->shouldBeActive()Z
HSPLandroidx/lifecycle/LiveData$LifecycleBoundObserver;-><init>(Landroidx/lifecycle/LiveData;Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Observer;)V
HSPLandroidx/lifecycle/LiveData$LifecycleBoundObserver;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/lifecycle/LiveData$LifecycleBoundObserver;->shouldBeActive()Z
HSPLandroidx/lifecycle/LiveData$ObserverWrapper;-><init>(Landroidx/lifecycle/LiveData;Landroidx/lifecycle/Observer;)V
HSPLandroidx/lifecycle/LiveData$ObserverWrapper;->activeStateChanged(Z)V
HSPLandroidx/lifecycle/LiveData$ObserverWrapper;->detachObserver()V
HSPLandroidx/lifecycle/LiveData;-><clinit>()V
HSPLandroidx/lifecycle/LiveData;-><init>()V
HSPLandroidx/lifecycle/LiveData;->assertMainThread(Ljava/lang/String;)V
HSPLandroidx/lifecycle/LiveData;->changeActiveCounter(I)V
HSPLandroidx/lifecycle/LiveData;->considerNotify(Landroidx/lifecycle/LiveData$ObserverWrapper;)V
HSPLandroidx/lifecycle/LiveData;->dispatchingValue(Landroidx/lifecycle/LiveData$ObserverWrapper;)V
HSPLandroidx/lifecycle/LiveData;->getValue()Ljava/lang/Object;
HSPLandroidx/lifecycle/LiveData;->getVersion()I
HSPLandroidx/lifecycle/LiveData;->hasActiveObservers()Z
HSPLandroidx/lifecycle/LiveData;->observe(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Observer;)V
HSPLandroidx/lifecycle/LiveData;->observeForever(Landroidx/lifecycle/Observer;)V
HSPLandroidx/lifecycle/LiveData;->onActive()V
HSPLandroidx/lifecycle/LiveData;->onInactive()V
HSPLandroidx/lifecycle/LiveData;->postValue(Ljava/lang/Object;)V
HSPLandroidx/lifecycle/LiveData;->removeObserver(Landroidx/lifecycle/Observer;)V
HSPLandroidx/lifecycle/LiveData;->setValue(Ljava/lang/Object;)V
HSPLandroidx/lifecycle/MediatorLiveData$Source;-><init>(Landroidx/lifecycle/LiveData;Landroidx/lifecycle/Observer;)V
HSPLandroidx/lifecycle/MediatorLiveData$Source;->onChanged(Ljava/lang/Object;)V
HSPLandroidx/lifecycle/MediatorLiveData$Source;->plug()V
HSPLandroidx/lifecycle/MediatorLiveData$Source;->unplug()V
HSPLandroidx/lifecycle/MediatorLiveData;-><init>()V
HSPLandroidx/lifecycle/MediatorLiveData;->addSource(Landroidx/lifecycle/LiveData;Landroidx/lifecycle/Observer;)V
HSPLandroidx/lifecycle/MediatorLiveData;->onActive()V
HSPLandroidx/lifecycle/MediatorLiveData;->onInactive()V
HSPLandroidx/lifecycle/MediatorLiveData;->removeSource(Landroidx/lifecycle/LiveData;)V
HSPLandroidx/lifecycle/MutableLiveData;-><init>()V
HSPLandroidx/lifecycle/MutableLiveData;->setValue(Ljava/lang/Object;)V
PLandroidx/lifecycle/LiveData$LifecycleBoundObserver;->detachObserver()V
