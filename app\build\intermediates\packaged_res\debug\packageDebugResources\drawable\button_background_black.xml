<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#444444" />
            <corners android:radius="8dp" />
            <stroke android:width="1dp" android:color="#666666" />
        </shape>
    </item>
    
    <!-- 正常状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#333333" />
            <corners android:radius="8dp" />
            <stroke android:width="1dp" android:color="#555555" />
        </shape>
    </item>
</selector>
