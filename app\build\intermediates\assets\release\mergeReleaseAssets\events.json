{"events": [{"id": "day1_start", "text": "刺骨的寒风把你从昏迷中唤醒。你挣扎着睁开眼，发现自己正躺在一间破旧的小木屋里。屋外，暴风雪的呼啸声如同怪物的嘶吼。你检查了一下自己的状况，必须立刻想办法生火！", "choices": [{"text": "劈开旧家具生火", "effects": {"stamina": -15, "firewood": 6, "cabin_integrity": -8}, "resultText": "你用墙角的斧头劈开了一把摇摇欲坠的椅子，冰冷的木屋里终于有了一丝暖意。虽然破坏了一些家具，但现在生存更重要。", "nextEventId": "day1_evening"}, {"text": "冒着风雪出去找柴火", "effects": {"warmth": -25, "stamina": -25, "firewood": 10}, "resultText": "你推开门，几乎被风雪掀翻。你在及膝深的雪地里艰难地收集了一些干树枝，冻僵的手指已经失去了知觉。但你收集到了更多的柴火。", "nextEventId": "day1_evening"}, {"text": "寻找其他热源", "effects": {"stamina": -10, "hope": -5}, "resultText": "你在小屋里四处寻找其他可能的热源，但除了壁炉外什么都没找到。这让你感到有些沮丧，但至少没有浪费太多体力。", "nextEventId": "day1_evening"}]}, {"id": "day1_evening", "text": "黄昏时分，橘红色的火光在壁炉中跳跃着。你感到一阵疲惫，但至少现在有了温暖。夜晚即将来临，你需要为漫长的黑夜做准备。", "choices": [{"text": "加固房屋抵御风雪", "effects": {"stamina": -20, "firewood": -5, "cabin_integrity": 20}, "requirements": {"firewood": 5, "stamina": 20}, "resultText": "你用一些木柴和找到的破布加固了窗户和门缝。虽然消耗了一些资源，但小屋变得更加坚固了。", "nextEventId": "day1_night"}, {"text": "早点休息保存体力", "effects": {"hope": 5}, "resultText": "你决定早点休息。躺在简陋的床上，听着外面的风雪声，你告诉自己一定要坚持下去。", "nextEventId": "day1_night"}, {"text": "检查小屋的安全隐患", "effects": {"stamina": -10, "hope": 3}, "resultText": "你仔细检查了小屋的每个角落，寻找可能的安全隐患。虽然发现了一些小问题，但总体来说还算安全，这让你稍微安心了一些。", "nextEventId": "day1_night"}]}, {"id": "day1_night", "text": "夜幕降临，小屋外的风雪声更加猛烈。壁炉中的火焰是你唯一的光明和温暖来源。你蜷缩在火炉旁，准备度过这个漫长的夜晚。", "effects": {}, "choices": [{"text": "继续下一天", "effects": {}, "resultText": "经过一夜的煎熬，你终于看到了第二天的曙光...", "nextEventId": "day2_start"}]}, {"id": "day2_start", "text": "火焰在壁炉里噼啪作响，你活过了第一个夜晚。但你知道，这仅仅是开始。你必须为接下来的日子做准备。透过结霜的窗户，你看到外面依然是白茫茫的一片。", "choices": [{"text": "探索小屋寻找物资", "effects": {"stamina": -15}, "resultText": "你仔细搜索小屋的每个角落。", "nextEventId": "day2_search_result"}, {"text": "外出砍柴", "effects": {"warmth": -20, "stamina": -25, "firewood": 10}, "requirements": {"stamina": 25}, "resultText": "你再次冒着严寒外出砍柴。虽然很辛苦，但你成功收集到了更多的燃料。", "nextEventId": "day2_night"}, {"text": "休息以保存体力", "effects": {"hope": -5}, "resultText": "你决定在屋内休息，虽然没有做什么事情，但至少保存了体力。无所事事让你感到有些沮丧。", "nextEventId": "day2_night"}, {"text": "制作简易工具", "effects": {"stamina": -15, "firewood": -2, "hope": 10}, "requirements": {"stamina": 15, "firewood": 2}, "resultText": "你用一些木材制作了简易的工具，这些工具可能在后续的生存中派上用场。虽然消耗了一些资源，但这让你感到更有准备。", "nextEventId": "day2_night", "specialEffects": {"hasTools": true}}]}, {"id": "day2_search_result", "text": "搜索结果", "choices": [], "conditionalEvents": {"cabin_integrity >= 50": "day2_good_search", "cabin_integrity >= 30": "day2_normal_search", "default": "day2_poor_search"}}, {"id": "day2_good_search", "text": "搜索结果\n\n由于小屋保存得相对完好，你在搜索中有了意外收获！在一个隐藏的储物箱里，你发现了一些罐头食品和一瓶烈酒。", "effects": {"food": 4, "hope": 15, "warmth": 10}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day2_night"}]}, {"id": "day2_normal_search", "text": "搜索结果\n\n你在小屋里找到了一些基本物资。虽然不多，但总比没有强。", "effects": {"food": 2, "hope": 8}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day2_night"}]}, {"id": "day2_poor_search", "text": "搜索结果\n\n由于小屋损坏严重，大部分物资都已经被破坏或丢失。你只找到了一些勉强能用的东西。", "effects": {"food": 1, "hope": 3}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day2_night"}]}, {"id": "day3_start", "text": "第三天 - 呼啸的风\n\n风声变得更加尖锐，仿佛要撕裂这间小屋。你感到一阵不安，屋顶的积雪似乎太厚了。", "choices": [{"text": "清理屋顶积雪", "effects": {"warmth": -15, "stamina": -25, "cabin_integrity": 10}, "requirements": {"stamina": 25}, "resultText": "你爬上屋顶清理积雪，虽然很危险，但减轻了屋顶的负担。", "nextEventId": "day3_evening"}, {"text": "尝试捕猎", "effects": {"warmth": -25, "stamina": -25}, "requirements": {"stamina": 25}, "resultText": "你冒着严寒外出捕猎。", "nextEventId": "day3_hunt_result"}, {"text": "收集木柴", "effects": {"stamina": -20, "warmth": -10, "firewood": 6}, "requirements": {"stamina": 20}, "resultText": "你冒着寒风外出收集木柴，虽然很辛苦，但获得了宝贵的燃料。", "nextEventId": "day3_evening"}, {"text": "继续休息", "effects": {"hope": -8}, "resultText": "你选择保存体力，在屋内休息。但无所事事让你感到更加沮丧。", "nextEventId": "day3_evening"}]}, {"id": "day3_hunt_result", "text": "捕猎结果", "choices": [], "conditionalEvents": {"hasTools": "day3_hunt_success", "hope >= 45": "day3_hunt_lucky", "default": "day3_hunt_fail"}}, {"id": "day3_hunt_success", "text": "捕猎结果\n\n由于你之前制作的工具，捕猎变得更加容易！你成功捕获了一只雪兔，获得了丰富的食物。", "effects": {"food": 5, "hope": 15}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day3_evening"}]}, {"id": "day3_hunt_lucky", "text": "捕猎结果\n\n凭借高昂的斗志和一些运气，你成功捕获了一只小动物！", "effects": {"food": 3, "hope": 10}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day3_evening"}]}, {"id": "day3_hunt_fail", "text": "捕猎结果\n\n你在雪地里搜寻了很久，但什么都没有找到。严寒和疲劳让你几乎支撑不住。", "effects": {"hope": -15}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day3_evening"}]}, {"id": "day3_evening", "text": "第三天黄昏，风雪依然猛烈。你需要为即将到来的夜晚做最后的准备。", "choices": [{"text": "制作陷阱准备明天捕猎", "effects": {"stamina": -15, "firewood": -3, "hope": 8}, "requirements": {"stamina": 15, "firewood": 3}, "resultText": "你用木材制作了一个简易陷阱，希望明天能有收获。", "nextEventId": "day3_night", "specialEffects": {"hasTrap": true}}, {"text": "节约资源早点休息", "effects": {"hope": 3}, "resultText": "你决定节约资源，早点休息为明天做准备。", "nextEventId": "day3_night"}]}, {"id": "day3_night", "text": "第三个夜晚来临了。风声更加猛烈，小屋在狂风中摇摆。你紧紧抱着自己，希望能撑过这一夜。外面的暴风雪似乎永远不会停止。", "effects": {}, "choices": [{"text": "继续下一天", "effects": {}, "resultText": "第三夜过去了，你依然活着...", "nextEventId": "day4_start"}]}, {"id": "day3_night_normal", "text": "第三夜过去了，你依然活着...", "choices": [{"text": "继续下一天", "effects": {}, "resultText": "第三夜过去了，你依然活着...", "nextEventId": "day4_start"}]}, {"id": "day4_start", "text": "第四天 - 希望的微光\n\n风雪似乎短暂地减弱了。透过窗户的缝隙，你似乎看到了远处有什么东西在反光。", "choices": [{"text": "前往反光点调查", "effects": {"warmth": -30, "stamina": -30}, "requirements": {"stamina": 30}, "resultText": "你决定冒险外出调查那个反光点。深一脚浅一脚地跋涉过去，你发现那是一架小型飞机的残骸！机舱里一片狼藉。", "nextEventId": "crashed_plane"}, {"text": "寻找其他生存物资", "effects": {"warmth": -20, "stamina": -25}, "requirements": {"stamina": 25}, "resultText": "你决定在附近寻找其他可能的生存物资。", "nextEventId": "day4_scavenge_result"}, {"text": "这是陷阱，待在屋里", "effects": {"hope": -8}, "resultText": "你觉得这可能是陷阱，选择待在屋里。虽然安全，但希望也在减少。", "nextEventId": "day4_indoor_event"}]}, {"id": "day4_trap_result", "text": "陷阱检查结果", "choices": [], "conditionalEvents": {"hope >= 50": "day4_trap_success", "default": "day4_trap_empty"}}, {"id": "day4_trap_success", "text": "陷阱检查结果\n\n太好了！陷阱抓到了一只小动物！这给了你继续生存的希望。", "effects": {"food": 3, "hope": 12}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day4_night"}]}, {"id": "day4_trap_empty", "text": "陷阱检查结果\n\n陷阱是空的，看起来没有动物经过这里。有些失望，但你可以重新设置它。", "choices": [{"text": "重新设置陷阱", "effects": {"stamina": -10, "hope": 5}, "resultText": "你重新设置了陷阱，希望明天会有更好的运气。", "nextEventId": "day4_night", "specialEffects": {"hasTrap": true}}, {"text": "放弃陷阱", "effects": {"hope": -3}, "resultText": "你决定放弃陷阱，专注于其他生存方式。", "nextEventId": "day4_night"}]}, {"id": "day4_indoor_event", "text": "你决定待在室内，但突然听到了奇怪的声音...", "choices": [], "conditionalEvents": {"cabin_integrity < 40": "roof_leak", "default": "day4_false_alarm"}}, {"id": "day4_false_alarm", "text": "虚惊一场，但这提醒你要时刻保持警惕。", "effects": {"hope": 3}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day4_evening"}]}, {"id": "day4_scavenge_result", "text": "搜寻物资的结果", "choices": [], "conditionalEvents": {"hope >= 40": "day4_scavenge_success", "hope >= 25": "day4_scavenge_partial", "default": "day4_scavenge_fail"}}, {"id": "day4_scavenge_success", "text": "搜寻物资的结果\n\n凭借坚定的意志，你在雪地里找到了一些有用的物资！", "effects": {"food": 2, "firewood": 5, "hope": 8}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day4_evening"}]}, {"id": "day4_scavenge_partial", "text": "搜寻物资的结果\n\n你找到了一些基本物资，虽然不多但总比没有强。", "effects": {"food": 1, "firewood": 3, "hope": 3}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day4_evening"}]}, {"id": "day4_scavenge_fail", "text": "搜寻物资的结果\n\n你什么都没找到，白白消耗了体力和体温。沮丧的情绪开始蔓延。", "effects": {"hope": -8}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day4_evening"}]}, {"id": "day4_evening", "text": "第四天黄昏，你需要为夜晚做准备。今天的经历让你对明天有了新的计划。", "choices": [{"text": "准备明天的行动", "effects": {"stamina": -5, "hope": 5}, "resultText": "你仔细规划明天的行动，这让你感到更有准备。", "nextEventId": "day4_night"}, {"text": "保存体力", "effects": {}, "resultText": "你决定保存体力，为明天的挑战做准备。", "nextEventId": "day4_night"}, {"text": "检查昨天的陷阱", "effects": {"warmth": -10, "stamina": -15}, "requirements": {"hasTrap": true, "stamina": 15}, "resultText": "你去检查昨天设置的陷阱。", "nextEventId": "day4_trap_result"}]}, {"id": "crashed_plane", "text": "坠毁的飞机\n\n你深一脚浅一脚地跋涉过去，发现那是一架小型飞机的残骸！机舱里一片狼藉。", "choices": [{"text": "搜索驾驶舱", "effects": {}, "resultText": "你仔细搜索驾驶舱，在座椅下面找到了一把信号枪！这可能是你获救的关键。", "specialItemReward": "signal_gun", "nextEventId": "day4_night_with_gun"}, {"text": "搜索货仓", "effects": {"food": 3}, "resultText": "你搜索了货仓，发现了一些应急食品！虽然包装有些破损，但还是可以食用的。", "nextEventId": "day4_night_no_gun"}]}, {"id": "day4_night_with_gun", "text": "第四个夜晚。今天的发现让你对明天充满了期待，但现在你必须先撑过这一夜。你握着手中的信号枪，这给了你一丝希望。", "effects": {}, "choices": [{"text": "继续下一天", "effects": {}, "resultText": "第四夜过去了，你离救援更近了...", "nextEventId": "day5_start"}]}, {"id": "day4_night_no_gun", "text": "第四个夜晚。今天的发现给了你一些物资，但现在你必须先撑过这一夜。虽然没有找到求救工具，但至少有了更多的食物。", "effects": {}, "choices": [{"text": "继续下一天", "effects": {}, "resultText": "第四夜过去了，你继续坚持着...", "nextEventId": "day5_start"}]}, {"id": "day4_night", "text": "第四个夜晚。今天的经历让你对生存有了新的认识，但现在你必须先撑过这一夜。", "effects": {}, "choices": [{"text": "继续下一天", "effects": {}, "resultText": "第四夜过去了，你依然在坚持...", "nextEventId": "day5_start"}]}, {"id": "day2_night", "text": "第二个夜晚来临了。你已经更加熟悉这间小屋，也更加了解如何在这里生存。壁炉中的火焰依然温暖，但你知道每一夜都是对生存意志的考验。", "effects": {}, "choices": [{"text": "继续下一天", "effects": {}, "resultText": "夜晚平静地过去了。又一个夜晚过去了，你离救援又近了一步...", "nextEventId": "day3_start"}]}, {"id": "day2_night_normal", "text": "夜晚平静地过去了。又一个夜晚过去了，你离救援又近了一步...", "choices": [{"text": "继续下一天", "effects": {}, "resultText": "又一个夜晚过去了，你离救援又近了一步...", "nextEventId": "day3_start"}]}, {"id": "day5_start", "text": "第五天 - 绝望\n\n风雪再次加强，比以往任何时候都要猛烈。小屋在狂风中颤抖，你感到一阵深深的绝望。", "choices": [{"text": "检查房屋状况", "effects": {"stamina": -15}, "resultText": "你仔细检查了房屋的状况...", "nextEventId": "house_check_result"}, {"text": "蜷缩在壁炉边", "effects": {"hope": -5}, "resultText": "你选择蜷缩在壁炉边取暖，虽然身体暖和了一些，但心情更加沮丧。", "nextEventId": "day5_evening"}]}, {"id": "day5_desperate_search", "text": "绝望的搜寻", "choices": [], "conditionalEvents": {"hope >= 60": "day5_find_shelter", "hope >= 35": "day5_find_supplies", "default": "day5_nothing_found"}}, {"id": "day5_find_shelter", "text": "你发现了一个废弃的猎人小屋！这给了你新的希望和选择。", "effects": {"hope": 25, "cabin_integrity": 20}, "choices": [{"text": "立即搬到新小屋", "effects": {"stamina": -20, "hope": 30, "firewood": 6, "food": 2}, "resultText": "你决定立即搬到新发现的小屋。虽然搬迁很累，但新环境让你感到更安全。新小屋里还有一些之前主人留下的木柴和食物！", "nextEventId": "day5_night", "specialEffects": {"hasBackupShelter": true, "inBackupShelter": true}}, {"text": "记住位置，暂时回去", "effects": {"hope": 5}, "resultText": "你记住了小屋的位置，决定先回到原来的地方。至少现在有了备选方案。", "nextEventId": "day5_night", "specialEffects": {"hasBackupShelter": true}}]}, {"id": "day5_find_supplies", "text": "意外的收获！这些补给品让你的生存状况大大改善。", "effects": {"food": 2, "firewood": 6, "hope": 15}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day5_night"}]}, {"id": "day5_nothing_found", "text": "一无所获的搜寻让你更加绝望，但至少你还在努力。", "effects": {"hope": -12}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day5_night"}]}, {"id": "day5_evening", "text": "第五天黄昏，你需要做出一些重要的决定来度过接下来的困难时期。", "choices": [{"text": "制定详细的生存计划", "effects": {"stamina": -10, "hope": 10}, "requirements": {"hope": 30}, "resultText": "你冷静地制定了详细的生存计划，这让你感到更有控制力。", "nextEventId": "day5_night"}, {"text": "休息", "effects": {"hope": 5}, "resultText": "你决定严格节约所有资源，为最坏的情况做准备。", "nextEventId": "day5_night"}, {"text": "强迫自己外出寻找希望", "effects": {"warmth": -30, "stamina": -30}, "requirements": {"stamina": 30, "hope": 20}, "resultText": "你强迫自己走出小屋，在暴风雪中寻找任何可能的希望...", "nextEventId": "day5_desperate_search"}]}, {"id": "house_check_result", "text": "检查房屋状况结果", "choices": [], "conditionalEvents": {"cabin_integrity < 50": "roof_leak", "default": "house_ok"}}, {"id": "house_ok", "text": "检查房屋状况结果\n\n幸运的是，房屋状况还算良好，没有发现严重的问题。", "choices": [{"text": "继续", "effects": {}, "nextEventId": "day5_evening"}]}, {"id": "roof_leak", "text": "检查房屋状况结果\n\n屋顶的一角开始漏下冰冷的雪水，很快就会让你的体温迅速流失！", "effects": {"cabin_integrity": -25, "warmth": -15, "hope": -10}, "choices": [{"text": "立刻修复", "effects": {"stamina": -20, "firewood": -10, "cabin_integrity": 15}, "requirements": {"stamina": 20, "firewood": 10}, "resultText": "你立刻动手修复屋顶，虽然消耗了大量体力和木柴，但阻止了进一步的损坏。", "nextEventId": "day5_evening"}, {"text": "暂时不管", "effects": {"hope": -10}, "resultText": "你决定暂时不管漏雪的问题，但这意味着每晚你都会损失更多体温。", "nextEventId": "day5_evening", "specialEffects": {"roofLeaking": true}}]}, {"id": "day5_night", "text": "第五个夜晚。风雪依然猛烈，但你的意志依然坚强。", "effects": {}, "choices": [{"text": "继续生存之旅", "effects": {}, "resultText": "第五夜过去了，你还在坚持...", "nextEventId": "day6_start"}]}, {"id": "day5_night_normal", "text": "第五夜过去了，你还在坚持...", "choices": [{"text": "继续生存之旅", "effects": {}, "resultText": "第五夜过去了，你还在坚持...", "nextEventId": "day6_start"}]}, {"id": "day6_start", "text": "第六天 - 最后的机会", "choices": [], "conditionalEvents": {"inBackupShelter": "day6_in_backup_shelter", "hasSignalGun": "day6_with_signal_gun", "firewood >= 10": "day6_resource_rich", "default": "day6_resource_poor"}}, {"id": "day6_in_backup_shelter", "text": "第六天 - 最后的机会\n\n你已经在备用小屋中了，这里比原来的地方更加安全舒适。这可能是最后一个机会了。", "choices": [{"text": "保持乐观", "effects": {"hope": 15}, "resultText": "在相对安全的备用小屋中，你更容易保持乐观的心态。", "nextEventId": "day6_evening"}, {"text": "最后的搜寻", "effects": {"firewood": 5, "food": 2, "stamina": -10}, "requirements": {"stamina": 10}, "resultText": "你在备用小屋周围进行搜寻，找到了一些额外的物资。", "nextEventId": "day6_evening"}, {"text": "发射信号枪", "effects": {"hope": 50}, "requirements": {"specialItems": ["signal_gun"]}, "resultText": "你向天空发射了信号弹！红色的光芒划破天际，这是你最后的希望！现在只能等待救援队的到来...", "specialItemUsed": "signal_gun", "nextEventId": "day6_evening", "specialEffects": {"signalFired": true}}, {"text": "精心制作求救器", "effects": {"firewood": -12, "food": -4, "hope": 20}, "requirements": {"firewood": 12, "food": 4}, "resultText": "利用备用小屋的工具和材料，你制作了一个更精良的求救装置。", "nextEventId": "day6_evening", "specialEffects": {"hasRescueDevice": true, "rescuePoints": 80}}]}, {"id": "day6_with_signal_gun", "text": "第六天 - 最后的机会\n\n这可能是最后一个机会了。你握着手中的信号枪，这是你最大的希望。", "choices": [{"text": "保持乐观", "effects": {"hope": 12}, "resultText": "你努力保持乐观的心态，相信救援一定会到来。", "nextEventId": "day6_evening"}, {"text": "最后的搜寻", "effects": {"firewood": 3, "food": 1, "stamina": -10}, "requirements": {"stamina": 10}, "resultText": "你进行了最后一次搜寻，找到了一些有用的物资。", "nextEventId": "day6_evening"}, {"text": "发射信号枪", "effects": {"hope": 50}, "requirements": {"specialItems": ["signal_gun"]}, "resultText": "你向天空发射了信号弹！红色的光芒划破天际，这是你最后的希望！现在只能等待救援队的到来...", "specialItemUsed": "signal_gun", "nextEventId": "day6_evening", "specialEffects": {"signalFired": true}}, {"text": "转移到备用小屋", "effects": {"stamina": -30, "hope": 20, "firewood": 8, "food": 3}, "requirements": {"hasBackupShelter": true, "inBackupShelter": false, "stamina": 30}, "resultText": "你决定转移到之前发现的备用小屋，这可能是最后的机会了。新小屋里还有一些之前主人留下的木柴和食物！", "nextEventId": "day6_evening", "specialEffects": {"inBackupShelter": true}}]}, {"id": "day6_resource_rich", "text": "第六天 - 最后的机会\n\n你的物资还算充足，这给了你更多的选择。这可能是最后一个机会了。", "choices": [{"text": "坚定意志", "effects": {"hope": 20, "stamina": -5}, "requirements": {"stamina": 5}, "resultText": "你坚定自己的意志，相信一定能坚持到最后。", "nextEventId": "day6_evening"}, {"text": "充实物资储备", "effects": {"firewood": 8, "food": 3, "stamina": -25}, "requirements": {"stamina": 25}, "resultText": "你花费大量体力收集和整理物资，为最后的时刻做准备。", "nextEventId": "day6_evening"}, {"text": "精心制作求救器", "effects": {"firewood": -15, "food": -5, "hope": 15}, "requirements": {"firewood": 15, "food": 5}, "resultText": "你用大量物资精心制作了一个复杂的求救装置。", "nextEventId": "day6_evening", "specialEffects": {"hasRescueDevice": true, "rescuePoints": 70}}, {"text": "转移到备用小屋", "effects": {"stamina": -30, "hope": 20, "firewood": 8, "food": 3}, "requirements": {"hasBackupShelter": true, "inBackupShelter": false, "stamina": 30}, "resultText": "你决定转移到之前发现的备用小屋，这可能是最后的机会了。新小屋里还有一些之前主人留下的木柴和食物！", "nextEventId": "day6_evening", "specialEffects": {"inBackupShelter": true}}]}, {"id": "day6_resource_poor", "text": "第六天 - 最后的机会\n\n物资已经很匮乏了，但你不能放弃。这可能是最后一个机会了。", "choices": [{"text": "保持乐观", "effects": {"hope": 12}, "resultText": "即使在困境中，你依然努力保持乐观的心态。", "nextEventId": "day6_evening"}, {"text": "最后的搜寻", "effects": {"firewood": 3, "food": 1, "stamina": -10}, "requirements": {"stamina": 10}, "resultText": "你进行了最后一次彻底的搜寻，找到了一些勉强能用的物资。", "nextEventId": "day6_evening"}, {"text": "孤注一掷的信号", "effects": {"firewood": -5, "food": -2, "hope": 8}, "requirements": {"firewood": 5, "food": 2}, "resultText": "你用仅有的物资制作了一个简陋的求救信号，这是孤注一掷的尝试。", "nextEventId": "day6_evening", "specialEffects": {"hasRescueDevice": true, "rescuePoints": 30}}, {"text": "转移到备用小屋", "effects": {"stamina": -30, "hope": 20, "firewood": 8, "food": 3}, "requirements": {"hasBackupShelter": true, "inBackupShelter": false, "stamina": 30}, "resultText": "你决定转移到之前发现的备用小屋，这可能是最后的机会了。新小屋里还有一些之前主人留下的木柴和食物！", "nextEventId": "day6_evening", "specialEffects": {"inBackupShelter": true}}]}, {"id": "day6_evening", "text": "第六天黄昏\n\n太阳即将落下，这可能是你看到的最后一次日落。但你不会放弃。夜晚即将来临，你必须做好准备。", "effects": {}, "choices": [{"text": "节约体力静静等待", "effects": {"hope": -5}, "resultText": "你选择静静地坐着，节约体力。虽然保存了力气，但内心的焦虑在增长。", "nextEventId": "day6_night"}, {"text": "向外面大声呼救", "effects": {"stamina": -15, "hope": 10}, "resultText": "你打开门向外面大声呼救，虽然消耗了体力，但这让你感到至少还在努力争取生存。", "nextEventId": "day6_night"}, {"text": "制作紧急求救标志", "effects": {"stamina": -20, "firewood": -8, "hope": 20}, "requirements": {"hope": 60, "stamina": 20, "firewood": 8}, "resultText": "凭借高昂的斗志，你用木柴在雪地上制作了一个巨大的SOS标志。这个创意给了你巨大的希望！", "nextEventId": "day6_night"}]}, {"id": "day6_night", "text": "第六个夜晚。这可能是最后一个夜晚了。你已经坚持了这么久，无论如何都要撑过去。外面的风雪依然猛烈，但你的意志比任何暴风雪都要坚强。", "effects": {}, "choices": [{"text": "继续下一天", "effects": {}, "resultText": "第六夜过去了，你奇迹般地活了下来...", "nextEventId": "day7_start"}]}, {"id": "day7_start", "text": "第七天 - 最后的坚持", "choices": [], "conditionalEvents": {"signalFired": "day7_signal_fired", "hasSignalGun": "day7_with_signal_gun", "hasRescueDevice": "day7_with_rescue_device", "default": "day7_basic"}}, {"id": "day7_signal_fired", "text": "第七天 - 等待救援\n\n昨天发射的信号弹给了你巨大的希望。你相信救援队一定看到了信号，现在只需要耐心等待。", "choices": [{"text": "保持希望等待", "effects": {"hope": 20}, "resultText": "你满怀希望地等待着救援的到来，相信很快就会有人来救你。", "nextEventId": "day7_signal_fired_ending"}, {"text": "节约使用资源", "effects": {"firewood": 2, "food": 1}, "resultText": "你小心地节约使用剩余的资源。", "nextEventId": "day7_signal_fired_ending"}, {"text": "准备迎接救援", "effects": {"hope": 10}, "resultText": "你选择节约体力，静静等待救援的到来。", "nextEventId": "day7_signal_fired_ending"}]}, {"id": "day7_with_signal_gun", "text": "第七天 - 最后的坚持\n\n最后一天了。你握着手中的信号枪，这是你最后的王牌。", "choices": [{"text": "心理暗示", "effects": {"hope": 15}, "resultText": "你通过心理暗示让自己保持冷静和希望。", "nextEventId": "day7_night"}, {"text": "节约使用资源", "effects": {"firewood": 2, "food": 1}, "resultText": "你小心地节约使用剩余的资源。", "nextEventId": "day7_night"}, {"text": "发射信号枪", "effects": {"hope": 50}, "requirements": {"specialItems": ["signal_gun"]}, "resultText": "你向天空发射了信号弹！红色的光芒划破天际，这是你最后的希望！", "specialItemUsed": "signal_gun", "nextEventId": "rescue_ending"}]}, {"id": "day7_with_rescue_device", "text": "第七天 - 最后的坚持\n\n最后一天了。你之前制作的求救装置给了你一丝希望。", "choices": [{"text": "坚定等待救援", "effects": {"hope": 30}, "resultText": "你坚定地相信救援会到来，之前的准备没有白费。", "nextEventId": "day7_night"}, {"text": "补充应急物资", "effects": {"firewood": 5, "food": 2}, "resultText": "你补充了一些应急物资，为最后的时刻做准备。", "nextEventId": "day7_night"}, {"text": "激活求救装置", "effects": {"hope": 20}, "resultText": "你激活了之前制作的求救装置，希望它能发挥作用。", "nextEventId": "day7_rescue_attempt"}]}, {"id": "day7_basic", "text": "第七天 - 最后的坚持\n\n最后一天了。你只能依靠自己的意志坚持下去。", "choices": [{"text": "心理暗示", "effects": {"hope": 15}, "resultText": "你通过心理暗示让自己保持冷静和希望。", "nextEventId": "day7_night"}, {"text": "节约使用资源", "effects": {"firewood": 2, "food": 1}, "resultText": "你小心地节约使用剩余的资源。", "nextEventId": "day7_night"}, {"text": "简单求救尝试", "effects": {"firewood": -8, "food": -3, "hope": 10}, "requirements": {"firewood": 8, "food": 3}, "resultText": "你用剩余的物资进行了最后的求救尝试。", "nextEventId": "day7_rescue_attempt", "specialEffects": {"rescuePoints": 20}}]}, {"id": "day7_rescue_attempt", "text": "求救尝试结果", "choices": [], "conditionalEvents": {"rescuePoints >= 70": "special_rescue_ending", "rescuePoints >= 30": "partial_rescue_ending", "default": "day7_night"}}, {"id": "day7_signal_fired_ending", "text": "第七个夜晚 - 等待奇迹\n\n这是最后一个夜晚了。你已经发射了信号弹，现在只能等待救援的到来。外面的风雪依然猛烈，但你心中充满希望。", "effects": {}, "choices": [{"text": "继续等待救援", "effects": {}, "resultText": "你耐心地等待着救援的到来。几个小时后，远方传来了直升机的声音...", "nextEventId": "signal_gun_rescue_ending"}]}, {"id": "day7_night", "text": "第七个夜晚。你已经在这里生存了一周。无论结果如何，你都已经证明了自己的坚强。这是最后的考验。", "effects": {}, "choices": [{"text": "制作求救信号", "effects": {"stamina": -15, "firewood": -5, "hope": 15}, "requirements": {"stamina": 15, "firewood": 5}, "resultText": "你用剩余的木柴制作了一个大型的求救信号，在雪地上摆成SOS的形状。这消耗了你的体力，但给了你巨大的希望。第七夜过去了，你依然在等待...", "nextEventId": "survival_ending"}, {"text": "燃烧所有木柴取暖", "effects": {"firewood": -10, "warmth": 20, "hope": 15}, "requirements": {"firewood": 10}, "resultText": "你决定燃烧剩余的所有木柴，让火焰燃烧得更旺。温暖的火光照亮了整个小屋，也给了你继续坚持的勇气。第七夜过去了，你依然在等待...", "nextEventId": "survival_ending"}, {"text": "保存体力坚持到底", "effects": {"stamina": -5, "hope": 8}, "resultText": "你决定保存体力，静静等待。你咬紧牙关，告诉自己无论如何都要坚持到底。这种坚定的意志支撑着你度过了最后的夜晚。第七夜过去了，你依然在等待...", "nextEventId": "survival_ending"}]}, {"id": "special_rescue_ending", "text": "完美求救结局\n\n你精心制作的求救装置发挥了巨大作用！几个小时后，搜救队准确地找到了你的位置。\n\n\"太好了！我们收到了你的求救信号！\"救援队长兴奋地说道。你的智慧和坚持创造了完美的求救奇迹！\n\n在这个被遗忘的雪山角落，你不仅生存了下来，还展现了人类的智慧和创造力！", "choices": [{"text": "再次挑战", "effects": {}, "nextEventId": "day1_start"}, {"text": "离开雪山", "effects": {}, "nextEventId": "day1_start"}]}, {"id": "partial_rescue_ending", "text": "幸运求救结局\n\n虽然你的求救装置很简陋，但幸运女神眷顾了你！一架路过的飞机恰好看到了你的求救信号。\n\n几个小时后，救援直升机降落在你的面前。\"我们差点就错过了你的信号！\"飞行员说道。\n\n有时候，坚持和一点点运气就能创造奇迹！你成功获救了！", "choices": [{"text": "再次挑战", "effects": {}, "nextEventId": "day1_start"}, {"text": "离开雪山", "effects": {}, "nextEventId": "day1_start"}]}, {"id": "signal_gun_rescue_ending", "text": "信号枪救援结局\n\n经过漫长的等待，你昨天发射的信号弹终于发挥了作用！\n\n清晨时分，远方传来了直升机螺旋桨的轰鸣声。你激动地冲出小屋，在雪地中疯狂地挥舞着双手。\n\n\"我们看到了你的信号弹！\"救援队长大声喊道，\"昨天的红色信号帮我们确定了你的位置！\"\n\n你的耐心等待得到了回报！信号枪在关键时刻拯救了你的生命！", "choices": [{"text": "再次挑战", "effects": {}, "nextEventId": "day1_start"}, {"text": "离开雪山", "effects": {}, "nextEventId": "day1_start"}]}, {"id": "rescue_ending", "text": "救援结局\n\n红色的信号弹在灰暗的天空中绽放，如同希望之花般绚烂。几个小时后，远方传来了直升机螺旋桨的轰鸣声！\n\n你激动地冲出小屋，在雪地中疯狂地挥舞着双手。直升机盘旋着降落，救援队员跳下来向你跑来。\n\n\"我们找到你了！\"队员的声音透过风雪传来。你终于获救了！在这个被遗忘的雪山角落，你用智慧和勇气创造了生存的奇迹！", "choices": [{"text": "再次挑战", "effects": {}, "nextEventId": "day1_start"}, {"text": "离开雪山", "effects": {}, "nextEventId": "day1_start"}]}, {"id": "survival_ending", "text": "生存结局判定", "choices": [], "conditionalEvents": {"hope <= 20": "desperate_ending", "cabin_integrity <= 20": "shelter_collapse_ending", "hope >= 70": "survival_ending_high_hope", "default": "normal_survival_ending"}}, {"id": "survival_ending_high_hope", "text": "高希望值结局判定", "choices": [], "conditionalEvents": {"warmth >= 60": "survival_ending_check_stamina", "default": "normal_survival_ending"}}, {"id": "survival_ending_check_stamina", "text": "体温充足结局判定", "choices": [], "conditionalEvents": {"stamina >= 60": "perfect_survival_ending", "default": "normal_survival_ending"}}, {"id": "normal_survival_ending", "text": "生存结局\n\n你在雪山中坚持了七天七夜。虽然救援还没有到来，但你证明了人类的坚韧不拔。故事还在继续...", "choices": [{"text": "再次挑战", "effects": {}, "nextEventId": "day1_start"}, {"text": "离开雪山", "effects": {}, "nextEventId": "day1_start"}]}, {"id": "perfect_survival_ending", "text": "完美生存结局\n\n你不仅在雪山中坚持了七天，还保持了良好的身体和精神状态。你的智慧和勇气让你成为了真正的生存专家！", "choices": [{"text": "再次挑战", "effects": {}, "nextEventId": "day1_start"}, {"text": "离开雪山", "effects": {}, "nextEventId": "day1_start"}]}, {"id": "desperate_ending", "text": "绝望结局\n\n虽然你坚持了七天，但绝望已经吞噬了你的内心。你勉强活着，但已经失去了对未来的希望...", "choices": [{"text": "再次挑战", "effects": {}, "nextEventId": "day1_start"}, {"text": "离开雪山", "effects": {}, "nextEventId": "day1_start"}]}, {"id": "shelter_collapse_ending", "text": "避难所崩塌结局\n\n小屋最终无法承受暴风雪的摧残而倒塌。你在废墟中艰难求生，但最终还是坚持到了第七天...", "choices": [{"text": "再次挑战", "effects": {}, "nextEventId": "day1_start"}, {"text": "离开雪山", "effects": {}, "nextEventId": "day1_start"}]}, {"id": "death_ending_cold", "text": "冻死结局\n\n你的体温已经降到了危险的临界点。寒冷侵蚀着你的身体，意识逐渐模糊。在这个雪山小屋里，你的生命之火终于熄灭了...", "choices": [{"text": "再次挑战", "effects": {}, "nextEventId": "day1_start"}, {"text": "离开雪山", "effects": {}, "nextEventId": "day1_start"}]}, {"id": "death_ending_exhaustion", "text": "体力耗尽结局\n\n你的身体已经承受不了更多的折磨。连续的劳累和恶劣的环境让你的体力彻底透支。你倒在了小屋里，再也无法站起来...", "choices": [{"text": "再次挑战", "effects": {}, "nextEventId": "day1_start"}, {"text": "离开雪山", "effects": {}, "nextEventId": "day1_start"}]}, {"id": "death_ending_despair", "text": "绝望结局\n\n绝望如潮水般吞噬了你的内心。在这个与世隔绝的地方，你失去了继续生存的意志。黑暗最终战胜了光明...", "choices": [{"text": "再次挑战", "effects": {}, "nextEventId": "day1_start"}, {"text": "离开雪山", "effects": {}, "nextEventId": "day1_start"}]}, {"id": "continue_current_day", "text": "继续当前进度", "choices": [], "conditionalEvents": {"currentDay == 1": "day1_start", "currentDay == 2": "day2_start", "currentDay == 3": "day3_start", "currentDay == 4": "day4_start", "currentDay == 5": "day5_start", "currentDay == 6": "day6_start", "currentDay == 7": "day7_start", "default": "day1_start"}}, {"id": "continue_night", "text": "夜晚继续", "choices": [], "conditionalEvents": {"currentDay == 1": "day1_night", "currentDay == 2": "day2_night_normal", "currentDay == 3": "day3_night_normal", "currentDay == 4": "day4_night", "currentDay == 5": "day5_night_normal", "currentDay == 6": "day6_night", "currentDay == 7": "day7_night", "default": "day2_night_normal"}}]}