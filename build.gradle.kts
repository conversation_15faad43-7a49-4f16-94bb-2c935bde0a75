// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        google()
        mavenCentral()
        // 添加示例项目中的仓库配置
        maven { url = uri("https://jitpack.io") }
        
        //bidmachine
        maven { url = uri("https://artifactory.bidmachine.io/bidmachine") }
        //ironsource
        maven { url = uri("https://android-sdk.is.com") }
        //mintegral
        maven { url = uri("https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_oversea") }
        //pangle
        maven { url = uri("https://artifact.bytedance.com/repository/pangle") }
        //Anythink(Core)
        maven {
            url = uri("https://jfrog.anythinktech.com/artifactory/overseas_sdk")
        }
        // adSet 国外版本
        maven {
            isAllowInsecureProtocol = true
            url = uri("http://maven.shenshiads.com/nexus/repository/adset/")
        }
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        // 添加示例项目中的仓库配置
        maven { url = uri("https://jitpack.io") }
        
        //bidmachine
        maven { url = uri("https://artifactory.bidmachine.io/bidmachine") }
        //ironsource
        maven { url = uri("https://android-sdk.is.com") }
        //mintegral
        maven { url = uri("https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_oversea") }
        //pangle
        maven { url = uri("https://artifact.bytedance.com/repository/pangle") }
        //Anythink(Core)
        maven {
            url = uri("https://jfrog.anythinktech.com/artifactory/overseas_sdk")
        }
        // adSet 国外版本
        maven {
            isAllowInsecureProtocol = true
            url = uri("http://maven.shenshiads.com/nexus/repository/adset/")
        }
    }
}

plugins {
    id("com.android.application") version "8.7.2" apply false
    id("org.jetbrains.kotlin.android") version "2.0.21" apply false
}
