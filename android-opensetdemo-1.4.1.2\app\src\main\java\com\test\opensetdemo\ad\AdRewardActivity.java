package com.test.opensetdemo.ad;

import android.util.Log;
import android.widget.Button;

import com.kc.openset.ad.listener.OSETRewardListener;
import com.kc.openset.ad.reward.OSETRewardVideo;
import com.test.opensetdemo.BaseActivity;
import com.test.opensetdemo.Common;
import com.test.opensetdemo.R;

public class AdRewardActivity extends BaseActivity {

    private static final String TAG = "ssad-AdRewardActivity";

    private Button btnPreload;

    @Override
    public int getLayoutId() {
        return R.layout.activity_reward;
    }

    @Override
    public void initView() {
        initTitle("激励视频");
        btnPreload = findViewById(R.id.btn_preload);

        //这一步建议在首页进行初始化并开启缓存,减少第一次展示广告的时间。并且在首页onDestroy里面调用destroy()方法释放资源
        OSETRewardVideo.getInstance()
                .setContext(this)
                .setPosId(Common.POS_ID_RewardVideo)
                .setUserId("aaaa")
                .startLoad();

        btnPreload.setOnClickListener(v -> {
            OSETRewardVideo.getInstance().showAd(this,osetVideoListener);
        });

    }

    private final static OSETRewardListener osetVideoListener = new OSETRewardListener() {

        @Override
        public void onError(String s, String s1) {
            Log.d(TAG, "onError");

        }

        @Override
        public void onShow(String s) {
            Log.d(TAG, "onShow");
        }

        @Override
        public void onClick() {
            Log.d(TAG, "onClick");
        }

        @Override
        public void onClose(String s) {
            Log.d(TAG, "onClose");
        }

        @Override
        public void onVideoEnd(String s) {
            Log.d(TAG, "onVideoEnd");
        }

        @Override
        public void onVideoStart() {
            Log.d(TAG, "onVideoStart");
        }

        @Override
        public void onReward(String s, double v) {
            Log.d(TAG, "onReward-" + s);
        }

        @Override
        public void onServiceResponse(int i) {

        }
    };

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

}
