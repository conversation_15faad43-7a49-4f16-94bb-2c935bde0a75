{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-mergeReleaseResources-30:/values-de/values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5d5fa9c9e36bc912c1cb51114343e1e1\\transformed\\material-1.9.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,374,463,547,637,719,820,942,1023,1089,1183,1253,1312,1420,1486,1555,1613,1685,1749,1803,1931,1991,2053,2107,2185,2322,2414,2498,2643,2727,2813,2903,2960,3011,3077,3151,3233,3326,3400,3478,3550,3624,3716,3798,3887,3976,4050,4128,4214,4269,4336,4416,4500,4562,4626,4689,4796,4900,4999,5105,5166,5221", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,90,88,83,89,81,100,121,80,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,83,144,83,85,89,56,50,65,73,81,92,73,77,71,73,91,81,88,88,73,77,85,54,66,79,83,61,63,62,106,103,98,105,60,54,81", "endOffsets": "278,369,458,542,632,714,815,937,1018,1084,1178,1248,1307,1415,1481,1550,1608,1680,1744,1798,1926,1986,2048,2102,2180,2317,2409,2493,2638,2722,2808,2898,2955,3006,3072,3146,3228,3321,3395,3473,3545,3619,3711,3793,3882,3971,4045,4123,4209,4264,4331,4411,4495,4557,4621,4684,4791,4895,4994,5100,5161,5216,5298"}, "to": {"startLines": "2,42,43,44,45,46,52,53,56,73,77,89,90,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3809,3900,3989,4073,4163,4653,4754,5040,7843,8096,9489,9559,10817,10925,10991,11060,11118,11190,11254,11308,11436,11496,11558,11612,11690,11827,11919,12003,12148,12232,12318,12408,12465,12516,12582,12656,12738,12831,12905,12983,13055,13129,13221,13303,13392,13481,13555,13633,13719,13774,13841,13921,14005,14067,14131,14194,14301,14405,14504,14610,14671,16322", "endLines": "5,42,43,44,45,46,52,53,56,73,77,89,90,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,176", "endColumns": "12,90,88,83,89,81,100,121,80,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,83,144,83,85,89,56,50,65,73,81,92,73,77,71,73,91,81,88,88,73,77,85,54,66,79,83,61,63,62,106,103,98,105,60,54,81", "endOffsets": "328,3895,3984,4068,4158,4240,4749,4871,5116,7904,8185,9554,9613,10920,10986,11055,11113,11185,11249,11303,11431,11491,11553,11607,11685,11822,11914,11998,12143,12227,12313,12403,12460,12511,12577,12651,12733,12826,12900,12978,13050,13124,13216,13298,13387,13476,13550,13628,13714,13769,13836,13916,14000,14062,14126,14189,14296,14400,14499,14605,14666,14721,16399"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-de\\strings.xml", "from": {"startLines": "97,96,93,92,110,99,98,1,2,60,80,59,81,113,56,154,107,47,106,140,141,139,137,136,144,143,134,133,148,69,71,79,46,45,6,54,55,68,67,44,11,82,63,61,64,62,9,32,17,28,29,25,21,31,19,20,26,23,30,15,16,27,22,18,24,112,83,8,7,86,157,158,75,162,78,161,159,160,76,77,48,151,39,37,40,38,36,35,41,153,152,88,111,100,95,94,101,89,105,104,103,102,70,72,87,53,5,51,12,10,129,130,128,117,118,116,147,121,122,120,125,126,124,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8105,8027,7752,7693,9273,8324,8243,16,68,5071,6856,4996,6948,9466,4870,15705,9210,4404,9151,14346,14711,14239,13703,13595,14924,14836,13044,12941,15280,5821,5947,6790,4348,4269,208,4772,4824,5584,5517,4145,451,7044,5324,5151,5405,5239,356,1840,1023,1628,1681,1468,1236,1786,1138,1188,1518,1351,1733,891,962,1575,1301,1076,1409,9408,7142,307,260,7259,15825,15933,6268,16373,6729,16256,16058,16163,6337,6527,4459,15509,3878,2086,3937,3788,2016,1930,4005,15624,15550,7464,9335,8461,7919,7837,8622,7522,9002,8925,8843,8782,5878,6137,7382,4722,155,4595,788,401,12369,12805,12240,9708,10458,9557,15052,10747,11294,10607,11578,12062,11422,4665", "endColumns": "137,77,84,58,61,136,80,51,56,79,91,74,95,64,92,70,38,54,58,364,123,106,534,107,86,87,549,102,188,56,189,65,55,78,51,51,45,236,66,123,336,97,80,87,83,84,44,53,52,52,51,49,64,53,49,47,56,57,52,70,60,52,49,61,58,57,92,48,46,122,107,124,68,160,60,116,104,92,189,201,104,40,58,1701,67,89,69,85,109,80,73,57,72,160,107,81,159,140,148,76,81,60,68,105,81,49,52,69,74,49,435,105,128,749,147,150,227,546,126,139,483,176,155,56", "endOffsets": "8238,8100,7832,7747,9330,8456,8319,63,120,5146,6943,5066,7039,9526,4958,15771,9244,4454,9205,14706,14830,14341,14233,13698,15006,14919,13589,13039,15464,5873,6132,6851,4399,4343,255,4819,4865,5816,5579,4264,783,7137,5400,5234,5484,5319,396,1889,1071,1676,1728,1513,1296,1835,1183,1231,1570,1404,1781,957,1018,1623,1346,1133,1463,9461,7230,351,302,7377,15928,16053,6332,16529,6785,16368,16158,16251,6522,6724,4559,15545,3932,3783,4000,3873,2081,2011,4110,15700,15619,7517,9403,8617,8022,7914,8777,7658,9146,8997,8920,8838,5942,6238,7459,4767,203,4660,858,446,12800,12906,12364,10453,10601,9703,15275,11289,11416,10742,12057,12234,11573,4717"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,47,48,49,50,51,54,55,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,74,75,76,78,79,80,81,82,83,84,85,86,87,88,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,200,201,202,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3060,3198,3276,3361,3420,3482,3619,3700,3752,4245,4325,4417,4492,4588,4876,4969,5121,5160,5215,5274,5639,5763,5870,6405,6513,6600,6688,7238,7341,7530,7587,7777,7909,7965,8044,8190,8242,8288,8525,8592,8716,9053,9151,9232,9320,9404,9618,9663,9717,9770,9823,9875,9925,9990,10044,10094,10142,10199,10257,10310,10381,10442,10495,10545,10607,10666,10724,14726,14775,14822,14945,15053,15178,15247,15408,15469,15586,15691,15784,15974,16176,16281,16404,16463,18165,18233,18323,18393,18479,18589,18670,18744,18802,18875,19036,19144,19226,19386,19527,19676,19753,19835,19896,19965,20153,20235,20285,20439,20509,20584,20634,21070,21176,21305,22055,22203,22354,22582,23129,23256,23396,23880,24057,24213", "endColumns": "137,77,84,58,61,136,80,51,56,79,91,74,95,64,92,70,38,54,58,364,123,106,534,107,86,87,549,102,188,56,189,65,55,78,51,51,45,236,66,123,336,97,80,87,83,84,44,53,52,52,51,49,64,53,49,47,56,57,52,70,60,52,49,61,58,57,92,48,46,122,107,124,68,160,60,116,104,92,189,201,104,40,58,1701,67,89,69,85,109,80,73,57,72,160,107,81,159,140,148,76,81,60,68,105,81,49,52,69,74,49,435,105,128,749,147,150,227,546,126,139,483,176,155,56", "endOffsets": "3193,3271,3356,3415,3477,3614,3695,3747,3804,4320,4412,4487,4583,4648,4964,5035,5155,5210,5269,5634,5758,5865,6400,6508,6595,6683,7233,7336,7525,7582,7772,7838,7960,8039,8091,8237,8283,8520,8587,8711,9048,9146,9227,9315,9399,9484,9658,9712,9765,9818,9870,9920,9985,10039,10089,10137,10194,10252,10305,10376,10437,10490,10540,10602,10661,10719,10812,14770,14817,14940,15048,15173,15242,15403,15464,15581,15686,15779,15969,16171,16276,16317,16458,18160,18228,18318,18388,18474,18584,18665,18739,18797,18870,19031,19139,19221,19381,19522,19671,19748,19830,19891,19960,20066,20230,20280,20333,20504,20579,20629,21065,21171,21300,22050,22198,22349,22577,23124,23251,23391,23875,24052,24208,24265"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bde3453fac62cb9a4dc37198ed238829\\transformed\\appcompat-1.6.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,2832", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,2909"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,438,536,648,734,840,955,1033,1108,1200,1294,1390,1491,1598,1698,1802,1900,1998,2095,2177,2288,2390,2488,2595,2698,2802,2958,20071", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "433,531,643,729,835,950,1028,1103,1195,1289,1385,1486,1593,1693,1797,1895,1993,2090,2172,2283,2385,2483,2590,2693,2797,2953,3055,20148"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b7d68e85c64c5740c775d2c6236c08f5\\transformed\\core-1.9.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "203", "startColumns": "4", "startOffsets": "20338", "endColumns": "100", "endOffsets": "20434"}}]}]}