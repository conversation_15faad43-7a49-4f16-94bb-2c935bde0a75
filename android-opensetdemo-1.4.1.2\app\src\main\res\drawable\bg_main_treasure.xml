<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="2px"
                android:left="1px"
                android:right="2px"
                android:top="1px" />
            <solid android:color="#01E86767" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="2px"
                android:left="1px"
                android:right="2px"
                android:top="1px" />
            <solid android:color="#02E86767" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="2px"
                android:left="1px"
                android:right="2px"
                android:top="1px" />
            <solid android:color="#03E86767" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="2px"
                android:left="1px"
                android:right="2px"
                android:top="1px" />
            <solid android:color="#04E86767" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="2px"
                android:left="1px"
                android:right="2px"
                android:top="1px" />
            <solid android:color="#05E86767" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="2px"
                android:left="1px"
                android:right="2px"
                android:top="1px" />
            <solid android:color="#06E86767" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="2px"
                android:left="1px"
                android:right="2px"
                android:top="1px" />
            <solid android:color="#07E86767" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="2px"
                android:left="1px"
                android:right="2px"
                android:top="1px" />
            <solid android:color="#08E86767" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="2px"
                android:left="1px"
                android:right="2px"
                android:top="1px" />
            <solid android:color="#09E86767" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="2px"
                android:left="1px"
                android:right="2px"
                android:top="1px" />
            <solid android:color="#10E86767" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#ffE86767" />
            <corners android:radius="8dp" />
            <padding
                android:bottom="20px"
                android:left="10px"
                android:right="20px"
                android:top="10px" />
        </shape>
    </item>
</layer-list>