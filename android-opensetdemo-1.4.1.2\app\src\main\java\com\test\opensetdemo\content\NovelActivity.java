package com.test.opensetdemo.content;

import android.view.View;
import android.widget.Button;

import com.test.opensetdemo.BaseActivity;
import com.test.opensetdemo.R;

public class NovelActivity extends BaseActivity {

    private Button btnShow;

    @Override
    public int getLayoutId() {
        return R.layout.activity_novel;
    }

    @Override
    public void initView() {
        initTitle("小说");
        btnShow = findViewById(R.id.btn_show);
        btnShow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showNovel();
            }
        });
        findViewById(R.id.btn_sign_novel).setOnClickListener(v -> showSingNovelTask());
    }

    private void showSingNovelTask() {
//        OSETRewardVideo.getInstance().load(this, Common.POS_ID_NOVEL_TASK, new OSETVideoListener() {
//            @Override
//            public void onShow() {
//
//            }
//
//            @Override
//            public void onError(String s, String s1) {
//
//            }
//
//            @Override
//            public void onClick() {
//
//            }
//
//            @Override
//            public void onClose(String s) {
//
//            }
//
//            @Override
//            public void onVideoEnd(String s) {
//
//            }
//
//            @Override
//            public void onLoad() {
//                OSETRewardVideo.getInstance().showRewardAd(NovelActivity.this);
//            }
//
//            @Override
//            public void onVideoStart() {
//
//            }
//
//            @Override
//            public void onReward(String s) {
//
//            }
//        });
    }


    private void showNovel() {
//        OSETNovel.getInstance().setUserId("123456");//如果需要回调，要传入userid（服务器端回调）
//        OSETNovel.getInstance().show(activity, Common.POS_ID_NOVELCONTENT);
    }
}
