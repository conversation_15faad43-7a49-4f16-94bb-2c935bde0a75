package com.test.opensetdemo.ad;

import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.Toast;

import com.kc.openset.ad._native.OSETNative;
import com.kc.openset.ad.listener.OSETNativeListener;
import com.test.opensetdemo.BaseActivity;
import com.test.opensetdemo.Common;
import com.test.opensetdemo.R;

public class NativeActivity extends BaseActivity {

    private LinearLayout ll_information;
    private Button btnLoad, btnShow;


    private View nativeView;

    @Override
    public int getLayoutId() {
        return R.layout.activity_information;
    }

    @Override
    public void initView() {
        initTitle("原生信息流");
        ll_information = findViewById(R.id.ll_information);
        btnLoad = findViewById(R.id.btn_load);
        btnShow = findViewById(R.id.btn_show);
        btnShow.setClickable(false);
        btnLoad.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                btnLoad.setClickable(false);
                btnLoad.setBackgroundResource(R.drawable.bg_btn_gray);

                OSETNative.getInstance().setContext(NativeActivity.this).setPosId(Common.POS_ID_INFORMATION).loadAd(NativeActivity.this, myosetNativeListener);
                btnLoad.setClickable(false);
                btnLoad.setBackgroundResource(R.drawable.bg_btn_gray);
            }
        });
        btnShow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                btnShow.setClickable(false);
                btnShow.setBackgroundResource(R.drawable.bg_btn_gray);
                ll_information.removeAllViews();
                if (nativeView != null) {
                    ll_information.addView(nativeView);
                }
//                informations.clear();
                btnLoad.setClickable(true);
                btnLoad.setBackgroundResource(R.drawable.bg_main_information);
            }
        });

    }

    private MYOSETNativeListener myosetNativeListener = new MYOSETNativeListener();

    class MYOSETNativeListener implements OSETNativeListener {
        @Override
        public void onClick(View view) {
            Toast.makeText(activity, "onClick", Toast.LENGTH_SHORT).show();

        }

        @Override
        public void onClose(View view) {
            ll_information.removeAllViews();
            Toast.makeText(activity, "onClose", Toast.LENGTH_SHORT).show();

        }

        @Override
        public void onError(String s, String s1) {
            Toast.makeText(activity, "onError", Toast.LENGTH_SHORT).show();
            btnLoad.setClickable(true);
            btnLoad.setBackgroundResource(R.drawable.bg_main_information);

        }

        @Override
        public void onLoad(View view) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {

                    Toast.makeText(activity, "onLoad", Toast.LENGTH_SHORT).show();

                    nativeView = view;
                    btnShow.setClickable(true);
                    btnShow.setBackgroundResource(R.drawable.bg_main_information);
                }
            });
        }

        @Override
        public void onRenderSuccess(View view) {

        }

        @Override
        public void onRenderFail(View view) {

        }

        @Override
        public void onShow(View view) {
            Toast.makeText(activity, "onShow", Toast.LENGTH_SHORT).show();

        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }
}
