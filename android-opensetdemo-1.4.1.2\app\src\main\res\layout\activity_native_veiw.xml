<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/include_title" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <Button
                android:id="@+id/btn_load"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="20dp"
                android:background="@drawable/bg_main_turntable"
                android:gravity="center"
                android:text="加载自渲染"
                android:textColor="@android:color/white" />

            <Button
                android:id="@+id/btn_show"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="20dp"
                android:background="@drawable/bg_btn_gray"
                android:gravity="center"
                android:text="显示自渲染"
                android:textColor="@android:color/white" />

            <Button
                android:id="@+id/btn_show_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="20dp"
                android:background="@drawable/bg_btn_gray"
                android:gravity="center"
                android:text="在列表中显示自渲染"
                android:textColor="@android:color/white" />

            <LinearLayout
                android:id="@+id/ll_information"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

            </LinearLayout>
        </LinearLayout>

        </ScrollView>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/information_recyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        />

</LinearLayout>