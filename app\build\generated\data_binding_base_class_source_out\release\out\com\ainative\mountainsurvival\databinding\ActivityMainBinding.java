// Generated by view binder compiler. Do not edit!
package com.ainative.mountainsurvival.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.ainative.mountainsurvival.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button choice1Button;

  @NonNull
  public final Button choice2Button;

  @NonNull
  public final Button choice3Button;

  @NonNull
  public final Button choice4Button;

  @NonNull
  public final LinearLayout choicesLayout;

  @NonNull
  public final TextView firewoodPlusButton;

  @NonNull
  public final TextView firewoodTextView;

  @NonNull
  public final TextView foodPlusButton;

  @NonNull
  public final TextView foodTextView;

  @NonNull
  public final TextView staminaPlusButton;

  @NonNull
  public final TextView staminaTextView;

  @NonNull
  public final LinearLayout statusBarLayout;

  @NonNull
  public final ScrollView storyScrollView;

  @NonNull
  public final TextView storyTextView;

  @NonNull
  public final TextView warmthPlusButton;

  @NonNull
  public final TextView warmthTextView;

  private ActivityMainBinding(@NonNull ConstraintLayout rootView, @NonNull Button choice1Button,
      @NonNull Button choice2Button, @NonNull Button choice3Button, @NonNull Button choice4Button,
      @NonNull LinearLayout choicesLayout, @NonNull TextView firewoodPlusButton,
      @NonNull TextView firewoodTextView, @NonNull TextView foodPlusButton,
      @NonNull TextView foodTextView, @NonNull TextView staminaPlusButton,
      @NonNull TextView staminaTextView, @NonNull LinearLayout statusBarLayout,
      @NonNull ScrollView storyScrollView, @NonNull TextView storyTextView,
      @NonNull TextView warmthPlusButton, @NonNull TextView warmthTextView) {
    this.rootView = rootView;
    this.choice1Button = choice1Button;
    this.choice2Button = choice2Button;
    this.choice3Button = choice3Button;
    this.choice4Button = choice4Button;
    this.choicesLayout = choicesLayout;
    this.firewoodPlusButton = firewoodPlusButton;
    this.firewoodTextView = firewoodTextView;
    this.foodPlusButton = foodPlusButton;
    this.foodTextView = foodTextView;
    this.staminaPlusButton = staminaPlusButton;
    this.staminaTextView = staminaTextView;
    this.statusBarLayout = statusBarLayout;
    this.storyScrollView = storyScrollView;
    this.storyTextView = storyTextView;
    this.warmthPlusButton = warmthPlusButton;
    this.warmthTextView = warmthTextView;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.choice1Button;
      Button choice1Button = ViewBindings.findChildViewById(rootView, id);
      if (choice1Button == null) {
        break missingId;
      }

      id = R.id.choice2Button;
      Button choice2Button = ViewBindings.findChildViewById(rootView, id);
      if (choice2Button == null) {
        break missingId;
      }

      id = R.id.choice3Button;
      Button choice3Button = ViewBindings.findChildViewById(rootView, id);
      if (choice3Button == null) {
        break missingId;
      }

      id = R.id.choice4Button;
      Button choice4Button = ViewBindings.findChildViewById(rootView, id);
      if (choice4Button == null) {
        break missingId;
      }

      id = R.id.choicesLayout;
      LinearLayout choicesLayout = ViewBindings.findChildViewById(rootView, id);
      if (choicesLayout == null) {
        break missingId;
      }

      id = R.id.firewoodPlusButton;
      TextView firewoodPlusButton = ViewBindings.findChildViewById(rootView, id);
      if (firewoodPlusButton == null) {
        break missingId;
      }

      id = R.id.firewoodTextView;
      TextView firewoodTextView = ViewBindings.findChildViewById(rootView, id);
      if (firewoodTextView == null) {
        break missingId;
      }

      id = R.id.foodPlusButton;
      TextView foodPlusButton = ViewBindings.findChildViewById(rootView, id);
      if (foodPlusButton == null) {
        break missingId;
      }

      id = R.id.foodTextView;
      TextView foodTextView = ViewBindings.findChildViewById(rootView, id);
      if (foodTextView == null) {
        break missingId;
      }

      id = R.id.staminaPlusButton;
      TextView staminaPlusButton = ViewBindings.findChildViewById(rootView, id);
      if (staminaPlusButton == null) {
        break missingId;
      }

      id = R.id.staminaTextView;
      TextView staminaTextView = ViewBindings.findChildViewById(rootView, id);
      if (staminaTextView == null) {
        break missingId;
      }

      id = R.id.statusBarLayout;
      LinearLayout statusBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (statusBarLayout == null) {
        break missingId;
      }

      id = R.id.storyScrollView;
      ScrollView storyScrollView = ViewBindings.findChildViewById(rootView, id);
      if (storyScrollView == null) {
        break missingId;
      }

      id = R.id.storyTextView;
      TextView storyTextView = ViewBindings.findChildViewById(rootView, id);
      if (storyTextView == null) {
        break missingId;
      }

      id = R.id.warmthPlusButton;
      TextView warmthPlusButton = ViewBindings.findChildViewById(rootView, id);
      if (warmthPlusButton == null) {
        break missingId;
      }

      id = R.id.warmthTextView;
      TextView warmthTextView = ViewBindings.findChildViewById(rootView, id);
      if (warmthTextView == null) {
        break missingId;
      }

      return new ActivityMainBinding((ConstraintLayout) rootView, choice1Button, choice2Button,
          choice3Button, choice4Button, choicesLayout, firewoodPlusButton, firewoodTextView,
          foodPlusButton, foodTextView, staminaPlusButton, staminaTextView, statusBarLayout,
          storyScrollView, storyTextView, warmthPlusButton, warmthTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
