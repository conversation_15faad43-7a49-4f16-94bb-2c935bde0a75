<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <!-- 背景图片 -->
    <ImageView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:src="@drawable/background2"
        android:scaleType="centerCrop"
        android:contentDescription="游戏背景"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 虚化遮罩层 -->
    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/game_overlay"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 顶部状态栏 -->
    <LinearLayout
        android:id="@+id/statusBarLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:orientation="horizontal"
        android:weightSum="4"
        android:background="#60000000"
        android:padding="18dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 体温区域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center">

            <TextView
                android:id="@+id/warmthTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="🔥 80"
                android:textColor="#E67E22"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/warmthPlusButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:text="+"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:padding="4dp"
                android:clickable="true"
                android:focusable="true" />
        </LinearLayout>

        <!-- 体力区域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center">

            <TextView
                android:id="@+id/staminaTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="💪 100"
                android:textColor="#ECF0F1"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/staminaPlusButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:text="+"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:padding="4dp"
                android:clickable="true"
                android:focusable="true" />
        </LinearLayout>

        <!-- 木柴区域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center">

            <TextView
                android:id="@+id/firewoodTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="🪵 5"
                android:textColor="#ECF0F1"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/firewoodPlusButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:text="+"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:padding="4dp"
                android:clickable="true"
                android:focusable="true" />
        </LinearLayout>

        <!-- 食物区域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center">

            <TextView
                android:id="@+id/foodTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="🍖 3"
                android:textColor="#ECF0F1"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/foodPlusButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:text="+"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:padding="4dp"
                android:clickable="true"
                android:focusable="true" />
        </LinearLayout>

    </LinearLayout>

    <!-- 故事文本区 - 包装在ScrollView中支持滚动 -->
    <ScrollView
        android:id="@+id/storyScrollView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:background="#50000000"
        android:fillViewport="true"
        android:scrollbars="vertical"
        app:layout_constraintBottom_toTopOf="@+id/choicesLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/statusBarLayout">

        <TextView
            android:id="@+id/storyTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="top|start"
            android:lineSpacingExtra="4dp"
            android:padding="16dp"
            android:text="刺骨的寒风把你从昏迷中唤醒。你挣扎着睁开眼，发现自己正躺在一间破旧的小木屋里。屋外，暴风雪的呼啸声如同怪物的嘶吼。你检查了一下自己的状况，必须立刻想办法生火！"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:shadowColor="#000000"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="2" />

    </ScrollView>

    <!-- 选项按钮区 -->
    <LinearLayout
        android:id="@+id/choicesLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- 选项按钮1 -->
        <Button
            android:id="@+id/choice1Button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:background="@drawable/button_black_frame"
            android:padding="16dp"
            android:text="劈开旧家具生火"
            android:textColor="#ffffff"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- 选项按钮2 -->
        <Button
            android:id="@+id/choice2Button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:background="@drawable/button_black_frame"
            android:padding="16dp"
            android:text="冒着风雪出去找柴火"
            android:textColor="#ffffff"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- 选项按钮3 -->
        <Button
            android:id="@+id/choice3Button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:background="@drawable/button_black_frame"
            android:padding="16dp"
            android:text="休息以保存体力"
            android:textColor="#ffffff"
            android:textSize="16sp"
            android:textStyle="bold"
            android:visibility="gone" />

        <!-- 选项按钮4 -->
        <Button
            android:id="@+id/choice4Button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/button_black_frame"
            android:padding="16dp"
            android:text="制作简易工具"
            android:textColor="#ffffff"
            android:textSize="16sp"
            android:textStyle="bold"
            android:visibility="gone" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
