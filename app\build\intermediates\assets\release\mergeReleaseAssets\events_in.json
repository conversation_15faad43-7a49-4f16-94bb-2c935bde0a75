{"events": [{"id": "day1_start", "text": "<PERSON><PERSON> yang menusuk tulang membangunkan Anda dari ketidak<PERSON>aran. <PERSON>gan susah payah membuka mata, <PERSON><PERSON> mendapati diri berbaring di sebuah pondok kayu yang lapuk. <PERSON> luar, suara badai salju mengaum seperti raungan monster. Anda memeriksa kondisi diri - harus segera menemukan cara untuk membuat api!", "choices": [{"text": "Hancurkan furnitur lama untuk membuat api", "effects": {"stamina": -15, "firewood": 6, "cabin_integrity": -8}, "resultText": "Mengg<PERSON>kan kapak di sudut, <PERSON><PERSON>n kursi yang goyah. Akhirnya ada tanda-tanda kehangatan di pondok yang dingin. Meskipun merusak furnitur, sekarang bertahan hidup lebih penting.", "nextEventId": "day1_evening"}, {"text": "<PERSON><PERSON> mencari kayu bakar di tengah badai salju", "effects": {"warmth": -25, "stamina": -25, "firewood": 10}, "resultText": "Mendor<PERSON> pintu terbuka, <PERSON><PERSON> hamp<PERSON> ter<PERSON><PERSON>h oleh angin dan salju. <PERSON><PERSON> putus asa mengumpulkan ranting kering di salju setinggi lutut, jari-jari beku kehilangan semua rasa. <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> mengumpulkan lebih banyak kayu bakar.", "nextEventId": "day1_evening"}, {"text": "Cari sumber panas lain", "effects": {"stamina": -10, "hope": -5}, "resultText": "<PERSON>a mencari sumber panas lain di sekitar pondok, tetapi tidak menemukan apa-apa selain perapian. Ini agak men<PERSON>, tetapi setidaknya tidak membuang terlalu banyak energi.", "nextEventId": "day1_evening"}]}, {"id": "day1_evening", "text": "<PERSON><PERSON> senja, api oranye menari di perapian. <PERSON><PERSON>a <PERSON>, tetapi setidaknya sekarang ada kehangatan. <PERSON><PERSON>, dan <PERSON>a perlu bersiap untuk kegelapan panjang di depan.", "choices": [{"text": "<PERSON><PERSON><PERSON> rumah melawan angin dan salju", "effects": {"stamina": -20, "firewood": -5, "cabin_integrity": 20}, "requirements": {"firewood": 5, "stamina": 20}, "resultText": "Menggunakan kayu bakar untuk menutup celah jendela dan memperkuat pintu. <PERSON><PERSON><PERSON><PERSON><PERSON> ini mele<PERSON>kan, tetapi pondok menjadi lebih kokoh dan dapat menahan dingin malam dengan lebih baik.", "nextEventId": "day1_night"}, {"text": "<PERSON><PERSON> ma<PERSON>an", "effects": {"stamina": -15, "food": 3}, "resultText": "<PERSON><PERSON><PERSON><PERSON><PERSON> pondok dengan hati-hati, <PERSON><PERSON>n beberapa kaleng makanan di belakang rak. Sudah kedaluwarsa tetapi masih terlihat bisa dimakan. Makanan ini akan membantu bertahan beberapa hari.", "nextEventId": "day1_night"}, {"text": "Is<PERSON><PERSON><PERSON> lebih awal", "effects": {"stamina": 10, "hope": 5}, "resultText": "Memutuskan untuk beristirahat dekat perapian. Kehangatan api membungkus tubuh dan memulihkan sedikit semangat. <PERSON>a ber<PERSON><PERSON> menyimpan energi untuk hari esok.", "nextEventId": "day1_night"}]}]}