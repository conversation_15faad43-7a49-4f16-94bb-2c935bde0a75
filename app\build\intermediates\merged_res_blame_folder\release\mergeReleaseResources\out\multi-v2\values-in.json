{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-mergeReleaseResources-30:/values-in/values-in.xml", "map": [{"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-in\\strings.xml", "from": {"startLines": "97,96,93,92,110,99,98,1,2,60,80,59,81,113,56,154,107,47,106,140,141,139,137,136,144,143,134,133,148,69,71,79,46,45,6,54,55,68,67,44,11,82,63,61,64,62,9,32,17,28,29,25,21,31,19,20,26,23,30,15,16,27,22,18,24,112,83,8,7,86,157,158,75,162,78,161,159,160,76,77,48,151,39,37,40,38,36,35,41,153,152,88,111,100,95,94,101,89,105,104,103,102,70,72,87,53,5,51,12,10,129,130,128,117,118,116,147,121,122,120,125,126,124,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7716,7648,7384,7326,8758,7902,7829,16,75,4879,6523,4809,6622,8943,4687,14971,8691,4244,8634,13614,13982,13499,12973,12861,14207,14105,12352,12239,14574,5556,5686,6451,4192,4116,208,4588,4641,5337,5273,4016,450,6713,5095,4948,5170,5023,358,1808,991,1596,1649,1436,1204,1754,1106,1156,1486,1319,1701,864,930,1543,1269,1044,1377,8882,6800,308,258,6910,15084,15189,5965,15577,6387,15475,15309,15395,6028,6202,4298,14795,3753,2053,3809,3660,1979,1890,3874,14900,14836,7102,8814,8025,7545,7468,8157,7162,8501,8430,8357,8299,5613,5835,7030,4539,158,4418,751,402,11676,12101,11538,9183,9880,9030,14350,10162,10653,10020,10940,11388,10787,4488", "endColumns": "112,67,83,57,55,122,72,58,56,68,98,69,90,56,86,67,38,53,56,367,121,114,524,111,108,101,507,112,184,56,148,71,51,75,49,52,45,218,63,99,300,86,74,74,73,71,43,49,52,52,51,49,64,53,49,47,56,57,52,65,60,52,49,61,58,60,81,49,49,119,104,119,62,147,63,101,85,79,173,184,84,40,55,1606,64,92,73,88,109,70,63,59,67,131,102,76,141,131,132,70,72,57,72,100,71,48,49,69,81,47,424,108,137,696,138,152,223,490,132,141,447,148,152,50", "endOffsets": "7824,7711,7463,7379,8809,8020,7897,70,127,4943,6617,4874,6708,8995,4769,15034,8725,4293,8686,13977,14099,13609,13493,12968,14311,14202,12855,12347,14754,5608,5830,6518,4239,4187,253,4636,4682,5551,5332,4111,746,6795,5165,5018,5239,5090,397,1853,1039,1644,1696,1481,1264,1803,1151,1199,1538,1372,1749,925,986,1591,1314,1101,1431,8938,6877,353,303,7025,15184,15304,6023,15720,6446,15572,15390,15470,6197,6382,4378,14831,3804,3655,3869,3748,2048,1974,3979,14966,14895,7157,8877,8152,7643,7540,8294,7289,8629,8496,8425,8352,5681,5931,7097,4583,203,4483,828,445,12096,12205,11671,9875,10014,9178,14569,10648,10781,10157,11383,11532,10935,4534"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,47,48,49,50,51,54,55,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,74,75,76,78,79,80,81,82,83,84,85,86,87,88,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,200,201,202,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3023,3136,3204,3288,3346,3402,3525,3598,3657,4123,4192,4291,4361,4452,4731,4818,4969,5008,5062,5119,5487,5609,5724,6249,6361,6470,6572,7080,7193,7378,7435,7584,7721,7773,7849,7993,8046,8092,8311,8375,8475,8776,8863,8938,9013,9087,9283,9327,9377,9430,9483,9535,9585,9650,9704,9754,9802,9859,9917,9970,10036,10097,10150,10200,10262,10321,10382,14251,14301,14351,14471,14576,14696,14759,14907,14971,15073,15159,15239,15413,15598,15683,15804,15860,17467,17532,17625,17699,17788,17898,17969,18033,18093,18161,18293,18396,18473,18615,18747,18880,18951,19024,19082,19155,19341,19413,19462,19613,19683,19765,19813,20238,20347,20485,21182,21321,21474,21698,22189,22322,22464,22912,23061,23214", "endColumns": "112,67,83,57,55,122,72,58,56,68,98,69,90,56,86,67,38,53,56,367,121,114,524,111,108,101,507,112,184,56,148,71,51,75,49,52,45,218,63,99,300,86,74,74,73,71,43,49,52,52,51,49,64,53,49,47,56,57,52,65,60,52,49,61,58,60,81,49,49,119,104,119,62,147,63,101,85,79,173,184,84,40,55,1606,64,92,73,88,109,70,63,59,67,131,102,76,141,131,132,70,72,57,72,100,71,48,49,69,81,47,424,108,137,696,138,152,223,490,132,141,447,148,152,50", "endOffsets": "3131,3199,3283,3341,3397,3520,3593,3652,3709,4187,4286,4356,4447,4504,4813,4881,5003,5057,5114,5482,5604,5719,6244,6356,6465,6567,7075,7188,7373,7430,7579,7651,7768,7844,7894,8041,8087,8306,8370,8470,8771,8858,8933,9008,9082,9154,9322,9372,9425,9478,9530,9580,9645,9699,9749,9797,9854,9912,9965,10031,10092,10145,10195,10257,10316,10377,10459,14296,14346,14466,14571,14691,14754,14902,14966,15068,15154,15234,15408,15593,15678,15719,15855,17462,17527,17620,17694,17783,17893,17964,18028,18088,18156,18288,18391,18468,18610,18742,18875,18946,19019,19077,19150,19251,19408,19457,19507,19678,19760,19808,20233,20342,20480,21177,21316,21469,21693,22184,22317,22459,22907,23056,23209,23260"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bde3453fac62cb9a4dc37198ed238829\\transformed\\appcompat-1.6.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1920,2023,2128,2229,2333,2442,2550,2710,2809", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,424,511,615,731,814,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1915,2018,2123,2224,2328,2437,2545,2705,2804,2889"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,434,538,643,730,834,950,1033,1111,1202,1295,1390,1484,1584,1677,1772,1866,1957,2048,2134,2237,2342,2443,2547,2656,2764,2924,19256", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "429,533,638,725,829,945,1028,1106,1197,1290,1385,1479,1579,1672,1767,1861,1952,2043,2129,2232,2337,2438,2542,2651,2759,2919,3018,19336"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5d5fa9c9e36bc912c1cb51114343e1e1\\transformed\\material-1.9.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,348,424,503,593,678,784,900,983,1048,1142,1207,1266,1353,1415,1477,1537,1603,1665,1719,1831,1888,1949,2003,2075,2201,2287,2371,2510,2591,2672,2762,2815,2867,2933,3005,3089,3172,3247,3323,3396,3471,3556,3631,3723,3817,3891,3964,4058,4110,4179,4264,4351,4413,4477,4540,4643,4743,4838,4940,4997,5053", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,78,75,78,89,84,105,115,82,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,83,138,80,80,89,52,51,65,71,83,82,74,75,72,74,84,74,91,93,73,72,93,51,68,84,86,61,63,62,102,99,94,101,56,55,79", "endOffsets": "264,343,419,498,588,673,779,895,978,1043,1137,1202,1261,1348,1410,1472,1532,1598,1660,1714,1826,1883,1944,1998,2070,2196,2282,2366,2505,2586,2667,2757,2810,2862,2928,3000,3084,3167,3242,3318,3391,3466,3551,3626,3718,3812,3886,3959,4053,4105,4174,4259,4346,4408,4472,4535,4638,4738,4833,4935,4992,5048,5128"}, "to": {"startLines": "2,42,43,44,45,46,52,53,56,73,77,89,90,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3714,3793,3869,3948,4038,4509,4615,4886,7656,7899,9159,9224,10464,10551,10613,10675,10735,10801,10863,10917,11029,11086,11147,11201,11273,11399,11485,11569,11708,11789,11870,11960,12013,12065,12131,12203,12287,12370,12445,12521,12594,12669,12754,12829,12921,13015,13089,13162,13256,13308,13377,13462,13549,13611,13675,13738,13841,13941,14036,14138,14195,15724", "endLines": "5,42,43,44,45,46,52,53,56,73,77,89,90,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,176", "endColumns": "12,78,75,78,89,84,105,115,82,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,83,138,80,80,89,52,51,65,71,83,82,74,75,72,74,84,74,91,93,73,72,93,51,68,84,86,61,63,62,102,99,94,101,56,55,79", "endOffsets": "314,3788,3864,3943,4033,4118,4610,4726,4964,7716,7988,9219,9278,10546,10608,10670,10730,10796,10858,10912,11024,11081,11142,11196,11268,11394,11480,11564,11703,11784,11865,11955,12008,12060,12126,12198,12282,12365,12440,12516,12589,12664,12749,12824,12916,13010,13084,13157,13251,13303,13372,13457,13544,13606,13670,13733,13836,13936,14031,14133,14190,14246,15799"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b7d68e85c64c5740c775d2c6236c08f5\\transformed\\core-1.9.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "203", "startColumns": "4", "startOffsets": "19512", "endColumns": "100", "endOffsets": "19608"}}]}]}