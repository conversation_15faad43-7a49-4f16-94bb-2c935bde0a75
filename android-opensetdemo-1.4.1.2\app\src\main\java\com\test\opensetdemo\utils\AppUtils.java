package com.test.opensetdemo.utils;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;


import androidx.core.content.FileProvider;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

public class AppUtils {

    public static int getVersionNo(Context c) {
        PackageManager manager = c.getPackageManager();
        PackageInfo info = null;
        try {
            info = manager.getPackageInfo(c.getPackageName(), 0);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
            return -1;
        }
        return info.versionCode;
    }

    /**
     * 下载
     *
     * @param context
     */
    public static void downloadFile(final Context context, VersionBean.Data data, final DownLoadListener listener) {
        String savePath = Environment.getExternalStorageDirectory().getAbsolutePath(); //储存下载文件的目录
        File filedir = new File(savePath);
        if (!filedir.exists()) {
            filedir.mkdir();
        }
        final File file = new File(savePath, "adsetDemo.apk");
        if (file.exists()) {
            file.delete(); // 覆盖（删除原先的文件）
        }
        OkHttpClient okHttpClient = new OkHttpClient();
        Request request = new Request.Builder()
                .url(data.getDemo_info())
                .addHeader("Connection", "close")
                .build();
        okHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                listener.downError();
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                InputStream is = null;
                byte[] buf = new byte[2048];
                int len = 0;
                FileOutputStream fos = null;
                try {
                    is = response.body().byteStream();
                    long total = response.body().contentLength();
                    fos = new FileOutputStream(file);
                    long sum = 0;
                    while ((len = is.read(buf)) != -1) {
                        fos.write(buf, 0, len);
                        sum += len;
                        int progress = (int) (sum * 1.0f / total * 100);
                        listener.progress(progress);
                    }
                    fos.flush();
                    fos.close();
                    listener.downSuccess();

                } catch (Exception e) {
                    listener.downError();
                } finally {
                    try {
                        if (is != null) {
                            is.close();
                        }
                    } catch (Exception e) {
                    }
                    try {
                        if (fos != null) {
                            fos.close();
                        }
                    } catch (Exception e) {
                    }
                }

            }
        });
    }


    public static long getFileLenght(String url) throws IOException {
        long lenght = 0;
//URL mUrl = new URL(urlString);
        URL mUrl = new URL(url);
        HttpURLConnection conn = (HttpURLConnection) mUrl.openConnection();
        conn.setConnectTimeout(5 * 1000);
        conn.setRequestMethod("GET");
        conn.setRequestProperty("Accept-Encoding", "identity");
        conn.setRequestProperty("Referer", url);
//conn.setRequestProperty("Referer", urlString);
        conn.setRequestProperty("Charset", "UTF-8");
        conn.setRequestProperty("Connection", "Keep-Alive");
        conn.connect();

        int responseCode = conn.getResponseCode();
//// 判断请求是否成功处理
//        if (responseCode == HttpStatus.SC_OK) {
//            lenght = conn.getContentLength();
//        }

        return lenght;
    }

    /*
     * 下载到本地后执行安装
     */
    public static void installAPK(Context context) {
        File apkFile = new File(Environment.getExternalStorageDirectory().getAbsolutePath(), "adsetDemo.apk");
        Intent intent = new Intent(Intent.ACTION_VIEW);
//      安装完成后，启动app（源码中少了这句话）
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            Uri uri = FileProvider.getUriForFile(context, context.getPackageName() + ".demofileprovider", apkFile);
            intent.setDataAndType(uri, "application/vnd.android.package-archive");
        } else {
            Uri uri = Uri.parse("file://" + apkFile.getPath());
            intent.setDataAndType(uri, "application/vnd.android.package-archive");
        }
        context.startActivity(intent);
    }

}
