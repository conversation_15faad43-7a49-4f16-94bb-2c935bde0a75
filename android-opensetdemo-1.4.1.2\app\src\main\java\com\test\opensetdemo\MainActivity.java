package com.test.opensetdemo;


import static android.Manifest.permission.ACCESS_FINE_LOCATION;
import static android.Manifest.permission.READ_PHONE_STATE;
import static android.Manifest.permission.WRITE_EXTERNAL_STORAGE;
import static android.content.pm.PackageManager.PERMISSION_GRANTED;

import android.app.Activity;
import android.app.Fragment;
import android.app.FragmentTransaction;
import android.os.Build;
import android.os.Bundle;
import android.widget.RadioButton;

import com.test.opensetdemo.fragment.AdFragment;
import com.test.opensetdemo.fragment.BaseFragment;
import com.test.opensetdemo.utils.VersionBean;

import java.util.ArrayList;
import java.util.List;


public class MainActivity extends Activity {

    private Activity activity;
    private VersionBean bean;
    private RadioButton rbLive;

    private List<BaseFragment> fragments = new ArrayList<>();


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        activity = this;
        requestPermissions();//权限申请
//        getVserion();
        initFragment();
        changeFragment(0);
    }

    /**
     * 加载fragment
     */
    private void initFragment() {
        fragments.clear();
        fragments.add(new AdFragment());
    }

    /**
     * 更改显示的fragment
     *
     * @param index
     */
    public void changeFragment(int index) {

        //隐藏activity的fragment栈中的所有Fragment
        FragmentTransaction ft = getFragmentManager().beginTransaction();
        for (Fragment f : fragments) {
            if (f.isAdded()) {
                ft.hide(f);
            }
        }
        ft.commit();

        //1.判断要显示的Fragment是否已经在fragment栈中添加过了，如果没有则添加进去；
        //2.如果已经添加则通过TAG获取并且到该Fragment的引用，将它显示
        if (!fragments.get(index).isAdded()) {
            getFragmentManager().beginTransaction()
                    .add(R.id.fl, fragments.get(index), "" + index)
                    .commit();
        } else {
            Fragment fragment = getFragmentManager().findFragmentByTag("" + index);
            FragmentTransaction transaction = getFragmentManager().beginTransaction();
            transaction.show(fragment);
            transaction.commit();
        }
    }


    private void requestPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {

            if (checkSelfPermission(READ_PHONE_STATE) != PERMISSION_GRANTED
                    || checkSelfPermission(ACCESS_FINE_LOCATION) != PERMISSION_GRANTED
                    || checkSelfPermission(WRITE_EXTERNAL_STORAGE) != PERMISSION_GRANTED) {
                requestPermissions(new String[]{READ_PHONE_STATE, WRITE_EXTERNAL_STORAGE,
                        ACCESS_FINE_LOCATION}, PERMISSION_GRANTED);
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }
}
