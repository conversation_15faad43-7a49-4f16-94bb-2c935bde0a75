# PowerShell脚本：批量修复strings.xml中的格式化占位符
# 修复 "Multiple substitutions" 警告

$valuesDirectories = @(
    "app\src\main\res\values-es",
    "app\src\main\res\values-fr", 
    "app\src\main\res\values-hi",
    "app\src\main\res\values-in",
    "app\src\main\res\values-it",
    "app\src\main\res\values-ja",
    "app\src\main\res\values-ko",
    "app\src\main\res\values-ms",
    "app\src\main\res\values-pt",
    "app\src\main\res\values-ru",
    "app\src\main\res\values-th",
    "app\src\main\res\values-vi",
    "app\src\main\res\values-zh-rTW"
)

# 需要修复的字符串模式
$patterns = @{
    # night_with_firewood: 3个%d占位符
    'night_with_firewood' = @{
        'pattern' = '(%d.*?%d.*?%d)'
        'replacement' = { param($match) 
            $match -replace '%d', '%1$d' -replace '%1\$d(.*?)%1\$d', '%1$d$1%2$d' -replace '%2\$d(.*?)%1\$d', '%2$d$1%3$d'
        }
    }
    
    # night_without_firewood: 2个%d占位符  
    'night_without_firewood' = @{
        'pattern' = '(%d.*?%d)'
        'replacement' = { param($match)
            $match -replace '%d', '%1$d' -replace '%1\$d(.*?)%1\$d', '%1$d$1%2$d'
        }
    }
    
    # reward_message: %s和%d占位符
    'reward_message' = @{
        'pattern' = '(%s.*?%d)'
        'replacement' = { param($match)
            $match -replace '%s', '%1$s' -replace '%d', '%2$d'
        }
    }
    
    # victory_stats_format: 5个%d占位符
    'victory_stats_format' = @{
        'pattern' = '(%d.*?%d.*?%d.*?%d.*?%d)'
        'replacement' = { param($match)
            $count = 1
            $result = $match
            while ($result -match '%d' -and $count -le 5) {
                $result = $result -replace '%d', "%$count`$d", 1
                $count++
            }
            $result
        }
    }
    
    # defeat_stats_format: 5个%d占位符
    'defeat_stats_format' = @{
        'pattern' = '(%d.*?%d.*?%d.*?%d.*?%d)'
        'replacement' = { param($match)
            $count = 1
            $result = $match
            while ($result -match '%d' -and $count -le 5) {
                $result = $result -replace '%d', "%$count`$d", 1
                $count++
            }
            $result
        }
    }
}

foreach ($dir in $valuesDirectories) {
    $stringsFile = Join-Path $dir "strings.xml"
    
    if (Test-Path $stringsFile) {
        Write-Host "Processing: $stringsFile"
        
        $content = Get-Content $stringsFile -Raw -Encoding UTF8
        $modified = $false
        
        # 修复每种模式
        foreach ($stringName in $patterns.Keys) {
            $pattern = $patterns[$stringName]
            
            # 查找包含该字符串名称的行
            if ($content -match "<string name=`"$stringName`"[^>]*>.*?</string>") {
                $stringMatch = $matches[0]
                
                # 检查是否包含多个占位符
                if ($stringMatch -match $pattern.pattern) {
                    $oldPlaceholders = $matches[1]
                    $newPlaceholders = & $pattern.replacement $oldPlaceholders
                    
                    if ($oldPlaceholders -ne $newPlaceholders) {
                        $newStringMatch = $stringMatch -replace [regex]::Escape($oldPlaceholders), $newPlaceholders
                        $content = $content -replace [regex]::Escape($stringMatch), $newStringMatch
                        $modified = $true
                        Write-Host "  Fixed: $stringName"
                    }
                }
            }
        }
        
        # 保存修改后的文件
        if ($modified) {
            $content | Set-Content $stringsFile -Encoding UTF8 -NoNewline
            Write-Host "  Saved: $stringsFile"
        } else {
            Write-Host "  No changes needed: $stringsFile"
        }
    } else {
        Write-Host "File not found: $stringsFile"
    }
}

Write-Host "String format fixing completed!"
