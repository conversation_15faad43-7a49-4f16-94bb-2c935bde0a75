package com.test.opensetdemo.ad;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.widget.FrameLayout;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.kc.openset.ad.listener.OSETAppOpenListener;
import com.kc.openset.ad.splash.OSETAppOpen;
import com.kc.openset.config.OSETSDK;
import com.kc.openset.listener.OSETInitListener;
import com.test.opensetdemo.Common;
import com.test.opensetdemo.MainActivity;
import com.test.opensetdemo.R;


public class AppOpenActivity extends AppCompatActivity {
    private static final String TAG = "AdsetSplashActivity";

    private FrameLayout fl;
    private Activity activity;
    private boolean isOnPause = false;//判断是否跳转了广告落地页
    private boolean isClick = false;//是否进行了点击
    private boolean isClose = false;//是否回调了Close

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_welcome);
        activity = this;
        fl = findViewById(R.id.fl);
        if (OSETSDK.getInstance().isInit()) {
            showAd();
            return;
        }

        OSETSDK.getInstance().init(getApplication(), Common.AppKey, new OSETInitListener() {
            @Override
            public void onError(String s) {
                //初始化失败：会调用不到广告，清选择合适的时机重新进行初始化
                startActivity(new Intent(activity, MainActivity.class));
                finish();
            }

            @Override
            public void onSuccess() {
                showAd();
                //初始化成功：可以开始调用广告
            }
        });

    }

    private void showAd() {

        OSETAppOpen.getInstance().setPosId(Common.POS_ID_Splash).show(this, fl, new OSETAppOpenListener() {
            @Override
            public void onShow() {
                Log.e(TAG, "onShow");
            }

            @Override
            public void onError(String s, String s1) {
                Log.e(TAG, "onError——————code:" + s + "----message:" + s1);
                startActivity(new Intent(activity, MainActivity.class));
                finish();
            }

            @Override
            public void onClick() {
                isClick = true;
                Log.e(TAG, "onClick");
            }

            @Override
            public void onClose() {
                Log.e(TAG, "onclose +isOnPause=" + isOnPause + "isClick=" + isClick);
                isClose = true;
                if (!isOnPause && !isClick) {//如果已经调用了onPause说明已经跳转了广告落地页
                    startActivity(new Intent(activity, MainActivity.class));
                    finish();
                }
            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        Log.e(TAG, "onResume");
        if (isOnPause && isClose) {//判断是否点击，并且跳转了落地页，如果是，就相当于走了onclose
            startActivity(new Intent(activity, MainActivity.class));
            finish();
        } else {
            isClick = false;
            isOnPause = false;
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        Log.e(TAG, "onPause");
        if (isClick) {
            isOnPause = true;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }
}
