[{"merged": "com.ainative.mountainsurvival.app-release-32:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.ainative.mountainsurvival.app-main-33:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "com.ainative.mountainsurvival.app-main-33:/mipmap-xhdpi/ic_launcher_foreground.webp"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.ainative.mountainsurvival.app-main-33:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/xml_oset_file_paths.xml.flat", "source": "com.ainative.mountainsurvival.app-main-33:/xml/oset_file_paths.xml"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.ainative.mountainsurvival.app-main-33:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.ainative.mountainsurvival.app-main-33:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/drawable_ic_launcher_foreground.xml.flat", "source": "com.ainative.mountainsurvival.app-main-33:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/drawable_game_overlay.xml.flat", "source": "com.ainative.mountainsurvival.app-main-33:/drawable/game_overlay.xml"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/xml_backup_rules.xml.flat", "source": "com.ainative.mountainsurvival.app-main-33:/xml/backup_rules.xml"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/drawable_button_background_gray.xml.flat", "source": "com.ainative.mountainsurvival.app-main-33:/drawable/button_background_gray.xml"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/drawable_button_background_black.xml.flat", "source": "com.ainative.mountainsurvival.app-main-33:/drawable/button_background_black.xml"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/xml_file_paths.xml.flat", "source": "com.ainative.mountainsurvival.app-main-33:/xml/file_paths.xml"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/drawable_title.png.flat", "source": "com.ainative.mountainsurvival.app-main-33:/drawable/title.png"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/drawable_button_background_secondary.xml.flat", "source": "com.ainative.mountainsurvival.app-main-33:/drawable/button_background_secondary.xml"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/drawable_button_background.xml.flat", "source": "com.ainative.mountainsurvival.app-main-33:/drawable/button_background.xml"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "com.ainative.mountainsurvival.app-main-33:/mipmap-mdpi/ic_launcher_foreground.webp"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "com.ainative.mountainsurvival.app-main-33:/mipmap-hdpi/ic_launcher_foreground.webp"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "com.ainative.mountainsurvival.app-main-33:/mipmap-xxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/drawable_background2.webp.flat", "source": "com.ainative.mountainsurvival.app-main-33:/drawable/background2.webp"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.ainative.mountainsurvival.app-main-33:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/xml_network_security_config.xml.flat", "source": "com.ainative.mountainsurvival.app-main-33:/xml/network_security_config.xml"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/drawable_ic_launcher_background.xml.flat", "source": "com.ainative.mountainsurvival.app-main-33:/drawable/ic_launcher_background.xml"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/drawable_button_black_frame.xml.flat", "source": "com.ainative.mountainsurvival.app-main-33:/drawable/button_black_frame.xml"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/layout_activity_main.xml.flat", "source": "com.ainative.mountainsurvival.app-main-33:/layout/activity_main.xml"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/drawable_title_en.png.flat", "source": "com.ainative.mountainsurvival.app-main-33:/drawable/title_en.png"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.ainative.mountainsurvival.app-main-33:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.ainative.mountainsurvival.app-main-33:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "com.ainative.mountainsurvival.app-main-33:/mipmap-xxxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/drawable_dialog_background.xml.flat", "source": "com.ainative.mountainsurvival.app-main-33:/drawable/dialog_background.xml"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.ainative.mountainsurvival.app-main-33:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.ainative.mountainsurvival.app-main-33:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/xml_data_extraction_rules.xml.flat", "source": "com.ainative.mountainsurvival.app-main-33:/xml/data_extraction_rules.xml"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/drawable_background1.webp.flat", "source": "com.ainative.mountainsurvival.app-main-33:/drawable/background1.webp"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/layout_activity_privacy_policy.xml.flat", "source": "com.ainative.mountainsurvival.app-main-33:/layout/activity_privacy_policy.xml"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/layout_activity_start.xml.flat", "source": "com.ainative.mountainsurvival.app-main-33:/layout/activity_start.xml"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/drawable_start_overlay.xml.flat", "source": "com.ainative.mountainsurvival.app-main-33:/drawable/start_overlay.xml"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.ainative.mountainsurvival.app-main-33:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.ainative.mountainsurvival.app-main-33:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/raw_winter.mp3.flat", "source": "com.ainative.mountainsurvival.app-main-33:/raw/winter.mp3"}, {"merged": "com.ainative.mountainsurvival.app-release-32:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.ainative.mountainsurvival.app-main-33:/mipmap-hdpi/ic_launcher_round.webp"}]