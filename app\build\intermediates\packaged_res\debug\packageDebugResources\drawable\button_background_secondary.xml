<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#5a2d2d" />
            <corners android:radius="8dp" />
            <stroke android:width="2dp" android:color="#e24a4a" />
        </shape>
    </item>
    
    <!-- 正常状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#8a4a4a" />
            <corners android:radius="8dp" />
            <stroke android:width="2dp" android:color="#ff6b6b" />
        </shape>
    </item>
</selector>
