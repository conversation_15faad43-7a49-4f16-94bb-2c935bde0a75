package com.test.opensetdemo;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import java.util.List;

public class MainAdapter extends BaseAdapter {

    private List<MainBean> data;
    private Context context;
    private GVItemClick click;

    public MainAdapter(Context context, List<MainBean> data, GVItemClick click) {
        this.context = context;
        this.data = data;
        this.click = click;
    }

    @Override
    public int getCount() {
        return data.size();
    }

    @Override
    public Object getItem(int position) {
        return null;
    }

    @Override
    public long getItemId(int position) {
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        convertView = LayoutInflater.from(context).inflate(R.layout.item_view, null);
        TextView tv = convertView.findViewById(R.id.tv_name);
        ImageView iv = convertView.findViewById(R.id.iv_item);
        LinearLayout ll = convertView.findViewById(R.id.ll_item);
        tv.setText(data.get(position).name);
        iv.setImageResource(data.get(position).imgId);
        ll.setBackgroundResource(data.get(position).bgImgId);
        ll.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                click.click(data.get(position).name);
            }
        });
        return convertView;
    }
}
