// Generated by view binder compiler. Do not edit!
package com.ainative.mountainsurvival.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.ainative.mountainsurvival.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityStartBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final Button exitGameButton;

  @NonNull
  public final Button languageButton;

  @NonNull
  public final Button musicToggleButton;

  @NonNull
  public final Button startGameButton;

  @NonNull
  public final ImageView titleImageView;

  private ActivityStartBinding(@NonNull FrameLayout rootView, @NonNull Button exitGameButton,
      @NonNull Button languageButton, @NonNull Button musicToggleButton,
      @NonNull Button startGameButton, @NonNull ImageView titleImageView) {
    this.rootView = rootView;
    this.exitGameButton = exitGameButton;
    this.languageButton = languageButton;
    this.musicToggleButton = musicToggleButton;
    this.startGameButton = startGameButton;
    this.titleImageView = titleImageView;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityStartBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityStartBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_start, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityStartBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.exitGameButton;
      Button exitGameButton = ViewBindings.findChildViewById(rootView, id);
      if (exitGameButton == null) {
        break missingId;
      }

      id = R.id.languageButton;
      Button languageButton = ViewBindings.findChildViewById(rootView, id);
      if (languageButton == null) {
        break missingId;
      }

      id = R.id.musicToggleButton;
      Button musicToggleButton = ViewBindings.findChildViewById(rootView, id);
      if (musicToggleButton == null) {
        break missingId;
      }

      id = R.id.startGameButton;
      Button startGameButton = ViewBindings.findChildViewById(rootView, id);
      if (startGameButton == null) {
        break missingId;
      }

      id = R.id.titleImageView;
      ImageView titleImageView = ViewBindings.findChildViewById(rootView, id);
      if (titleImageView == null) {
        break missingId;
      }

      return new ActivityStartBinding((FrameLayout) rootView, exitGameButton, languageButton,
          musicToggleButton, startGameButton, titleImageView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
